import React, { useState, useEffect } from 'react';
import { Box } from '@mui/material';
import { ThemeContextProvider } from './contexts/ThemeContext';
import ModernAuthForm from './components/auth/ModernAuthForm';
import OTPVerification from './components/auth/OTPVerification';
import Navbar from './components/layout/Navbar';
import Sidebar from './components/layout/Sidebar';
import Dashboard from './components/dashboard/Dashboard';
import ClientList from './components/clients/ClientList';
import NoticeList from './components/notices/NoticeList';
import ClientDetailModal from './components/clients/ClientDetailModal';
import RepliesManagement from './components/replies/RepliesManagement';
import SettingsPage from './components/settings/SettingsPage';
import RegistrationForm from './components/auth/RegistrationForm';
import { mockStore, mockQuery } from './data/gstTaxMockData';
import { realDataService } from './services/realDataService';
import { User, Client, Notice, NoticeSyncLog } from './types/schema';
import { NoticeStatus } from './types/enums';
import { taxPortalService } from './services/taxPortalService';
import { useDashboardStats, useClients, useNotices, useSyncStatus } from './hooks/useApi';

type AuthStep = 'login' | 'otp' | 'authenticated';
type ActiveRoute = '/dashboard' | '/clients' | '/notices' | '/replies' | '/settings';

const App: React.FC = () => {
  const [authStep, setAuthStep] = useState<AuthStep>('login');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [user, setUser] = useState<User | null>(null);
  const [activeRoute, setActiveRoute] = useState<ActiveRoute>('/dashboard');
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [clients, setClients] = useState<Client[]>(mockStore.clients);
  const [syncLogs, setSyncLogs] = useState<NoticeSyncLog[]>([]);
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);

  // API hooks for real backend data
  const { stats: dashboardStats, loading: statsLoading, error: statsError } = useDashboardStats();
  const { clients: apiClients, loading: clientsLoading, error: clientsError } = useClients();
  const { notices: apiNotices, loading: noticesLoading, error: noticesError } = useNotices();
  const { syncStatus, loading: syncLoading, triggerSync, syncAll } = useSyncStatus();

  const [notices, setNotices] = useState<Notice[]>(mockStore.notices);

  // Real data state
  const [realStats, setRealStats] = useState<any>(null);
  const [realChartData, setRealChartData] = useState<any[]>([]);
  const [realDataLoading, setRealDataLoading] = useState(false);

  // Load real data when user is authenticated
  useEffect(() => {
    if (user && activeRoute === '/dashboard') {
      loadRealData();
    }
  }, [user, activeRoute]);

  const loadRealData = async () => {
    setRealDataLoading(true);
    try {
      console.log('🔄 Loading real data from Whitebooks API...');

      // Load real dashboard stats
      const stats = await realDataService.getDashboardStats();
      setRealStats(stats);

      // Load real chart data
      const chartData = await realDataService.getNoticesByStatus();
      setRealChartData(chartData);

      // Load real clients and notices
      const realClients = await realDataService.getClients();
      const realNotices = await realDataService.getNotices();

      setClients(realClients);
      setNotices(realNotices);

      console.log('✅ Real data loaded successfully:', {
        stats,
        chartData,
        clients: realClients.length,
        notices: realNotices.length
      });
    } catch (error) {
      console.error('❌ Error loading real data:', error);
    } finally {
      setRealDataLoading(false);
    }
  };

  const handleLogin = (credentials: { email?: string; phone?: string; password: string }) => {
    // Simulate login with email/phone and password
    setUser(mockStore.user);
    setAuthStep('authenticated');
  };

  const handleRegister = (credentials: { email?: string; phone?: string; password: string; fullName?: string; companyName?: string }) => {
    // Simulate registration
    setUser(mockStore.user);
    setAuthStep('authenticated');
  };

  const handleGoogleLogin = () => {
    // Simulate Google login
    setUser(mockStore.user);
    setAuthStep('authenticated');
  };

  const handleForgotPassword = (email: string) => {
    // Simulate forgot password
    alert(`Password reset link sent to ${email}`);
  };

  const handleMobileLogin = (phone: string) => {
    setPhoneNumber(phone);
    setAuthStep('otp');
  };

  const handleOTPVerify = (otp: string) => {
    // Simulate OTP verification
    if (otp === '123456') {
      setUser(mockStore.user);
      setAuthStep('authenticated');
    } else {
      alert('Invalid OTP. Please try 123456');
    }
  };

  const handleOTPResend = () => {
    alert('OTP resent successfully!');
  };

  const handleLogout = () => {
    setUser(null);
    setAuthStep('login');
    setActiveRoute('/dashboard');
  };

  const handleNavigateFromNavbar = (route: string) => {
    if (route === '/profile') {
      // For now, redirect to settings since we don't have a dedicated profile page
      setActiveRoute('/settings');
    } else {
      setActiveRoute(route as ActiveRoute);
    }
  };

  const handleAddClient = async (clientData: Omit<Client, 'id'>) => {
    try {
      console.log('🔄 Adding new client via real data service:', clientData);

      // Use real data service to add client with Whitebooks API integration
      const newClient = await realDataService.addClient(clientData.email, clientData.gstin);

      // Update local state
      setClients(prev => [...prev, newClient]);

      // Refresh real data to get updated stats and notices
      await loadRealData();

      console.log('✅ Client added successfully via Whitebooks API:', newClient);
      alert(`Client "${newClient.clientName}" added successfully with real data from Whitebooks API!`);
    } catch (error) {
      console.error('❌ Error adding client via Whitebooks API:', error);

      // Fallback to original tax portal service logic
      const newClient: Client = {
        ...clientData,
        id: `client_${Date.now()}`,
        autoSyncEnabled: true,
        totalNotices: 0,
        pendingReplies: 0
      };

      // Add client to state first
      setClients(prev => [...prev, newClient]);

      try {
        // Automatically download all notices for the new client
        const { notices: downloadedNotices, syncLog } = await taxPortalService.downloadAllNotices(newClient);

        // Update client with sync information
        setClients(prev => prev.map(client =>
          client.id === newClient.id
            ? {
                ...client,
                lastNoticeSync: syncLog.syncDate,
                totalNotices: downloadedNotices.length,
                pendingReplies: downloadedNotices.length
              }
            : client
        ));

        // Add downloaded notices to the notices state
        setNotices(prev => [...prev, ...downloadedNotices]);

        alert(`Client added successfully! Downloaded ${downloadedNotices.length} notices from tax portal.`);
      } catch (fallbackError) {
        console.error('Failed to download notices for new client:', fallbackError);
        alert('Client added, but failed to download notices from tax portal. Please try manual sync later.');
      }
    }
  };

  const handleBulkUpload = (file: File) => {
    // Simulate bulk upload
    alert(`Bulk upload initiated for file: ${file.name}`);
  };

  const handleDailySync = async () => {
    try {
      const activeClients = clients.filter(c => c.status === 'active' && c.autoSyncEnabled);
      const logs = await taxPortalService.runDailySync(activeClients);

      // Collect all new notices from sync
      const allNewNotices: Notice[] = [];

      for (const log of logs) {
        if (log.status === 'success' && log.noticesDownloaded > 0) {
          const client = clients.find(c => c.id === log.clientId);
          if (client) {
            const { notices: newNotices } = await taxPortalService.checkForNewNotices(client, client.lastNoticeSync);
            allNewNotices.push(...newNotices);
          }
        }
      }

      // Update notices state
      setNotices(prev => [...prev, ...allNewNotices]);

      // Update sync logs
      setSyncLogs(prev => [...prev, ...logs]);

      // Update clients with new sync information
      setClients(prev => prev.map(client => {
        const clientLog = logs.find(log => log.clientId === client.id);
        if (clientLog) {
          return {
            ...client,
            lastNoticeSync: clientLog.syncDate,
            totalNotices: (client.totalNotices || 0) + clientLog.noticesDownloaded,
            pendingReplies: (client.pendingReplies || 0) + clientLog.noticesDownloaded
          };
        }
        return client;
      }));

      const totalNewNotices = allNewNotices.length;
      alert(`Daily sync completed! Found ${totalNewNotices} new notices across ${logs.length} clients.`);
    } catch (error) {
      console.error('Daily sync failed:', error);
      alert('Daily sync failed. Please check the logs and try again.');
    }
  };

  const handleViewClient = (clientId: string) => {
    const client = clients.find(c => c.id === clientId);
    if (client) {
      setSelectedClient(client);
    }
  };

  const handleDownloadNotices = (clientId: string) => {
    const client = clients.find(c => c.id === clientId);
    if (client) {
      setSelectedClient(client);
      // Open the client detail modal with focus on Notice Download tab
    }
  };

  const handleCloseClientModal = () => {
    setSelectedClient(null);
  };

  const handleSyncClientNotices = async (clientId: string) => {
    const client = clients.find(c => c.id === clientId);
    if (client) {
      try {
        const { notices: newNotices, syncLog } = await taxPortalService.checkForNewNotices(client, client.lastNoticeSync);

        // Update notices state
        setNotices(prev => [...prev, ...newNotices]);

        // Update sync logs
        setSyncLogs(prev => [...prev, syncLog]);

        // Update client with new sync information
        setClients(prev => prev.map(c =>
          c.id === clientId
            ? {
                ...c,
                lastNoticeSync: syncLog.syncDate,
                totalNotices: (c.totalNotices || 0) + syncLog.noticesDownloaded,
                pendingReplies: (c.pendingReplies || 0) + syncLog.noticesDownloaded
              }
            : c
        ));

        alert(`Sync completed! Found ${newNotices.length} new notices.`);
      } catch (error) {
        console.error('Failed to sync client notices:', error);
        alert('Failed to sync notices. Please try again.');
      }
    }
  };

  const handleSendNoticeReply = (noticeId: string, reply: string) => {
    setNotices(prev => 
      prev.map(notice => 
        notice.id === noticeId 
          ? { ...notice, status: NoticeStatus.REPLIED }
          : notice
      )
    );
    alert('Reply sent successfully!');
  };

  const handleViewNotice = (noticeId: string) => {
    const notice = notices.find(n => n.id === noticeId);
    if (notice) {
      console.log('Viewing notice:', notice.subject);
    }
  };

  const handleSendReply = (noticeId: string, reply: string) => {
    console.log('Sending reply for notice:', noticeId, 'Reply:', reply);
    // Update the notice status to 'replied'
    setNotices(prevNotices =>
      prevNotices.map(notice =>
        notice.id === noticeId
          ? { ...notice, status: 'replied' as NoticeStatus }
          : notice
      )
    );
    // TODO: Send reply to backend
  };

  if (authStep === 'login') {
    return (
      <ThemeContextProvider>
        <ModernAuthForm
          onLogin={handleLogin}
          onRegister={handleRegister}
          onGoogleLogin={handleGoogleLogin}
          onForgotPassword={handleForgotPassword}
          loading={false}
        />
      </ThemeContextProvider>
    );
  }

  if (authStep === 'otp') {
    return (
      <ThemeContextProvider>
        <OTPVerification
          phoneNumber={phoneNumber}
          onVerify={handleOTPVerify}
          onResend={handleOTPResend}
          onBack={() => setAuthStep('login')}
          loading={false}
        />
      </ThemeContextProvider>
    );
  }

  if (!user) return null;

  const renderContent = () => {
    switch (activeRoute) {
      case '/dashboard':
        const statsToUse = realStats || dashboardStats || mockQuery.dashboardStats;
        const chartDataToUse = realChartData.length > 0 ? realChartData : mockQuery.noticesByStatus;
        const isUsingRealData = realStats !== null && realChartData.length > 0;

        console.log('📊 Dashboard rendering with:', {
          stats: statsToUse,
          chartData: chartDataToUse,
          isUsingRealData,
          realDataLoading,
          notices: notices.length
        });

        return (
          <Dashboard
            key={isUsingRealData ? 'real-data' : 'fallback-data'}
            stats={statsToUse}
            chartData={chartDataToUse}
            recentNotices={notices.slice(0, 12)}
            onViewNotice={handleViewNotice}
            onSendReply={handleSendReply}
            loading={realDataLoading || statsLoading}
            error={statsError}
          />
        );
      case '/clients':
        return (
          <ClientList
            clients={clients}
            onAddClient={handleAddClient}
            onViewClient={handleViewClient}
            onBulkUpload={handleBulkUpload}
            onDownloadNotices={handleDownloadNotices}
          />
        );
      case '/notices':
        return (
          <NoticeList
            notices={notices}
            onSendReply={handleSendNoticeReply}
          />
        );
      case '/replies':
        return <RepliesManagement />;
      case '/settings':
        return <SettingsPage onLogout={handleLogout} />;
      default:
        return (
          <Box sx={{ p: 3 }}>
            <h2>Coming Soon</h2>
            <p>This feature is under development.</p>
          </Box>
        );
    }
  };

  return (
    <ThemeContextProvider>
      <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
        <Navbar
          user={user}
          onMenuClick={() => setSidebarOpen(true)}
          onLogout={handleLogout}
          onNavigate={handleNavigateFromNavbar}
          title={activeRoute.replace('/', '').charAt(0).toUpperCase() + activeRoute.slice(2)}
          notificationCount={3}
        />
        
        <Sidebar
          open={sidebarOpen}
          onClose={() => setSidebarOpen(false)}
          activeRoute={activeRoute}
          onNavigate={(route) => {
            setActiveRoute(route as ActiveRoute);
            setSidebarOpen(false);
          }}
          onLogout={handleLogout}
        />

        <Box
          component="main"
          sx={{
            flexGrow: 1,
            bgcolor: 'background.default',
            marginLeft: sidebarOpen ? '280px' : 0,
            transition: 'margin-left 0.3s ease',
          }}
        >
          {renderContent()}
        </Box>

        {/* Client Detail Modal */}
        <ClientDetailModal
          client={selectedClient}
          open={!!selectedClient}
          onClose={handleCloseClientModal}
          notices={notices}
          onSyncNotices={handleSyncClientNotices}
        />
      </Box>
    </ThemeContextProvider>
  );
};

export default App;
