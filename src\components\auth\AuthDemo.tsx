import React, { useState } from 'react';
import { ThemeProvider } from '@mui/material/styles';
import { CssBaseline, Alert, Snackbar } from '@mui/material';
import theme from '../../theme/theme';
import ModernAuthForm from './ModernAuthForm';

interface AuthCredentials {
  email?: string;
  phone?: string;
  password: string;
  confirmPassword?: string;
  fullName?: string;
  companyName?: string;
  rememberMe?: boolean;
}

const AuthDemo: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [notification, setNotification] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'info';
  }>({
    open: false,
    message: '',
    severity: 'info'
  });

  const showNotification = (message: string, severity: 'success' | 'error' | 'info' = 'info') => {
    setNotification({ open: true, message, severity });
  };

  const handleLogin = async (credentials: AuthCredentials) => {
    setLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setLoading(false);
      const loginMethod = credentials.email ? 'email' : 'phone';
      const identifier = credentials.email || credentials.phone;
      showNotification(
        `Login successful! Welcome back. Logged in with ${loginMethod}: ${identifier}`,
        'success'
      );
    }, 2000);
  };

  const handleRegister = async (credentials: AuthCredentials) => {
    setLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setLoading(false);
      const loginMethod = credentials.email ? 'email' : 'phone';
      const identifier = credentials.email || credentials.phone;
      showNotification(
        `Registration successful! Welcome ${credentials.fullName}. Account created with ${loginMethod}: ${identifier}`,
        'success'
      );
    }, 2000);
  };

  const handleGoogleLogin = async () => {
    setLoading(true);
    
    // Simulate Google OAuth
    setTimeout(() => {
      setLoading(false);
      showNotification('Google login successful! Welcome back.', 'success');
    }, 1500);
  };

  const handleForgotPassword = async (email: string) => {
    setLoading(true);
    
    // Simulate forgot password API call
    setTimeout(() => {
      setLoading(false);
      showNotification(
        `Password reset link sent to ${email}. Please check your inbox.`,
        'info'
      );
    }, 1000);
  };

  const handleCloseNotification = () => {
    setNotification(prev => ({ ...prev, open: false }));
  };

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <ModernAuthForm
        onLogin={handleLogin}
        onRegister={handleRegister}
        onGoogleLogin={handleGoogleLogin}
        onForgotPassword={handleForgotPassword}
        loading={loading}
      />
      
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          onClose={handleCloseNotification}
          severity={notification.severity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </ThemeProvider>
  );
};

export default AuthDemo;
