"""
Notice management service for downloading, processing, and managing GST notices
"""
import os
import asyncio
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc
from ..core.config import settings
from ..core.database import get_db
from ..models import Client, Notice, NoticeStatus, SyncLog, Log, LogLevel, LogCategory
from ..schemas.notice import NoticeCreate, NoticeUpdate
from ..services.gst_portal import gst_portal_service, GSTPortalError
from ..services.file_service import file_service
import logging

logger = logging.getLogger(__name__)


class NoticeService:
    """Service for managing GST notices"""
    
    def __init__(self):
        self.gst_service = gst_portal_service
        self.file_service = file_service
    
    async def download_initial_notices(
        self, 
        client: Client, 
        years_back: int = None,
        db: Session = None
    ) -> Tuple[int, int, List[str]]:
        """
        Download all notices for a new client
        Returns (notices_found, notices_downloaded, errors)
        """
        if not db:
            db = next(get_db())
        
        years_back = years_back or settings.MAX_NOTICE_FETCH_YEARS
        errors = []
        notices_found = 0
        notices_downloaded = 0
        
        # Create sync log
        sync_log = SyncLog(
            sync_id=f"initial_{client.id}_{int(datetime.utcnow().timestamp())}",
            sync_type="initial",
            started_at=datetime.utcnow(),
            status="running",
            clients_processed=1
        )
        db.add(sync_log)
        db.commit()
        
        try:
            # Authenticate with GST portal
            session_token = await self.gst_service.authenticate_client(client)
            
            # Calculate date range
            to_date = datetime.utcnow()
            from_date = to_date - timedelta(days=years_back * 365)
            
            # Fetch notices from GST portal
            portal_notices = await self.gst_service.fetch_notices(
                client, from_date, to_date, session_token
            )
            notices_found = len(portal_notices)
            
            # Process each notice
            for notice_data in portal_notices:
                try:
                    # Check if notice already exists
                    existing_notice = db.query(Notice).filter(
                        and_(
                            Notice.notice_id == notice_data.get("notice_id"),
                            Notice.client_id == client.id
                        )
                    ).first()
                    
                    if existing_notice:
                        logger.info(f"Notice {notice_data.get('notice_id')} already exists, skipping")
                        continue
                    
                    # Parse notice data
                    notice_create = self.gst_service.parse_notice_data(notice_data, client)
                    
                    # Download notice document
                    try:
                        file_content, filename = await self.gst_service.download_notice_document(
                            notice_data.get("notice_id"),
                            session_token,
                            client.gstin
                        )
                        
                        # Save file
                        file_path = await self.file_service.save_notice_file(
                            client.gstin,
                            notice_data.get("notice_id"),
                            file_content,
                            filename
                        )
                        
                        notice_create.file_path = file_path
                        notice_create.original_filename = filename
                        notice_create.file_size = len(file_content)
                        notice_create.file_hash = self.gst_service.calculate_file_hash(file_content)
                        
                    except Exception as e:
                        logger.warning(f"Failed to download document for notice {notice_data.get('notice_id')}: {str(e)}")
                        errors.append(f"Document download failed for {notice_data.get('notice_id')}: {str(e)}")
                    
                    # Create notice in database
                    notice = Notice(**notice_create.dict())
                    db.add(notice)
                    notices_downloaded += 1
                    
                    # Add small delay to avoid overwhelming the system
                    await asyncio.sleep(0.1)
                    
                except Exception as e:
                    error_msg = f"Failed to process notice {notice_data.get('notice_id', 'unknown')}: {str(e)}"
                    logger.error(error_msg)
                    errors.append(error_msg)
            
            # Update client statistics
            client.total_notices = notices_downloaded
            client.pending_replies = notices_downloaded
            client.last_sync_at = datetime.utcnow()
            client.last_successful_sync_at = datetime.utcnow()
            
            # Update sync log
            sync_log.completed_at = datetime.utcnow()
            sync_log.status = "completed" if not errors else "partial"
            sync_log.notices_found = notices_found
            sync_log.notices_downloaded = notices_downloaded
            sync_log.notices_failed = len(errors)
            sync_log.total_duration_seconds = int(
                (sync_log.completed_at - sync_log.started_at).total_seconds()
            )
            
            if errors:
                sync_log.error_message = f"{len(errors)} notices failed to process"
                sync_log.error_details = {"errors": errors}
            
            db.commit()
            
            # Logout session
            await self.gst_service.logout_session(session_token)
            
            logger.info(f"Initial sync completed for client {client.gstin}: {notices_downloaded}/{notices_found} notices downloaded")
            
            return notices_found, notices_downloaded, errors
            
        except GSTPortalError as e:
            sync_log.completed_at = datetime.utcnow()
            sync_log.status = "failed"
            sync_log.error_message = str(e)
            db.commit()
            raise e
        except Exception as e:
            sync_log.completed_at = datetime.utcnow()
            sync_log.status = "failed"
            sync_log.error_message = str(e)
            db.commit()
            logger.error(f"Initial sync failed for client {client.gstin}: {str(e)}")
            raise e
    
    async def sync_new_notices(
        self, 
        client: Client, 
        db: Session = None
    ) -> Tuple[int, int, List[str]]:
        """
        Sync new notices for an existing client
        Returns (notices_found, notices_downloaded, errors)
        """
        if not db:
            db = next(get_db())
        
        errors = []
        notices_found = 0
        notices_downloaded = 0
        
        try:
            # Authenticate with GST portal
            session_token = await self.gst_service.authenticate_client(client)
            
            # Calculate date range (from last sync or 30 days back)
            to_date = datetime.utcnow()
            if client.last_successful_sync_at:
                from_date = client.last_successful_sync_at - timedelta(hours=1)  # 1 hour overlap
            else:
                from_date = to_date - timedelta(days=30)
            
            # Fetch notices from GST portal
            portal_notices = await self.gst_service.fetch_notices(
                client, from_date, to_date, session_token
            )
            notices_found = len(portal_notices)
            
            # Process each notice
            for notice_data in portal_notices:
                try:
                    # Check if notice already exists
                    existing_notice = db.query(Notice).filter(
                        and_(
                            Notice.notice_id == notice_data.get("notice_id"),
                            Notice.client_id == client.id
                        )
                    ).first()
                    
                    if existing_notice:
                        continue
                    
                    # Parse and save notice (same logic as initial download)
                    notice_create = self.gst_service.parse_notice_data(notice_data, client)
                    
                    # Download document
                    try:
                        file_content, filename = await self.gst_service.download_notice_document(
                            notice_data.get("notice_id"),
                            session_token,
                            client.gstin
                        )
                        
                        file_path = await self.file_service.save_notice_file(
                            client.gstin,
                            notice_data.get("notice_id"),
                            file_content,
                            filename
                        )
                        
                        notice_create.file_path = file_path
                        notice_create.original_filename = filename
                        notice_create.file_size = len(file_content)
                        notice_create.file_hash = self.gst_service.calculate_file_hash(file_content)
                        
                    except Exception as e:
                        logger.warning(f"Failed to download document for notice {notice_data.get('notice_id')}: {str(e)}")
                    
                    # Create notice
                    notice = Notice(**notice_create.dict())
                    db.add(notice)
                    notices_downloaded += 1
                    
                except Exception as e:
                    error_msg = f"Failed to process notice {notice_data.get('notice_id', 'unknown')}: {str(e)}"
                    logger.error(error_msg)
                    errors.append(error_msg)
            
            # Update client statistics
            client.total_notices += notices_downloaded
            client.pending_replies += notices_downloaded
            client.last_sync_at = datetime.utcnow()
            if notices_downloaded > 0 or not errors:
                client.last_successful_sync_at = datetime.utcnow()
            
            db.commit()
            
            # Logout session
            await self.gst_service.logout_session(session_token)
            
            logger.info(f"Sync completed for client {client.gstin}: {notices_downloaded} new notices")
            
            return notices_found, notices_downloaded, errors
            
        except Exception as e:
            logger.error(f"Sync failed for client {client.gstin}: {str(e)}")
            raise e
    
    def get_client_notices(
        self, 
        client_id: int, 
        status: Optional[NoticeStatus] = None,
        limit: int = 100,
        offset: int = 0,
        db: Session = None
    ) -> List[Notice]:
        """Get notices for a specific client"""
        if not db:
            db = next(get_db())
        
        query = db.query(Notice).filter(Notice.client_id == client_id)
        
        if status:
            query = query.filter(Notice.status == status)
        
        return query.order_by(desc(Notice.notice_date)).offset(offset).limit(limit).all()
    
    def update_notice_status(
        self, 
        notice_id: int, 
        status: NoticeStatus,
        db: Session = None
    ) -> Notice:
        """Update notice status"""
        if not db:
            db = next(get_db())
        
        notice = db.query(Notice).filter(Notice.id == notice_id).first()
        if notice:
            notice.status = status
            notice.processed_at = datetime.utcnow()
            db.commit()
            db.refresh(notice)
        
        return notice


# Global instance
notice_service = NoticeService()
