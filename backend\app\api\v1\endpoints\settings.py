"""
Settings management endpoints
"""
from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from ....core.database import get_db
from ....models import Setting, SettingType, User
from ..endpoints.auth import get_current_active_user, get_current_admin_user
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/")
async def get_settings(
    category: SettingType = None,
    public_only: bool = True,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get settings"""
    query = db.query(Setting)
    
    if category:
        query = query.filter(Setting.category == category)
    
    # Non-admin users can only see public settings
    if current_user.role != "admin" and public_only:
        query = query.filter(Setting.is_public == True)
    
    settings = query.all()
    
    result = {}
    for setting in settings:
        # Use json_value if available, otherwise value
        value = setting.json_value if setting.json_value is not None else setting.value
        result[setting.key] = {
            "value": value,
            "description": setting.description,
            "category": setting.category,
            "is_public": setting.is_public
        }
    
    return result


@router.get("/{key}")
async def get_setting(
    key: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get specific setting"""
    setting = db.query(Setting).filter(Setting.key == key).first()
    
    if not setting:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Setting not found"
        )
    
    # Check permissions
    if not setting.is_public and current_user.role != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied"
        )
    
    value = setting.json_value if setting.json_value is not None else setting.value
    
    return {
        "key": setting.key,
        "value": value,
        "description": setting.description,
        "category": setting.category,
        "is_public": setting.is_public
    }


@router.put("/{key}")
async def update_setting(
    key: str,
    value: Dict[str, Any],
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Update setting (admin only)"""
    setting = db.query(Setting).filter(Setting.key == key).first()
    
    if not setting:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Setting not found"
        )
    
    new_value = value.get("value")
    
    # Determine if value should be stored as JSON or string
    if isinstance(new_value, (dict, list)):
        setting.json_value = new_value
        setting.value = None
    else:
        setting.value = str(new_value)
        setting.json_value = None
    
    db.commit()
    db.refresh(setting)
    
    logger.info(f"Setting updated: {key} by admin {current_user.email}")
    
    return {
        "key": setting.key,
        "value": setting.json_value if setting.json_value is not None else setting.value,
        "message": "Setting updated successfully"
    }


@router.post("/")
async def create_setting(
    setting_data: Dict[str, Any],
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Create new setting (admin only)"""
    key = setting_data.get("key")
    value = setting_data.get("value")
    category = setting_data.get("category", SettingType.SYSTEM)
    description = setting_data.get("description", "")
    is_public = setting_data.get("is_public", False)
    
    if not key:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Setting key is required"
        )
    
    # Check if setting already exists
    existing_setting = db.query(Setting).filter(Setting.key == key).first()
    if existing_setting:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Setting already exists"
        )
    
    # Create setting
    setting = Setting(
        key=key,
        category=category,
        description=description,
        is_public=is_public
    )
    
    # Set value
    if isinstance(value, (dict, list)):
        setting.json_value = value
    else:
        setting.value = str(value)
    
    db.add(setting)
    db.commit()
    db.refresh(setting)
    
    logger.info(f"Setting created: {key} by admin {current_user.email}")
    
    return {
        "key": setting.key,
        "value": setting.json_value if setting.json_value is not None else setting.value,
        "message": "Setting created successfully"
    }


@router.delete("/{key}")
async def delete_setting(
    key: str,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Delete setting (admin only)"""
    setting = db.query(Setting).filter(Setting.key == key).first()
    
    if not setting:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Setting not found"
        )
    
    db.delete(setting)
    db.commit()
    
    logger.info(f"Setting deleted: {key} by admin {current_user.email}")
    
    return {"message": "Setting deleted successfully"}


@router.get("/categories/list")
async def get_setting_categories(
    current_user: User = Depends(get_current_active_user)
):
    """Get list of setting categories"""
    return [
        {"value": category.value, "label": category.value.replace("_", " ").title()}
        for category in SettingType
    ]
