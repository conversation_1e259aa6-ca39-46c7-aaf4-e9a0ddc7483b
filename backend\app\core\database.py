"""
Database connection and session management
"""
from sqlalchemy import create_engine
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
from .config import settings
from ..models.base import Base


# Synchronous database engine
engine = create_engine(
    settings.DATABASE_URL,
    poolclass=StaticPool,
    pool_pre_ping=True,
    echo=settings.DEBUG,
)

# Asynchronous database engine
async_engine = create_async_engine(
    settings.DATABASE_URL_ASYNC,
    poolclass=StaticPool,
    pool_pre_ping=True,
    echo=settings.DEBUG,
)

# Session makers
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine,
    class_=Session
)

AsyncSessionLocal = sessionmaker(
    bind=async_engine,
    class_=AsyncSession,
    autocommit=False,
    autoflush=False,
)


def get_db() -> Session:
    """
    Dependency to get database session for synchronous operations
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


async def get_async_db() -> AsyncSession:
    """
    Dependency to get database session for asynchronous operations
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()


async def create_tables():
    """
    Create all database tables
    """
    async with async_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)


async def drop_tables():
    """
    Drop all database tables (use with caution!)
    """
    async with async_engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)


def create_tables_sync():
    """
    Create all database tables synchronously
    """
    Base.metadata.create_all(bind=engine)


def drop_tables_sync():
    """
    Drop all database tables synchronously (use with caution!)
    """
    Base.metadata.drop_all(bind=engine)
