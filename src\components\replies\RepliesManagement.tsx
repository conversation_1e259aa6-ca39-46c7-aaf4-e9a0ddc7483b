import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Chip,
  Stack,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  IconButton,
  Tooltip,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  CardActions,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemButton
} from '@mui/material';
import {
  Send as SendIcon,
  Visibility as VisibilityIcon,
  AutoAwesome as AIIcon,
  Email as EmailIcon,
  CheckCircle as CheckCircleIcon,
  Schedule as ScheduleIcon,
  Close as CloseIcon,
  AccountBalance as GSTIcon,
  Receipt as ITIcon,
  Assignment as TDSIcon,
  Business as ClientIcon,
  Description as NoticeIcon,
  Reply as ReplyIcon
} from '@mui/icons-material';
import { simpleGstApiService as gstApiService } from '../../services/gstApiServiceSimple';

type TaxType = 'GST' | 'IT' | 'TDS';

interface Client {
  id: string;
  name: string;
  gstin?: string;
  pan?: string;
  tan?: string;
}

interface Notice {
  id: string;
  clientId: string;
  taxType: TaxType;
  noticeNumber: string;
  noticeType: string;
  issueDate: string;
  dueDate: string;
  status: 'pending' | 'replied' | 'resolved';
  description: string;
}

interface Reply {
  id: string;
  noticeId: string;
  clientId: string;
  taxType: TaxType;
  aiReply: string;
  status: 'draft' | 'reviewed' | 'sent';
  generatedDate: string;
  sentDate?: string;
  reviewedBy?: string;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => {
  return (
    <div hidden={value !== index}>
      {value === index && <Box>{children}</Box>}
    </div>
  );
};

// Mock Data
const mockClients: Client[] = [
  { id: '1', name: 'Client 1', gstin: '27**********1Z5', pan: '**********', tan: 'DELH12345A' },
  { id: '2', name: 'Client 2', gstin: '29**********2Y6', pan: '**********', tan: 'MUMH67890B' },
  { id: '3', name: 'Client 3', gstin: '33**********3X7', pan: '**********', tan: 'BANG34567C' },
];

const mockNotices: Notice[] = [
  {
    id: '1', clientId: '1', taxType: 'GST', noticeNumber: 'GST-001-2024',
    noticeType: 'GSTR-3B Mismatch', issueDate: '2024-01-15', dueDate: '2024-02-15',
    status: 'pending', description: 'Discrepancy in GSTR-3B filing for December 2023'
  },
  {
    id: '2', clientId: '1', taxType: 'IT', noticeNumber: 'IT-002-2024',
    noticeType: 'TDS Default', issueDate: '2024-01-20', dueDate: '2024-02-20',
    status: 'pending', description: 'TDS not deducted on professional fees'
  },
  {
    id: '3', clientId: '2', taxType: 'TDS', noticeNumber: 'TDS-003-2024',
    noticeType: 'Late Filing', issueDate: '2024-01-25', dueDate: '2024-02-25',
    status: 'replied', description: 'TDS return filed after due date'
  },
  {
    id: '4', clientId: '1', taxType: 'GST', noticeNumber: 'GST-004-2024',
    noticeType: 'Input Credit Mismatch', issueDate: '2024-02-01', dueDate: '2024-03-01',
    status: 'pending', description: 'ITC claimed more than eligible amount'
  },
];

const mockReplies: Reply[] = [
  {
    id: '1', noticeId: '1', clientId: '1', taxType: 'GST',
    aiReply: 'Respected Sir/Madam,\n\nWith reference to the above notice regarding GSTR-3B mismatch, we would like to submit our response...',
    status: 'draft', generatedDate: '2024-01-16'
  },
  {
    id: '2', noticeId: '2', clientId: '1', taxType: 'IT',
    aiReply: 'Dear Tax Officer,\n\nWe acknowledge receipt of the notice regarding TDS default. We wish to clarify that...',
    status: 'reviewed', generatedDate: '2024-01-21', reviewedBy: 'CA Rajesh Kumar'
  },
  {
    id: '3', noticeId: '3', clientId: '2', taxType: 'TDS',
    aiReply: 'To The Assessing Officer,\n\nWe regret the delay in filing TDS return. Due to technical issues...',
    status: 'sent', generatedDate: '2024-01-26', sentDate: '2024-01-28'
  },
];

const RepliesManagement: React.FC = () => {
  const [taxTypeTab, setTaxTypeTab] = useState<TaxType>('GST');
  const [selectedClientId, setSelectedClientId] = useState<string>('');
  const [selectedNoticeId, setSelectedNoticeId] = useState<string>('');
  const [selectedReply, setSelectedReply] = useState<Reply | null>(null);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [sendConfirmOpen, setSendConfirmOpen] = useState(false);
  const [editedReply, setEditedReply] = useState('');

  // Helper functions
  const getFilteredNotices = () => {
    return mockNotices.filter(notice =>
      notice.taxType === taxTypeTab &&
      (selectedClientId === '' || notice.clientId === selectedClientId)
    );
  };

  const getFilteredReplies = () => {
    return mockReplies.filter(reply =>
      reply.taxType === taxTypeTab &&
      (selectedNoticeId === '' || reply.noticeId === selectedNoticeId)
    );
  };

  const getClientName = (clientId: string) => {
    return mockClients.find(client => client.id === clientId)?.name || 'Unknown Client';
  };

  const getNoticeDetails = (noticeId: string) => {
    return mockNotices.find(notice => notice.id === noticeId);
  };

  // Event handlers
  const handleTaxTypeChange = (event: React.SyntheticEvent, newValue: TaxType) => {
    setTaxTypeTab(newValue);
    setSelectedClientId('');
    setSelectedNoticeId('');
  };

  const handleClientChange = (clientId: string) => {
    setSelectedClientId(clientId);
    setSelectedNoticeId('');
  };

  // Function to fetch real GST notices from API
  const fetchGSTNoticesForClient = async (clientId: string) => {
    try {
      const client = mockClients.find(c => c.id === clientId);
      if (!client || !client.gstin) {
        console.log('No GSTIN found for client');
        return;
      }

      console.log(`Fetching GST notices for client ${client.name} (${client.gstin})`);
      const gstNotices = await gstApiService.fetchGSTNotices(client.gstin);

      // Transform GST API notices to our Notice format
      const transformedNotices = gstNotices.map(gstNotice => ({
        id: gstNotice.noticeNumber,
        clientId: clientId,
        taxType: 'GST' as TaxType,
        noticeNumber: gstNotice.noticeNumber,
        noticeType: gstNotice.noticeType,
        issueDate: gstNotice.issueDate,
        dueDate: gstNotice.dueDate,
        status: gstNotice.status === 'Open' ? 'pending' as const : 'replied' as const,
        description: gstNotice.description
      }));

      console.log('Fetched GST notices:', transformedNotices);
      // Here you would update your notices state with the fetched data
      // setNotices(prev => [...prev.filter(n => n.taxType !== 'GST' || n.clientId !== clientId), ...transformedNotices]);

    } catch (error) {
      console.error('Error fetching GST notices:', error);
    }
  };

  // Old mock data (to be removed)
  const oldMockReplies: any[] = [
    {
      id: 'REP001',
      clientName: 'ABC Enterprises Pvt Ltd',
      gstin: '27AAPFU0939F1ZV',
      noticeName: 'GST Notice - GSTR-1 Discrepancy',
      noticeType: 'GST Notice',
      aiReply: `Dear Sir/Madam,

We acknowledge receipt of the GST Notice dated [Date] regarding discrepancy in GSTR-1 Return for the period [Period].

After thorough review of our records, we would like to submit the following clarification:

1. The discrepancy mentioned appears to be due to a technical error in data transmission.
2. We have verified our internal records and found them to be accurate.
3. Attached herewith are the supporting documents for your reference.

We request you to kindly review the submitted documents and close this matter at the earliest.

Thank you for your cooperation.

Yours faithfully,
[Client Name]
[GSTIN: 27AAPFU0939F1ZV]`,
      status: 'draft',
      generatedDate: '2024-12-21T10:30:00Z',
      clientEmail: '<EMAIL>'
    },
    {
      id: 'REP002',
      clientName: 'XYZ Trading Company',
      gstin: '29AABCU9603R1ZX',
      noticeName: 'Show Cause Notice - ITC Verification',
      noticeType: 'Show Cause Notice',
      aiReply: `Dear Officer,

This is in response to the Show Cause Notice dated [Date] regarding Input Tax Credit claim verification.

We hereby submit our response with the following points:

1. All ITC claims are genuine and supported by valid tax invoices.
2. The invoices have been verified through GSTR-2A matching.
3. Complete documentation is attached for your perusal.

We trust this clarifies the matter and request closure of the notice.

Respectfully yours,
[Client Name]
[GSTIN: 29AABCU9603R1ZX]`,
      status: 'reviewed',
      generatedDate: '2024-12-20T14:15:00Z',
      clientEmail: '<EMAIL>'
    },
    {
      id: 'REP003',
      clientName: 'PQR Manufacturing Ltd',
      gstin: '24AAGCC7409Q1ZZ',
      noticeName: 'Demand Notice - Additional Tax',
      noticeType: 'Demand Notice',
      aiReply: `Dear Sir/Madam,

We are in receipt of the Demand Notice dated [Date] for additional tax demand.

After careful examination, we submit our response as follows:

1. We disagree with the demand raised as per the grounds mentioned below.
2. The calculation appears to have errors which we have identified.
3. We request a personal hearing to present our case.

Detailed response and supporting documents are enclosed.

Thanking you,
[Client Name]
[GSTIN: 24AAGCC7409Q1ZZ]`,
      status: 'sent',
      generatedDate: '2024-12-19T09:45:00Z',
      sentDate: '2024-12-19T16:30:00Z',
      clientEmail: '<EMAIL>'
    }
  ];

  // Removed old tab change handler

  const handlePreviewReply = (reply: Reply) => {
    setSelectedReply(reply);
    setEditedReply(reply.aiReply);
    setPreviewOpen(true);
  };

  const handleSendReply = (reply: Reply) => {
    setSelectedReply(reply);
    setSendConfirmOpen(true);
  };

  const confirmSendReply = () => {
    if (selectedReply) {
      console.log('Sending reply:', selectedReply.id);
      // TODO: Implement actual email sending logic
      alert(`Reply sent successfully to ${selectedReply.clientEmail}`);
    }
    setSendConfirmOpen(false);
    setSelectedReply(null);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft':
        return 'warning';
      case 'reviewed':
        return 'info';
      case 'sent':
        return 'success';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'draft':
        return <AIIcon sx={{ fontSize: 16 }} />;
      case 'reviewed':
        return <VisibilityIcon sx={{ fontSize: 16 }} />;
      case 'sent':
        return <CheckCircleIcon sx={{ fontSize: 16 }} />;
      default:
        return null;
    }
  };

  return (
    <Box sx={{ p: 3, backgroundColor: 'background.default', minHeight: '100vh' }}>
      <Typography variant="h4" fontWeight="bold" gutterBottom>
        Replies Management
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        Manage replies for GST, IT, and TDS notices by client
      </Typography>

      {/* Tax Type Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={taxTypeTab}
          onChange={handleTaxTypeChange}
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab
            label="GST"
            value="GST"
            icon={<GSTIcon />}
            iconPosition="start"
            sx={{ minHeight: 64, fontWeight: 600 }}
          />
          <Tab
            label="Income Tax"
            value="IT"
            icon={<ITIcon />}
            iconPosition="start"
            sx={{ minHeight: 64, fontWeight: 600 }}
          />
          <Tab
            label="TDS"
            value="TDS"
            icon={<TDSIcon />}
            iconPosition="start"
            sx={{ minHeight: 64, fontWeight: 600 }}
          />
        </Tabs>
      </Paper>

      {/* Client and Notice Selection */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        {/* Client Dropdown */}
        <Grid item xs={12} md={4}>
          <FormControl fullWidth>
            <InputLabel>Select Client</InputLabel>
            <Select
              value={selectedClientId}
              label="Select Client"
              onChange={(e) => handleClientChange(e.target.value)}
              startAdornment={<ClientIcon sx={{ mr: 1, color: 'action.active' }} />}
            >
              <MenuItem value="">
                <em>All Clients</em>
              </MenuItem>
              {mockClients.map((client) => (
                <MenuItem key={client.id} value={client.id}>
                  {client.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Notice Selection */}
        <Grid item xs={12} md={8}>
          <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
            <NoticeIcon />
            Notices ({getFilteredNotices().length})
          </Typography>

          {getFilteredNotices().length === 0 ? (
            <Alert severity="info">
              No notices found for {taxTypeTab} {selectedClientId ? `for ${getClientName(selectedClientId)}` : ''}
            </Alert>
          ) : (
            <Paper sx={{ maxHeight: 300, overflow: 'auto' }}>
              <List>
                {getFilteredNotices().map((notice, index) => (
                  <React.Fragment key={notice.id}>
                    <ListItemButton
                      selected={selectedNoticeId === notice.id}
                      onClick={() => setSelectedNoticeId(notice.id)}
                    >
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography variant="subtitle2" fontWeight="600">
                              {notice.noticeNumber}
                            </Typography>
                            <Chip
                              label={notice.status}
                              size="small"
                              color={notice.status === 'pending' ? 'warning' : 'success'}
                            />
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              {notice.noticeType} • {getClientName(notice.clientId)}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              Due: {notice.dueDate} • {notice.description}
                            </Typography>
                          </Box>
                        }
                      />
                    </ListItemButton>
                    {index < getFilteredNotices().length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </Paper>
          )}
        </Grid>
      </Grid>

      {/* Replies Section */}
      {selectedNoticeId && (
        <Card sx={{ mt: 3 }}>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
              <ReplyIcon />
              Replies for Notice: {getNoticeDetails(selectedNoticeId)?.noticeNumber}
            </Typography>

            {getFilteredReplies().length === 0 ? (
              <Alert severity="info">
                No replies found for this notice
              </Alert>
            ) : (
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Status</TableCell>
                      <TableCell>Generated Date</TableCell>
                      <TableCell>Reviewed By</TableCell>
                      <TableCell>Sent Date</TableCell>
                      <TableCell align="center">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {getFilteredReplies().map((reply) => (
                      <TableRow key={reply.id}>
                        <TableCell>
                          <Chip
                            label={reply.status}
                            size="small"
                            color={
                              reply.status === 'draft' ? 'warning' :
                              reply.status === 'reviewed' ? 'info' : 'success'
                            }
                            icon={
                              reply.status === 'draft' ? <AIIcon sx={{ fontSize: 16 }} /> :
                              reply.status === 'reviewed' ? <VisibilityIcon sx={{ fontSize: 16 }} /> :
                              <CheckCircleIcon sx={{ fontSize: 16 }} />
                            }
                          />
                        </TableCell>
                        <TableCell>{reply.generatedDate}</TableCell>
                        <TableCell>{reply.reviewedBy || '-'}</TableCell>
                        <TableCell>{reply.sentDate || '-'}</TableCell>
                        <TableCell align="center">
                          <Stack direction="row" spacing={1} justifyContent="center">
                            <Tooltip title="View Reply">
                              <IconButton
                                size="small"
                                onClick={() => {
                                  setSelectedReply(reply);
                                  setEditedReply(reply.aiReply);
                                  setPreviewOpen(true);
                                }}
                              >
                                <VisibilityIcon />
                              </IconButton>
                            </Tooltip>
                            {reply.status !== 'sent' && (
                              <Tooltip title="Send Reply">
                                <IconButton
                                  size="small"
                                  color="primary"
                                  onClick={() => {
                                    setSelectedReply(reply);
                                    setSendConfirmOpen(true);
                                  }}
                                >
                                  <SendIcon />
                                </IconButton>
                              </Tooltip>
                            )}
                          </Stack>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </CardContent>
        </Card>
      )}

      {/* Preview Dialog */}
      <Dialog
        open={previewOpen}
        onClose={() => setPreviewOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Stack direction="row" justifyContent="space-between" alignItems="center">
            <Typography variant="h6">Reply Preview</Typography>
            <IconButton onClick={() => setPreviewOpen(false)}>
              <CloseIcon />
            </IconButton>
          </Stack>
        </DialogTitle>
        <DialogContent>
          {selectedReply && (
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Notice: {getNoticeDetails(selectedReply.noticeId)?.noticeNumber}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Client: {getClientName(selectedReply.clientId)}
              </Typography>
              <TextField
                fullWidth
                multiline
                rows={12}
                value={editedReply}
                onChange={(e) => setEditedReply(e.target.value)}
                variant="outlined"
                sx={{ mt: 2 }}
              />
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPreviewOpen(false)}>Cancel</Button>
          <Button variant="contained" onClick={() => {
            // TODO: Save edited reply
            setPreviewOpen(false);
          }}>
            Save Changes
          </Button>
        </DialogActions>
      </Dialog>

      {/* Send Confirmation Dialog */}
      <Dialog
        open={sendConfirmOpen}
        onClose={() => setSendConfirmOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Confirm Send Reply</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to send this reply? This action cannot be undone.
          </Typography>
          {selectedReply && (
            <Alert severity="info" sx={{ mt: 2 }}>
              Reply will be sent for notice: {getNoticeDetails(selectedReply.noticeId)?.noticeNumber}
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSendConfirmOpen(false)}>Cancel</Button>
          <Button
            variant="contained"
            color="primary"
            onClick={() => {
              // TODO: Send reply logic
              setSendConfirmOpen(false);
              // Update reply status to 'sent'
            }}
          >
            Send Reply
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default RepliesManagement;
