import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  <PERSON>ton,
  Stack,
  Typography,
  IconButton,
  Divider,
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import AutoAwesomeIcon from '@mui/icons-material/AutoAwesome';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { Client, TaxPortalCredentials as ITaxPortalCredentials } from '../../types/schema';
import GSTINFetcher from './GSTINFetcher';
import TaxPortalCredentialsComponent from './TaxPortalCredentials';
import { simpleGstApiService as gstApiService, GSTClientDetails } from '../../services/gstApiServiceSimple';

interface AddClientModalProps {
  open: boolean;
  onClose: () => void;
  onAdd: (client: Omit<Client, 'id'>) => void;
}

const AddClientModal: React.FC<AddClientModalProps> = ({ open, onClose, onAdd }) => {
  const [showFetcher, setShowFetcher] = useState(true);
  const [dataFetched, setDataFetched] = useState(false);
  const [formData, setFormData] = useState({
    gstin: '',
    clientName: '',
    loginId: '',
    contactPerson: '',
    phone: '',
    email: '',
    address: ''
  });

  const [primaryTaxPortal, setPrimaryTaxPortal] = useState<ITaxPortalCredentials>({
    username: '',
    password: ''
  });

  const [secondaryTaxPortal, setSecondaryTaxPortal] = useState<ITaxPortalCredentials>({
    username: '',
    password: ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [credentialErrors, setCredentialErrors] = useState<{
    primary?: { username?: string; password?: string };
    secondary?: { username?: string; password?: string };
  }>({});

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    const newCredentialErrors: {
      primary?: { username?: string; password?: string };
      secondary?: { username?: string; password?: string };
    } = {};

    // Basic form validation
    if (!formData.gstin) newErrors.gstin = 'GSTIN is required';
    else if (!/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/.test(formData.gstin)) {
      newErrors.gstin = 'Invalid GSTIN format';
    }

    if (!formData.clientName) newErrors.clientName = 'Client name is required';
    if (!formData.contactPerson) newErrors.contactPerson = 'Contact person is required';
    
    if (!formData.phone) newErrors.phone = 'Phone number is required';
    else if (!/^[6-9]\d{9}$/.test(formData.phone.replace(/\D/g, ''))) {
      newErrors.phone = 'Invalid phone number';
    }

    if (!formData.email) newErrors.email = 'Email is required';
    else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Invalid email format';
    }

    if (!formData.address) newErrors.address = 'Address is required';

    // Tax portal credentials validation (at least primary is required)
    if (!primaryTaxPortal.username && !primaryTaxPortal.password) {
      newCredentialErrors.primary = {
        username: 'Primary tax portal username is required',
        password: 'Primary tax portal password is required'
      };
    } else {
      if (!primaryTaxPortal.username) {
        newCredentialErrors.primary = { ...newCredentialErrors.primary, username: 'Username is required' };
      }
      if (!primaryTaxPortal.password) {
        newCredentialErrors.primary = { ...newCredentialErrors.primary, password: 'Password is required' };
      }
    }

    // Secondary credentials validation (only if partially filled)
    if (secondaryTaxPortal.username || secondaryTaxPortal.password) {
      if (!secondaryTaxPortal.username) {
        newCredentialErrors.secondary = { ...newCredentialErrors.secondary, username: 'Username is required if password is provided' };
      }
      if (!secondaryTaxPortal.password) {
        newCredentialErrors.secondary = { ...newCredentialErrors.secondary, password: 'Password is required if username is provided' };
      }
    }

    setErrors(newErrors);
    setCredentialErrors(newCredentialErrors);
    
    return Object.keys(newErrors).length === 0 && 
           Object.keys(newCredentialErrors).length === 0;
  };

  const handleDataFetched = (data: any) => {
    setFormData({
      gstin: data.gstin,
      clientName: data.clientName,
      loginId: '',
      contactPerson: data.contactPerson,
      phone: data.phone,
      email: data.email,
      address: data.address
    });
    setDataFetched(true);
    setShowFetcher(false);
  };

  const handleSkipFetcher = () => {
    setShowFetcher(false);
  };

  const handleSubmit = () => {
    if (validateForm()) {
      const clientData: Omit<Client, 'id'> = {
        ...formData,
        addedDate: new Date().toISOString(),
        status: 'active',
        primaryTaxPortal: primaryTaxPortal.username ? primaryTaxPortal : undefined,
        secondaryTaxPortal: secondaryTaxPortal.username ? secondaryTaxPortal : undefined
      };
      onAdd(clientData);
      handleClose();
    }
  };

  const handleClose = () => {
    setFormData({
      gstin: '',
      clientName: '',
      loginId: '',
      contactPerson: '',
      phone: '',
      email: '',
      address: ''
    });
    setPrimaryTaxPortal({ username: '', password: '' });
    setSecondaryTaxPortal({ username: '', password: '' });
    setErrors({});
    setCredentialErrors({});
    setShowFetcher(true);
    setDataFetched(false);
    onClose();
  };

  const handleChange = (field: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, [field]: e.target.value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Typography variant="h6">Add New Client</Typography>
          <IconButton onClick={handleClose}>
            <CloseIcon />
          </IconButton>
        </Stack>
      </DialogTitle>

      <DialogContent>
        <Stack spacing={3} sx={{ mt: 1 }}>
          {showFetcher && (
            <GSTINFetcher
              onDataFetched={handleDataFetched}
              onSkip={handleSkipFetcher}
            />
          )}

          {!showFetcher && (
            <>
              {dataFetched && (
                <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 2 }}>
                  <Chip
                    icon={<AutoAwesomeIcon />}
                    label="Auto-filled from GST Portal"
                    color="success"
                    variant="outlined"
                    size="small"
                  />
                  <Typography variant="caption" color="text.secondary">
                    You can edit the details below if needed
                  </Typography>
                </Stack>
              )}

              <TextField
                label="GSTIN *"
                value={formData.gstin}
                onChange={handleChange('gstin')}
                error={!!errors.gstin}
                helperText={errors.gstin || 'Enter 15-character GSTIN'}
                placeholder="27AAPFU0939F1ZV"
                disabled={dataFetched}
                fullWidth
              />

              <Divider />

              <TextField
                label="Client Name *"
                value={formData.clientName}
                onChange={handleChange('clientName')}
                error={!!errors.clientName}
                helperText={errors.clientName}
                fullWidth
              />

              <TextField
                label="Login ID"
                value={formData.loginId}
                onChange={handleChange('loginId')}
                helperText="GST portal login ID (optional)"
                fullWidth
              />

              <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
                <TextField
                  label="Contact Person *"
                  value={formData.contactPerson}
                  onChange={handleChange('contactPerson')}
                  error={!!errors.contactPerson}
                  helperText={errors.contactPerson}
                  fullWidth
                />

                <TextField
                  label="Phone Number *"
                  value={formData.phone}
                  onChange={handleChange('phone')}
                  error={!!errors.phone}
                  helperText={errors.phone}
                  placeholder="9876543210"
                  fullWidth
                />
              </Stack>

              <TextField
                label="Email Address *"
                type="email"
                value={formData.email}
                onChange={handleChange('email')}
                error={!!errors.email}
                helperText={errors.email}
                fullWidth
              />

              <TextField
                label="Address *"
                value={formData.address}
                onChange={handleChange('address')}
                error={!!errors.address}
                helperText={errors.address}
                multiline
                rows={3}
                fullWidth
              />

              <Divider sx={{ my: 2 }} />

              {/* Tax Portal Credentials Section */}
              <Typography variant="h6" gutterBottom>
                Tax Portal Access Credentials
              </Typography>

              <TaxPortalCredentialsComponent
                title="Primary Tax Portal"
                credentials={primaryTaxPortal}
                onCredentialsChange={setPrimaryTaxPortal}
                errors={credentialErrors.primary}
                required={true}
              />

              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography variant="subtitle1">
                    Secondary Tax Portal (Optional)
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <TaxPortalCredentialsComponent
                    title="Secondary Tax Portal"
                    credentials={secondaryTaxPortal}
                    onCredentialsChange={setSecondaryTaxPortal}
                    errors={credentialErrors.secondary}
                    required={false}
                  />
                </AccordionDetails>
              </Accordion>
            </>
          )}
        </Stack>
      </DialogContent>

      <DialogActions sx={{ p: 3 }}>
        <Button onClick={handleClose} variant="outlined">
          Cancel
        </Button>
        {!showFetcher && (
          <Button onClick={handleSubmit} variant="contained">
            Add Client
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default AddClientModal;