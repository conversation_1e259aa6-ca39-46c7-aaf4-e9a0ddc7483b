"""
Notice schemas for API requests and responses
"""
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, validator
from datetime import datetime
from ..models.notice import NoticeStatus, NoticeType, NoticePriority


class NoticeBase(BaseModel):
    subject: str
    notice_type: NoticeType = NoticeType.GST_NOTICE
    status: NoticeStatus = NoticeStatus.NEW
    priority: NoticePriority = NoticePriority.MEDIUM
    notice_date: datetime
    due_date: Optional[datetime] = None
    description: Optional[str] = None
    content: Optional[str] = None


class NoticeCreate(NoticeBase):
    notice_id: str
    notice_number: Optional[str] = None
    received_date: datetime
    original_filename: Optional[str] = None
    file_path: Optional[str] = None
    file_size: Optional[int] = None
    file_hash: Optional[str] = None
    auto_downloaded: bool = False
    portal_metadata: Optional[Dict[str, Any]] = None
    client_id: int
    
    @validator('notice_id')
    def validate_notice_id(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('Notice ID cannot be empty')
        return v.strip()


class NoticeUpdate(BaseModel):
    subject: Optional[str] = None
    status: Optional[NoticeStatus] = None
    priority: Optional[NoticePriority] = None
    due_date: Optional[datetime] = None
    description: Optional[str] = None
    content: Optional[str] = None
    processed_at: Optional[datetime] = None


class NoticeInDB(NoticeBase):
    id: int
    notice_id: str
    notice_number: Optional[str]
    received_date: datetime
    original_filename: Optional[str]
    file_path: Optional[str]
    file_size: Optional[int]
    file_hash: Optional[str]
    auto_downloaded: bool
    processed_at: Optional[datetime]
    portal_metadata: Optional[Dict[str, Any]]
    client_id: int
    is_active: bool
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class Notice(NoticeInDB):
    pass


class NoticeWithClient(Notice):
    """Notice with client information"""
    client_name: str
    client_gstin: str


class NoticeStats(BaseModel):
    total_notices: int
    new_notices: int
    reviewed_notices: int
    replied_notices: int
    pending_notices: int
    closed_notices: int
    overdue_notices: int
    notices_today: int


class NoticeFilter(BaseModel):
    """Filter parameters for notice queries"""
    client_id: Optional[int] = None
    status: Optional[NoticeStatus] = None
    notice_type: Optional[NoticeType] = None
    priority: Optional[NoticePriority] = None
    from_date: Optional[datetime] = None
    to_date: Optional[datetime] = None
    due_from: Optional[datetime] = None
    due_to: Optional[datetime] = None
    search_term: Optional[str] = None
    auto_downloaded: Optional[bool] = None
    has_file: Optional[bool] = None
    limit: int = 100
    offset: int = 0
    
    @validator('limit')
    def validate_limit(cls, v):
        if v > 1000:
            raise ValueError('Limit cannot exceed 1000')
        return v


class NoticeBulkUpdate(BaseModel):
    """Bulk update notices"""
    notice_ids: List[int]
    status: Optional[NoticeStatus] = None
    priority: Optional[NoticePriority] = None
    
    @validator('notice_ids')
    def validate_notice_ids(cls, v):
        if len(v) == 0:
            raise ValueError('At least one notice ID must be provided')
        if len(v) > 100:
            raise ValueError('Maximum 100 notices can be updated at once')
        return v


class NoticeResponse(BaseModel):
    """Standard notice response"""
    success: bool
    message: str
    data: Optional[Notice] = None
    errors: Optional[List[str]] = None


class NoticeListResponse(BaseModel):
    """Notice list response with pagination"""
    success: bool
    message: str
    data: List[Notice]
    total: int
    limit: int
    offset: int
    has_more: bool


class NoticeSyncResult(BaseModel):
    """Result of notice sync operation"""
    client_id: int
    client_gstin: str
    notices_found: int
    notices_downloaded: int
    notices_failed: int
    errors: List[str]
    sync_duration_seconds: int
    success: bool


class DailyNoticeReport(BaseModel):
    """Daily notice report"""
    date: datetime
    total_clients_synced: int
    total_notices_found: int
    total_notices_downloaded: int
    successful_syncs: int
    failed_syncs: int
    sync_results: List[NoticeSyncResult]
