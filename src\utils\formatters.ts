import { NoticeStatus, EmailStatus, InvoiceType } from '../types/enums';

export const formatDate = (date: Date): string => {
  return new Intl.DateTimeFormat('en-IN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(date);
};

export const formatDateTime = (date: Date): string => {
  return new Intl.DateTimeFormat('en-IN', {
    year: 'numeric',
    month: 'short', 
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
};

export const formatGSTIN = (gstin: string): string => {
  return gstin.toUpperCase().replace(/(.{2})(.{10})(.{3})/, '$1 $2 $3');
};

export const formatPhoneNumber = (phone: string): string => {
  return phone.replace(/(\d{3})(\d{3})(\d{4})/, '+91 $1-$2-$3');
};

export const formatNoticeStatus = (status: NoticeStatus): string => {
  switch (status) {
    case NoticeStatus.NEW:
      return 'New';
    case NoticeStatus.REPLIED:
      return 'Replied';
    case NoticeStatus.PENDING:
      return 'Pending';
    default:
      return status;
  }
};

export const formatEmailStatus = (status: EmailStatus): string => {
  switch (status) {
    case EmailStatus.SENT:
      return 'Sent';
    case EmailStatus.DRAFT:
      return 'Draft';
    case EmailStatus.FAILED:
      return 'Failed';
    default:
      return status;
  }
};

export const formatInvoiceType = (type: InvoiceType): string => {
  switch (type) {
    case InvoiceType.B2B:
      return 'B2B';
    case InvoiceType.B2C:
      return 'B2C';
    case InvoiceType.EXPORT:
      return 'Export';
    case InvoiceType.IMPORT:
      return 'Import';
    default:
      return type;
  }
};

export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR'
  }).format(amount);
};