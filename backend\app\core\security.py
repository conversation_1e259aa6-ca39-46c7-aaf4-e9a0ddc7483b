"""
Security utilities for authentication and authorization
"""
from datetime import datetime, timedelta
from typing import Optional, Union
from jose import JWTError, jwt
from passlib.context import Crypt<PERSON>ontext
from fastapi import HTTPException, status
from .config import settings


# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """
    Create JWT access token
    """
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt


def verify_token(token: str) -> dict:
    """
    Verify and decode JWT token
    """
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        return payload
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )


def get_password_hash(password: str) -> str:
    """
    Hash a password
    """
    return pwd_context.hash(password)


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verify a password against its hash
    """
    return pwd_context.verify(plain_password, hashed_password)


def encrypt_sensitive_data(data: str) -> str:
    """
    Encrypt sensitive data like GST passwords
    Note: In production, use proper encryption like Fernet
    """
    from cryptography.fernet import Fernet
    import base64
    
    # Generate key from settings secret (in production, use dedicated encryption key)
    key = base64.urlsafe_b64encode(settings.SECRET_KEY[:32].encode().ljust(32, b'0'))
    fernet = Fernet(key)
    
    encrypted_data = fernet.encrypt(data.encode())
    return encrypted_data.decode()


def decrypt_sensitive_data(encrypted_data: str) -> str:
    """
    Decrypt sensitive data
    """
    from cryptography.fernet import Fernet
    import base64
    
    # Generate key from settings secret
    key = base64.urlsafe_b64encode(settings.SECRET_KEY[:32].encode().ljust(32, b'0'))
    fernet = Fernet(key)
    
    decrypted_data = fernet.decrypt(encrypted_data.encode())
    return decrypted_data.decode()


def generate_api_key() -> str:
    """
    Generate a secure API key
    """
    import secrets
    return secrets.token_urlsafe(32)


def validate_gstin(gstin: str) -> bool:
    """
    Validate GSTIN format
    """
    import re
    
    # GSTIN format: 2 digits (state code) + 10 alphanumeric + 1 check digit + 1 alphabet + 1 number + 1 alphabet
    pattern = r'^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}[Z]{1}[0-9A-Z]{1}$'
    return bool(re.match(pattern, gstin))


def validate_email(email: str) -> bool:
    """
    Validate email format
    """
    import re
    
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))


def validate_phone(phone: str) -> bool:
    """
    Validate Indian phone number format
    """
    import re
    
    # Indian phone number: +91 followed by 10 digits
    pattern = r'^(\+91|91)?[6-9]\d{9}$'
    return bool(re.match(pattern, phone.replace(' ', '').replace('-', '')))


class SecurityUtils:
    """
    Utility class for security operations
    """
    
    @staticmethod
    def mask_sensitive_data(data: str, mask_char: str = "*", visible_chars: int = 4) -> str:
        """
        Mask sensitive data for logging/display
        """
        if len(data) <= visible_chars:
            return mask_char * len(data)
        
        return data[:visible_chars] + mask_char * (len(data) - visible_chars)
    
    @staticmethod
    def generate_session_id() -> str:
        """
        Generate a unique session ID
        """
        import uuid
        return str(uuid.uuid4())
    
    @staticmethod
    def generate_request_id() -> str:
        """
        Generate a unique request ID for tracing
        """
        import uuid
        return str(uuid.uuid4())[:8]
