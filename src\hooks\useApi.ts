/**
 * React hooks for API data management
 */
import { useState, useEffect, useCallback } from 'react';
import {
  apiService,
  BackendDashboardStats,
  FrontendDashboardStats,
  ApiClient,
  ApiNotice,
  transformDashboardStats,
  transformClient,
  transformNotice
} from '../services/api';

// Dashboard hook
export const useDashboardStats = () => {
  const [stats, setStats] = useState<FrontendDashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchStats = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      console.log('🔄 Fetching dashboard stats from backend...');
      const backendStats = await apiService.getDashboardStats();
      console.log('✅ Dashboard stats received:', backendStats);
      const transformedStats = transformDashboardStats(backendStats);
      console.log('🔄 Transformed stats:', transformedStats);
      console.log('📊 Expected format: { totalClients, noticesToday, repliesSent, invoicesDownloaded }');
      setStats(transformedStats);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch dashboard stats');
      console.error('❌ Dashboard stats error:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  return { stats, loading, error, refetch: fetchStats };
};

// Clients hook
export const useClients = () => {
  const [clients, setClients] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchClients = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const backendClients = await apiService.getClients();
      const transformedClients = backendClients.map(transformClient);
      setClients(transformedClients);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch clients');
      console.error('Clients error:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchClients();
  }, [fetchClients]);

  return { clients, loading, error, refetch: fetchClients };
};

// Notices hook
export const useNotices = () => {
  const [notices, setNotices] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchNotices = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await apiService.getNotices();
      const transformedNotices = response.data.map(transformNotice);
      setNotices(transformedNotices);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch notices');
      console.error('Notices error:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchNotices();
  }, [fetchNotices]);

  return { notices, loading, error, refetch: fetchNotices };
};

// Sync status hook
export const useSyncStatus = () => {
  const [syncStatus, setSyncStatus] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchSyncStatus = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const status = await apiService.getSyncStatus();
      setSyncStatus(status);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch sync status');
      console.error('Sync status error:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  const triggerSync = useCallback(async (clientIds: number[]) => {
    try {
      await apiService.triggerManualSync(clientIds);
      await fetchSyncStatus(); // Refresh status after sync
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to trigger sync');
      console.error('Sync trigger error:', err);
      return false;
    }
  }, [fetchSyncStatus]);

  const syncAll = useCallback(async () => {
    try {
      await apiService.syncAllClients();
      await fetchSyncStatus(); // Refresh status after sync
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to sync all clients');
      console.error('Sync all error:', err);
      return false;
    }
  }, [fetchSyncStatus]);

  useEffect(() => {
    fetchSyncStatus();
  }, [fetchSyncStatus]);

  return { 
    syncStatus, 
    loading, 
    error, 
    refetch: fetchSyncStatus,
    triggerSync,
    syncAll
  };
};

// Backend connection hook
export const useBackendConnection = () => {
  const [isConnected, setIsConnected] = useState<boolean | null>(null);
  const [loading, setLoading] = useState(true);

  const checkConnection = useCallback(async () => {
    try {
      setLoading(true);
      console.log('🔄 Checking backend connection...');
      const connected = await apiService.testConnection();
      console.log(connected ? '✅ Backend connected!' : '❌ Backend disconnected');
      setIsConnected(connected);
    } catch (err) {
      setIsConnected(false);
      console.error('❌ Backend connection check failed:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    checkConnection();
    
    // Check connection every 30 seconds
    const interval = setInterval(checkConnection, 30000);
    
    return () => clearInterval(interval);
  }, [checkConnection]);

  return { isConnected, loading, checkConnection };
};
