"""
Email reply model for managing notice responses
"""
from sqlalchemy import <PERSON>um<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, DateTime, Text, Enum as S<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, JSO<PERSON>
from sqlalchemy.orm import relationship
import enum
from .base import BaseModel


class EmailStatus(str, enum.Enum):
    DRAFT = "draft"
    PENDING = "pending"
    SENT = "sent"
    FAILED = "failed"
    BOUNCED = "bounced"


class EmailTemplate(str, enum.Enum):
    ACKNOWLEDGMENT = "acknowledgment"
    REQUEST_EXTENSION = "request_extension"
    DOCUMENT_SUBMISSION = "document_submission"
    CLARIFICATION = "clarification"
    OBJECTION = "objection"
    CUSTOM = "custom"


class EmailReply(BaseModel):
    __tablename__ = "email_replies"
    
    # Email Information
    subject = Column(String(500), nullable=False)
    body = Column(Text, nullable=False)
    template_used = Column(SQLEnum(EmailTemplate), nullable=True)
    
    # Recipients
    to_email = Column(String(255), nullable=False)
    cc_emails = Column(JSON, nullable=True)  # List of CC emails
    bcc_emails = Column(JSON, nullable=True)  # List of BCC emails
    
    # Status and Tracking
    status = Column(SQLEnum(EmailStatus), default=EmailStatus.DRAFT, nullable=False)
    sent_at = Column(DateTime(timezone=True), nullable=True)
    delivery_status = Column(String(100), nullable=True)
    
    # Attachments
    attachments = Column(JSON, nullable=True)  # List of attachment file paths
    
    # Auto-generation info
    auto_generated = Column(Boolean, default=False, nullable=False)
    reviewed_by_user = Column(Boolean, default=False, nullable=False)
    
    # Email metadata
    message_id = Column(String(255), nullable=True)  # Email message ID for tracking
    thread_id = Column(String(255), nullable=True)  # For email threading
    
    # Error handling
    error_message = Column(Text, nullable=True)
    retry_count = Column(Integer, default=0, nullable=False)
    
    # Foreign Keys
    notice_id = Column(Integer, ForeignKey("notices.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Relationships
    notice = relationship("Notice", back_populates="email_replies")
    user = relationship("User", back_populates="email_replies")
    
    def __repr__(self):
        return f"<EmailReply(subject='{self.subject[:50]}...', status='{self.status}')>"
