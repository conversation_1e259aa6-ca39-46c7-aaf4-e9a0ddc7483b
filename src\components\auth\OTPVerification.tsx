import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  CardContent,
  <PERSON><PERSON>graphy,
  Button,
  TextField,
  Stack,
  Box,
  CircularProgress,
  Link
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import SecurityIcon from '@mui/icons-material/Security';

interface OTPVerificationProps {
  phoneNumber: string;
  onVerify: (otp: string) => void;
  onResend: () => void;
  onBack: () => void;
  loading: boolean;
}

const OTPVerification: React.FC<OTPVerificationProps> = ({
  phoneNumber,
  onVerify,
  onResend,
  onBack,
  loading
}) => {
  const [otp, setOtp] = useState('');
  const [timer, setTimer] = useState(30);
  const [canResend, setCanResend] = useState(false);

  useEffect(() => {
    if (timer > 0) {
      const interval = setInterval(() => {
        setTimer(timer - 1);
      }, 1000);
      return () => clearInterval(interval);
    } else {
      setCanResend(true);
    }
  }, [timer]);

  const handleOtpChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, '').slice(0, 6);
    setOtp(value);
  };

  const handleVerify = () => {
    if (otp.length === 6) {
      onVerify(otp);
    }
  };

  const handleResend = () => {
    onResend();
    setTimer(30);
    setCanResend(false);
    setOtp('');
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        bgcolor: 'background.default',
        p: 2
      }}
    >
      <Card sx={{ maxWidth: 400, width: '100%', boxShadow: 3 }}>
        <CardContent sx={{ p: 4 }}>
          <Stack spacing={3} alignItems="center">
            <Button
              startIcon={<ArrowBackIcon />}
              onClick={onBack}
              sx={{ alignSelf: 'flex-start' }}
            >
              Back
            </Button>

            <SecurityIcon color="primary" sx={{ fontSize: 48 }} />

            <Typography variant="h5" component="h1" textAlign="center">
              Verify OTP
            </Typography>

            <Typography variant="body2" color="text.secondary" textAlign="center">
              We've sent a 6-digit code to
              <br />
              <strong>+91 {phoneNumber}</strong>
            </Typography>

            <TextField
              label="Enter OTP"
              value={otp}
              onChange={handleOtpChange}
              placeholder="123456"
              inputProps={{
                style: { textAlign: 'center', fontSize: '1.5rem', letterSpacing: '0.5rem' }
              }}
              fullWidth
            />

            <Button
              variant="contained"
              fullWidth
              size="large"
              onClick={handleVerify}
              disabled={loading || otp.length !== 6}
              sx={{ py: 1.5 }}
            >
              {loading ? <CircularProgress size={24} color="inherit" /> : 'Verify OTP'}
            </Button>

            <Stack direction="row" spacing={1} alignItems="center">
              <Typography variant="body2" color="text.secondary">
                Didn't receive code?
              </Typography>
              {canResend ? (
                <Link
                  component="button"
                  variant="body2"
                  onClick={handleResend}
                  sx={{ textDecoration: 'none' }}
                >
                  Resend OTP
                </Link>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  Resend in {timer}s
                </Typography>
              )}
            </Stack>
          </Stack>
        </CardContent>
      </Card>
    </Box>
  );
};

export default OTPVerification;