"""
Celery tasks for notice synchronization
"""
from celery import current_task
from sqlalchemy.orm import Session
from ..celery_app import celery_app
from ..core.database import get_db
from ..models import Client, Notice, NoticeStatus, SyncLog
from ..services.notice_service import notice_service
from ..services.settings_service import settings_service
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)


@celery_app.task(bind=True)
def daily_notice_sync(self):
    """Daily task to sync notices for all active clients"""
    task_id = self.request.id
    logger.info(f"Starting daily notice sync (Task ID: {task_id})")
    
    db = next(get_db())
    
    # Create sync log
    sync_log = SyncLog(
        sync_id=f"daily_{int(datetime.utcnow().timestamp())}",
        sync_type="scheduled",
        started_at=datetime.utcnow(),
        status="running"
    )
    db.add(sync_log)
    db.commit()
    
    try:
        # Get all active clients with auto sync enabled
        clients = db.query(Client).filter(
            Client.is_active == True,
            Client.auto_sync_enabled == True
        ).all()
        
        sync_log.clients_processed = len(clients)
        db.commit()
        
        total_notices_found = 0
        total_notices_downloaded = 0
        total_errors = 0
        
        for i, client in enumerate(clients):
            try:
                # Update task progress
                current_task.update_state(
                    state="PROGRESS",
                    meta={
                        "current": i + 1,
                        "total": len(clients),
                        "client": client.client_name
                    }
                )
                
                # Check if sync is due
                if client.last_successful_sync_at:
                    next_sync = client.last_successful_sync_at + timedelta(hours=client.sync_interval_hours)
                    if datetime.utcnow() < next_sync:
                        logger.info(f"Sync not due for client {client.gstin}, skipping")
                        continue
                
                # Perform sync
                notices_found, notices_downloaded, errors = await notice_service.sync_new_notices(client, db=db)
                
                total_notices_found += notices_found
                total_notices_downloaded += notices_downloaded
                total_errors += len(errors)
                
                logger.info(f"Synced client {client.gstin}: {notices_downloaded} new notices")
                
            except Exception as e:
                logger.error(f"Failed to sync client {client.gstin}: {str(e)}")
                total_errors += 1
        
        # Update sync log
        sync_log.completed_at = datetime.utcnow()
        sync_log.status = "completed" if total_errors == 0 else "partial"
        sync_log.notices_found = total_notices_found
        sync_log.notices_downloaded = total_notices_downloaded
        sync_log.notices_failed = total_errors
        sync_log.total_duration_seconds = int(
            (sync_log.completed_at - sync_log.started_at).total_seconds()
        )
        
        if total_errors > 0:
            sync_log.error_message = f"{total_errors} clients failed to sync"
        
        db.commit()
        
        logger.info(f"Daily sync completed: {total_notices_downloaded} notices downloaded, {total_errors} errors")
        
        return {
            "status": "completed",
            "clients_processed": len(clients),
            "notices_found": total_notices_found,
            "notices_downloaded": total_notices_downloaded,
            "errors": total_errors
        }
        
    except Exception as e:
        sync_log.completed_at = datetime.utcnow()
        sync_log.status = "failed"
        sync_log.error_message = str(e)
        db.commit()
        
        logger.error(f"Daily sync failed: {str(e)}")
        raise
    
    finally:
        db.close()


@celery_app.task(bind=True)
def sync_client_notices(self, client_id: int, force_sync: bool = False):
    """Task to sync notices for a specific client"""
    task_id = self.request.id
    logger.info(f"Starting client sync for client {client_id} (Task ID: {task_id})")
    
    db = next(get_db())
    
    try:
        client = db.query(Client).filter(Client.id == client_id).first()
        
        if not client:
            raise ValueError(f"Client {client_id} not found")
        
        if not client.is_active:
            raise ValueError(f"Client {client_id} is not active")
        
        # Check if sync is needed (unless forced)
        if not force_sync and client.last_successful_sync_at:
            next_sync = client.last_successful_sync_at + timedelta(hours=client.sync_interval_hours)
            if datetime.utcnow() < next_sync:
                logger.info(f"Sync not due for client {client.gstin}, skipping")
                return {"status": "skipped", "reason": "sync_not_due"}
        
        # Perform sync
        notices_found, notices_downloaded, errors = await notice_service.sync_new_notices(client, db=db)
        
        logger.info(f"Client sync completed for {client.gstin}: {notices_downloaded} new notices")
        
        return {
            "status": "completed",
            "client_id": client_id,
            "client_gstin": client.gstin,
            "notices_found": notices_found,
            "notices_downloaded": notices_downloaded,
            "errors": len(errors)
        }
        
    except Exception as e:
        logger.error(f"Client sync failed for {client_id}: {str(e)}")
        raise
    
    finally:
        db.close()


@celery_app.task
def check_urgent_notices():
    """Check for urgent notices that need immediate attention"""
    logger.info("Checking for urgent notices")
    
    db = next(get_db())
    
    try:
        # Find notices due within 24 hours that haven't been replied to
        tomorrow = datetime.utcnow() + timedelta(days=1)
        
        urgent_notices = db.query(Notice).filter(
            Notice.due_date <= tomorrow,
            Notice.due_date > datetime.utcnow(),
            Notice.status.in_([NoticeStatus.NEW, NoticeStatus.PENDING])
        ).all()
        
        if urgent_notices:
            logger.warning(f"Found {len(urgent_notices)} urgent notices")
            
            # TODO: Send notifications to users
            # This could trigger email alerts or push notifications
            
        return {
            "urgent_notices_count": len(urgent_notices),
            "notice_ids": [notice.id for notice in urgent_notices]
        }
        
    except Exception as e:
        logger.error(f"Failed to check urgent notices: {str(e)}")
        raise
    
    finally:
        db.close()


@celery_app.task(bind=True)
def initial_client_sync(self, client_id: int, years_back: int = None):
    """Task for initial notice download for new clients"""
    task_id = self.request.id
    logger.info(f"Starting initial sync for client {client_id} (Task ID: {task_id})")
    
    db = next(get_db())
    
    try:
        client = db.query(Client).filter(Client.id == client_id).first()
        
        if not client:
            raise ValueError(f"Client {client_id} not found")
        
        # Update task progress
        current_task.update_state(
            state="PROGRESS",
            meta={"status": "starting", "client": client.client_name}
        )
        
        # Perform initial download
        years_back = years_back or settings_service.get_max_fetch_years()
        notices_found, notices_downloaded, errors = await notice_service.download_initial_notices(
            client, years_back=years_back, db=db
        )
        
        logger.info(f"Initial sync completed for {client.gstin}: {notices_downloaded}/{notices_found} notices")
        
        return {
            "status": "completed",
            "client_id": client_id,
            "client_gstin": client.gstin,
            "notices_found": notices_found,
            "notices_downloaded": notices_downloaded,
            "errors": len(errors)
        }
        
    except Exception as e:
        logger.error(f"Initial sync failed for client {client_id}: {str(e)}")
        raise
    
    finally:
        db.close()
