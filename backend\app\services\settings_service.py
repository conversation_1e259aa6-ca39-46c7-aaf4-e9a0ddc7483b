"""
Settings service for managing application settings
"""
from typing import Any, Optional, Dict
from sqlalchemy.orm import Session
from ..core.database import get_db
from ..models import Setting, SettingType, DEFAULT_SETTINGS
import logging

logger = logging.getLogger(__name__)


class SettingsService:
    """Service for managing application settings"""
    
    def __init__(self):
        self._cache = {}
    
    async def initialize_default_settings(self, db: Session = None):
        """Initialize default settings in database"""
        if not db:
            db = next(get_db())
        
        try:
            for key, config in DEFAULT_SETTINGS.items():
                existing_setting = db.query(Setting).filter(Setting.key == key).first()
                
                if not existing_setting:
                    setting = Setting(
                        key=key,
                        category=config["category"],
                        value=config["value"],
                        description=config["description"],
                        is_public=config.get("is_public", False)
                    )
                    db.add(setting)
            
            db.commit()
            logger.info("Default settings initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize default settings: {str(e)}")
            db.rollback()
            raise
        finally:
            if db:
                db.close()
    
    def get_setting(self, key: str, default: Any = None, db: Session = None) -> Any:
        """Get setting value"""
        # Check cache first
        if key in self._cache:
            return self._cache[key]
        
        if not db:
            db = next(get_db())
        
        try:
            setting = db.query(Setting).filter(Setting.key == key).first()
            
            if setting:
                value = setting.json_value if setting.json_value is not None else setting.value
                
                # Convert string values to appropriate types
                if isinstance(value, str):
                    if value.lower() in ('true', 'false'):
                        value = value.lower() == 'true'
                    elif value.isdigit():
                        value = int(value)
                    elif self._is_float(value):
                        value = float(value)
                
                # Cache the value
                self._cache[key] = value
                return value
            
            return default
            
        except Exception as e:
            logger.error(f"Failed to get setting {key}: {str(e)}")
            return default
        finally:
            if db:
                db.close()
    
    def set_setting(self, key: str, value: Any, db: Session = None) -> bool:
        """Set setting value"""
        if not db:
            db = next(get_db())
        
        try:
            setting = db.query(Setting).filter(Setting.key == key).first()
            
            if setting:
                # Update existing setting
                if isinstance(value, (dict, list)):
                    setting.json_value = value
                    setting.value = None
                else:
                    setting.value = str(value)
                    setting.json_value = None
            else:
                # Create new setting
                setting = Setting(
                    key=key,
                    category=SettingType.SYSTEM,
                    description=f"Auto-created setting: {key}"
                )
                
                if isinstance(value, (dict, list)):
                    setting.json_value = value
                else:
                    setting.value = str(value)
                
                db.add(setting)
            
            db.commit()
            
            # Update cache
            self._cache[key] = value
            
            logger.info(f"Setting updated: {key}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to set setting {key}: {str(e)}")
            db.rollback()
            return False
        finally:
            if db:
                db.close()
    
    def get_settings_by_category(self, category: SettingType, db: Session = None) -> Dict[str, Any]:
        """Get all settings for a category"""
        if not db:
            db = next(get_db())
        
        try:
            settings = db.query(Setting).filter(Setting.category == category).all()
            
            result = {}
            for setting in settings:
                value = setting.json_value if setting.json_value is not None else setting.value
                
                # Convert string values
                if isinstance(value, str):
                    if value.lower() in ('true', 'false'):
                        value = value.lower() == 'true'
                    elif value.isdigit():
                        value = int(value)
                    elif self._is_float(value):
                        value = float(value)
                
                result[setting.key] = value
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to get settings for category {category}: {str(e)}")
            return {}
        finally:
            if db:
                db.close()
    
    def clear_cache(self):
        """Clear settings cache"""
        self._cache.clear()
        logger.info("Settings cache cleared")
    
    def _is_float(self, value: str) -> bool:
        """Check if string can be converted to float"""
        try:
            float(value)
            return True
        except ValueError:
            return False
    
    # Convenience methods for common settings
    def get_fetch_interval_hours(self) -> int:
        """Get notice fetch interval in hours"""
        return self.get_setting("gst.fetch_interval_hours", 24)
    
    def get_max_fetch_years(self) -> int:
        """Get maximum years to fetch for new clients"""
        return self.get_setting("gst.max_fetch_years", 2)
    
    def get_batch_size(self) -> int:
        """Get batch size for processing"""
        return self.get_setting("gst.batch_size", 100)
    
    def is_auto_sync_enabled(self) -> bool:
        """Check if auto sync is enabled globally"""
        return self.get_setting("sync.auto_sync_enabled", True)
    
    def get_smtp_settings(self) -> Dict[str, Any]:
        """Get SMTP settings"""
        return {
            "host": self.get_setting("email.smtp_host", "smtp.gmail.com"),
            "port": self.get_setting("email.smtp_port", 587),
            "use_tls": self.get_setting("email.use_tls", True),
            "from_name": self.get_setting("email.default_from_name", "GST Notice System")
        }


# Global instance
settings_service = SettingsService()
