# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/gst_notice_db
DATABASE_URL_ASYNC=postgresql+asyncpg://username:password@localhost:5432/gst_notice_db

# Redis Configuration (for Celery)
REDIS_URL=redis://localhost:6379/0

# JWT Configuration
SECRET_KEY=your-super-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# GST Portal Configuration
GST_PORTAL_BASE_URL=https://services.gst.gov.in
GST_API_KEY=your-gst-api-key
GST_CLIENT_ID=your-gst-client-id
GST_CLIENT_SECRET=your-gst-client-secret

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_USE_TLS=true

# Application Configuration
APP_NAME=GST Notice Management System
APP_VERSION=1.0.0
DEBUG=true
ENVIRONMENT=development

# File Storage
UPLOAD_DIR=./uploads
NOTICE_STORAGE_DIR=./notices
MAX_FILE_SIZE=10485760  # 10MB

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# Logging
LOG_LEVEL=INFO
LOG_FILE=./logs/app.log

# Security
CORS_ORIGINS=["http://localhost:3000", "http://localhost:5173"]
ALLOWED_HOSTS=["localhost", "127.0.0.1"]

# Notice Fetch Configuration
DEFAULT_FETCH_INTERVAL_HOURS=24
MAX_NOTICE_FETCH_YEARS=2
BATCH_SIZE=100
