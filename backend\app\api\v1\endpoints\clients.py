"""
Client management endpoints
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc
from ....core.database import get_db
from ....core.security import encrypt_sensitive_data
from ....models import Client, User
from ....schemas.client import (
    ClientCreate, 
    ClientUpdate, 
    Client as ClientSchema,
    ClientStats,
    ClientSyncStatus,
    BulkClientUpload,
    ClientSyncRequest
)
from ....services.notice_service import notice_service
from ..endpoints.auth import get_current_active_user, get_current_admin_user
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/", response_model=ClientSchema)
async def create_client(
    client_data: ClientCreate,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Create a new client and start initial notice download"""
    # Check if client with GSTIN already exists
    existing_client = db.query(Client).filter(Client.gstin == client_data.gstin).first()
    if existing_client:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Client with this GSTIN already exists"
        )
    
    # Encrypt GST passwords
    encrypted_password = encrypt_sensitive_data(client_data.gst_password)
    encrypted_backup_password = None
    if client_data.gst_password_backup:
        encrypted_backup_password = encrypt_sensitive_data(client_data.gst_password_backup)
    
    # Create client
    client = Client(
        gstin=client_data.gstin,
        client_name=client_data.client_name,
        contact_person=client_data.contact_person,
        email=client_data.email,
        phone=client_data.phone,
        address=client_data.address,
        gst_username=client_data.gst_username,
        gst_password_encrypted=encrypted_password,
        gst_username_backup=client_data.gst_username_backup,
        gst_password_backup_encrypted=encrypted_backup_password,
        auto_sync_enabled=client_data.auto_sync_enabled,
        sync_interval_hours=client_data.sync_interval_hours,
        user_id=current_user.id
    )
    
    db.add(client)
    db.commit()
    db.refresh(client)
    
    # Start initial notice download in background
    background_tasks.add_task(
        download_initial_notices_task,
        client.id,
        current_user.id
    )
    
    logger.info(f"Client created: {client.gstin} by user {current_user.email}")
    return client


@router.get("/", response_model=List[ClientSchema])
async def get_clients(
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = None,
    active_only: bool = True,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get list of clients"""
    query = db.query(Client).filter(Client.user_id == current_user.id)
    
    if active_only:
        query = query.filter(Client.is_active == True)
    
    if search:
        search_filter = or_(
            Client.client_name.ilike(f"%{search}%"),
            Client.gstin.ilike(f"%{search}%"),
            Client.contact_person.ilike(f"%{search}%"),
            Client.email.ilike(f"%{search}%")
        )
        query = query.filter(search_filter)
    
    clients = query.order_by(desc(Client.created_at)).offset(skip).limit(limit).all()
    return clients


@router.get("/{client_id}", response_model=ClientSchema)
async def get_client(
    client_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get specific client"""
    client = db.query(Client).filter(
        and_(Client.id == client_id, Client.user_id == current_user.id)
    ).first()
    
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Client not found"
        )
    
    return client


@router.put("/{client_id}", response_model=ClientSchema)
async def update_client(
    client_id: int,
    client_data: ClientUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update client information"""
    client = db.query(Client).filter(
        and_(Client.id == client_id, Client.user_id == current_user.id)
    ).first()
    
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Client not found"
        )
    
    # Update fields
    update_data = client_data.dict(exclude_unset=True)
    
    # Handle password encryption
    if "gst_password" in update_data:
        update_data["gst_password_encrypted"] = encrypt_sensitive_data(update_data.pop("gst_password"))
    
    if "gst_password_backup" in update_data:
        update_data["gst_password_backup_encrypted"] = encrypt_sensitive_data(update_data.pop("gst_password_backup"))
    
    for field, value in update_data.items():
        setattr(client, field, value)
    
    db.commit()
    db.refresh(client)
    
    logger.info(f"Client updated: {client.gstin} by user {current_user.email}")
    return client


@router.delete("/{client_id}")
async def delete_client(
    client_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete (deactivate) client"""
    client = db.query(Client).filter(
        and_(Client.id == client_id, Client.user_id == current_user.id)
    ).first()
    
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Client not found"
        )
    
    # Soft delete
    client.is_active = False
    db.commit()
    
    logger.info(f"Client deleted: {client.gstin} by user {current_user.email}")
    return {"message": "Client deleted successfully"}


@router.get("/stats/overview", response_model=ClientStats)
async def get_client_stats(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get client statistics"""
    from datetime import datetime, timedelta
    
    base_query = db.query(Client).filter(Client.user_id == current_user.id)
    
    total_clients = base_query.count()
    active_clients = base_query.filter(Client.is_active == True).count()
    clients_with_pending_replies = base_query.filter(Client.pending_replies > 0).count()
    
    # Clients synced today
    today = datetime.utcnow().date()
    clients_synced_today = base_query.filter(
        Client.last_sync_at >= today
    ).count()
    
    # Clients never synced
    clients_never_synced = base_query.filter(
        Client.last_sync_at.is_(None)
    ).count()
    
    # Clients with sync errors (failed sync more than 24 hours ago)
    yesterday = datetime.utcnow() - timedelta(hours=24)
    clients_with_sync_errors = base_query.filter(
        and_(
            Client.last_sync_at < yesterday,
            or_(
                Client.last_successful_sync_at.is_(None),
                Client.last_successful_sync_at < Client.last_sync_at
            )
        )
    ).count()
    
    return ClientStats(
        total_clients=total_clients,
        active_clients=active_clients,
        clients_with_pending_replies=clients_with_pending_replies,
        clients_synced_today=clients_synced_today,
        clients_never_synced=clients_never_synced,
        clients_with_sync_errors=clients_with_sync_errors
    )


@router.get("/{client_id}/sync-status", response_model=ClientSyncStatus)
async def get_client_sync_status(
    client_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get client sync status"""
    from datetime import datetime, timedelta
    
    client = db.query(Client).filter(
        and_(Client.id == client_id, Client.user_id == current_user.id)
    ).first()
    
    if not client:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Client not found"
        )
    
    # Determine sync status
    sync_status = "never"
    if client.last_sync_at:
        if client.last_successful_sync_at and client.last_successful_sync_at >= client.last_sync_at:
            sync_status = "success"
        else:
            sync_status = "failed"
    
    # Calculate next sync due
    next_sync_due = None
    if client.auto_sync_enabled and client.last_successful_sync_at:
        next_sync_due = client.last_successful_sync_at + timedelta(hours=client.sync_interval_hours)
    
    return ClientSyncStatus(
        client_id=client.id,
        gstin=client.gstin,
        client_name=client.client_name,
        last_sync_at=client.last_sync_at,
        last_successful_sync_at=client.last_successful_sync_at,
        sync_status=sync_status,
        next_sync_due=next_sync_due,
        auto_sync_enabled=client.auto_sync_enabled,
        total_notices=client.total_notices,
        pending_replies=client.pending_replies
    )


@router.post("/bulk-upload")
async def bulk_upload_clients(
    upload_data: BulkClientUpload,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Bulk upload clients (admin only)"""
    created_clients = []
    errors = []
    
    for client_data in upload_data.clients:
        try:
            # Check if client already exists
            existing_client = db.query(Client).filter(Client.gstin == client_data.gstin).first()
            if existing_client:
                errors.append(f"Client with GSTIN {client_data.gstin} already exists")
                continue
            
            # Create client (similar to create_client)
            encrypted_password = encrypt_sensitive_data(client_data.gst_password)
            encrypted_backup_password = None
            if client_data.gst_password_backup:
                encrypted_backup_password = encrypt_sensitive_data(client_data.gst_password_backup)
            
            client = Client(
                gstin=client_data.gstin,
                client_name=client_data.client_name,
                contact_person=client_data.contact_person,
                email=client_data.email,
                phone=client_data.phone,
                address=client_data.address,
                gst_username=client_data.gst_username,
                gst_password_encrypted=encrypted_password,
                gst_username_backup=client_data.gst_username_backup,
                gst_password_backup_encrypted=encrypted_backup_password,
                auto_sync_enabled=client_data.auto_sync_enabled,
                sync_interval_hours=client_data.sync_interval_hours,
                user_id=current_user.id
            )
            
            db.add(client)
            db.flush()  # Get the ID without committing
            created_clients.append(client.id)
            
        except Exception as e:
            errors.append(f"Failed to create client {client_data.gstin}: {str(e)}")
    
    db.commit()
    
    # Start initial downloads for all created clients
    for client_id in created_clients:
        background_tasks.add_task(
            download_initial_notices_task,
            client_id,
            current_user.id
        )
    
    logger.info(f"Bulk upload completed: {len(created_clients)} clients created, {len(errors)} errors")
    
    return {
        "message": f"Bulk upload completed",
        "created_count": len(created_clients),
        "error_count": len(errors),
        "errors": errors
    }


async def download_initial_notices_task(client_id: int, user_id: int):
    """Background task to download initial notices for a client"""
    try:
        db = next(get_db())
        client = db.query(Client).filter(Client.id == client_id).first()
        
        if client:
            await notice_service.download_initial_notices(client, db=db)
            logger.info(f"Initial notice download completed for client {client.gstin}")
        
    except Exception as e:
        logger.error(f"Initial notice download failed for client {client_id}: {str(e)}")
    finally:
        db.close()
