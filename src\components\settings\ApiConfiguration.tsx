import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Di<PERSON>r,
  Icon<PERSON>utton,
  Toolt<PERSON>
} from '@mui/material';
import {
  Settings as SettingsIcon,
  CheckCircle as CheckIcon,
  <PERSON>rror as <PERSON><PERSON>rIcon,
  Refresh as RefreshIcon,
  ContentCopy as CopyIcon
} from '@mui/icons-material';

interface ApiConfigurationProps {
  onApiConfigured?: (config: any) => void;
}

const ApiConfiguration: React.FC<ApiConfigurationProps> = ({ onApiConfigured }) => {
  const [apiUrl, setApiUrl] = useState(process.env.REACT_APP_ERPCA_API_URL || 'https://api.whitebooks.in');
  const [clientId, setClientId] = useState(process.env.REACT_APP_ERPCA_CLIENT_ID || 'GSTP761cfe60-b2b9-4590-8992-25537b2f71dc');
  const [clientSecret, setClientSecret] = useState(process.env.REACT_APP_ERPCA_CLIENT_SECRET || 'GSTP49dec62c-6415-47c1-9746-112764f40ab2');
  const [testEmail, setTestEmail] = useState(process.env.REACT_APP_DEFAULT_EMAIL || '<EMAIL>');
  const [testGstin, setTestGstin] = useState(process.env.REACT_APP_DEFAULT_GSTIN || '27AADCG2914R1Z7');
  const [testing, setTesting] = useState(false);
  const [testResult, setTestResult] = useState<'success' | 'error' | null>(null);
  const [testMessage, setTestMessage] = useState('');

  const testApiConnection = async () => {
    setTesting(true);
    setTestResult(null);
    setTestMessage('');

    try {
      // Test the Whitebooks API connection
      const testUrl = `${apiUrl}/public/search?email=${encodeURIComponent(testEmail)}&gstin=${encodeURIComponent(testGstin)}`;

      const response = await fetch(testUrl, {
        method: 'GET',
        headers: {
          'client_id': clientId,
          'client_secret': clientSecret,
          'Content-Type': 'application/json',
        },
        mode: 'cors',
      });

      if (response.ok) {
        const data = await response.json();
        setTestResult('success');
        setTestMessage(`Whitebooks API connection successful! Retrieved data for GSTIN: ${data.gstin || testGstin}`);

        if (onApiConfigured) {
          onApiConfigured({ apiUrl, clientId, clientSecret, testEmail, testGstin });
        }
      } else {
        const errorText = await response.text();
        setTestResult('error');
        setTestMessage(`API connection failed: ${response.status} - ${response.statusText}. ${errorText}`);
      }
    } catch (error: any) {
      setTestResult('error');
      setTestMessage(`API connection error: ${error.message}`);
    } finally {
      setTesting(false);
    }
  };

  const copyEnvConfig = () => {
    const envConfig = `# Whitebooks API Configuration
REACT_APP_ERPCA_API_URL=${apiUrl}
REACT_APP_ERPCA_CLIENT_ID=${clientId}
REACT_APP_ERPCA_CLIENT_SECRET=${clientSecret}
REACT_APP_DEFAULT_EMAIL=${testEmail}
REACT_APP_DEFAULT_GSTIN=${testGstin}
REACT_APP_USE_MOCK_DATA=false`;

    navigator.clipboard.writeText(envConfig);
    alert('Environment configuration copied to clipboard!');
  };

  return (
    <Card>
      <CardContent>
        <Stack spacing={3}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <SettingsIcon color="primary" />
            <Typography variant="h6" color="primary">
              Whitebooks API Configuration
            </Typography>
          </Box>

          <Alert severity="info">
            Configure your Whitebooks API connection for real client data fetching.
          </Alert>

          <Stack spacing={2}>
            <TextField
              label="API Base URL"
              value={apiUrl}
              onChange={(e) => setApiUrl(e.target.value)}
              placeholder="https://api.whitebooks.in"
              helperText="The Whitebooks API base URL"
              fullWidth
            />

            <Box sx={{ display: 'flex', gap: 2 }}>
              <TextField
                label="Client ID"
                value={clientId}
                onChange={(e) => setClientId(e.target.value)}
                placeholder="GSTP761cfe60-b2b9-4590-8992-25537b2f71dc"
                sx={{ flex: 1 }}
              />
              <TextField
                label="Client Secret"
                value={clientSecret}
                onChange={(e) => setClientSecret(e.target.value)}
                placeholder="GSTP49dec62c-6415-47c1-9746-112764f40ab2"
                type="password"
                sx={{ flex: 1 }}
              />
            </Box>

            <Divider />

            <Typography variant="subtitle2" color="text.secondary">
              Test Credentials
            </Typography>

            <Box sx={{ display: 'flex', gap: 2 }}>
              <TextField
                label="Test Email"
                value={testEmail}
                onChange={(e) => setTestEmail(e.target.value)}
                placeholder="<EMAIL>"
                sx={{ flex: 1 }}
              />
              <TextField
                label="Test GSTIN"
                value={testGstin}
                onChange={(e) => setTestGstin(e.target.value)}
                placeholder="27AADCG2914R1Z7"
                sx={{ flex: 1 }}
              />
            </Box>
          </Stack>

          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
            <Button
              variant="contained"
              onClick={testApiConnection}
              disabled={!apiUrl || testing}
              startIcon={testing ? <RefreshIcon /> : <CheckIcon />}
            >
              {testing ? 'Testing...' : 'Test API Connection'}
            </Button>

            <Tooltip title="Copy environment configuration">
              <IconButton onClick={copyEnvConfig} disabled={!apiUrl}>
                <CopyIcon />
              </IconButton>
            </Tooltip>

            <Box sx={{ ml: 'auto' }}>
              {testResult === 'success' && (
                <Chip icon={<CheckIcon />} label="Connected" color="success" size="small" />
              )}
              {testResult === 'error' && (
                <Chip icon={<ErrorIcon />} label="Failed" color="error" size="small" />
              )}
            </Box>
          </Box>

          {testMessage && (
            <Alert severity={testResult === 'success' ? 'success' : 'error'}>
              {testMessage}
            </Alert>
          )}

          <Alert severity="warning">
            <Typography variant="body2">
              <strong>Setup Instructions:</strong>
              <br />
              1. Enter your actual API base URL from Postman
              <br />
              2. Verify the API ID and Pass match your Postman collection
              <br />
              3. Test the connection with your credentials
              <br />
              4. Copy the environment configuration to your .env file
            </Typography>
          </Alert>
        </Stack>
      </CardContent>
    </Card>
  );
};

export default ApiConfiguration;
