"""
User model for authentication and authorization
"""
from sqlalchemy import Column, String, Boolean, Enum as SQLEnum
from sqlalchemy.orm import relationship
import enum
from .base import BaseModel


class UserRole(str, enum.Enum):
    ADMIN = "admin"
    CONSULTANT = "consultant"
    CLIENT = "client"


class User(BaseModel):
    __tablename__ = "users"
    
    email = Column(String(255), unique=True, index=True, nullable=False)
    username = Column(String(100), unique=True, index=True, nullable=False)
    full_name = Column(String(255), nullable=False)
    hashed_password = Column(String(255), nullable=False)
    phone = Column(String(20), nullable=True)
    role = Column(SQLEnum(UserRole), default=UserRole.CONSULTANT, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    is_superuser = Column(<PERSON>olean, default=False, nullable=False)
    
    # Relationships
    clients = relationship("Client", back_populates="user", cascade="all, delete-orphan")
    email_replies = relationship("EmailReply", back_populates="user")
    logs = relationship("Log", back_populates="user")
    
    def __repr__(self):
        return f"<User(email='{self.email}', role='{self.role}')>"
