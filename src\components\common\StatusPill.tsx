import React from 'react';
import { Chip } from '@mui/material';
import { NoticeStatus, EmailStatus } from '../../types/enums';

interface StatusPillProps {
  status: NoticeStatus | EmailStatus | string;
  variant?: 'filled' | 'outlined';
}

const StatusPill: React.FC<StatusPillProps> = ({ status, variant = 'filled' }) => {
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'new':
        return 'info';
      case 'replied':
      case 'sent':
        return 'success';
      case 'pending':
      case 'draft':
        return 'warning';
      case 'failed':
        return 'error';
      default:
        return 'default';
    }
  };

  return (
    <Chip
      label={status}
      color={getStatusColor(status) as any}
      variant={variant}
      size="small"
      sx={{
        fontWeight: 500,
        textTransform: 'capitalize'
      }}
    />
  );
};

export default StatusPill;