"""
Log model for system activity tracking
"""
from sqlalchemy import Column, <PERSON>, Integer, ForeignKey, DateTime, Text, Enum as SQLEnum, JSON
from sqlalchemy.orm import relationship
import enum
from .base import BaseModel


class LogLevel(str, enum.Enum):
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class LogCategory(str, enum.Enum):
    AUTH = "auth"
    GST_SYNC = "gst_sync"
    EMAIL = "email"
    API = "api"
    SYSTEM = "system"
    USER_ACTION = "user_action"


class Log(BaseModel):
    __tablename__ = "logs"
    
    # Log identification
    level = Column(SQLEnum(LogLevel), nullable=False)
    category = Column(SQLEnum(LogCategory), nullable=False)
    
    # Log content
    message = Column(Text, nullable=False)
    details = Column(JSON, nullable=True)  # Additional structured data
    
    # Context
    module = Column(String(100), nullable=True)  # Module/function name
    request_id = Column(String(100), nullable=True)  # For request tracing
    session_id = Column(String(100), nullable=True)
    
    # IP and User Agent (for API logs)
    ip_address = Column(String(45), nullable=True)  # IPv6 compatible
    user_agent = Column(String(500), nullable=True)
    
    # Performance metrics
    execution_time_ms = Column(Integer, nullable=True)
    
    # Foreign Keys
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    client_id = Column(Integer, ForeignKey("clients.id"), nullable=True)
    notice_id = Column(Integer, ForeignKey("notices.id"), nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="logs")
    
    def __repr__(self):
        return f"<Log(level='{self.level}', category='{self.category}', message='{self.message[:50]}...')>"


class SyncLog(BaseModel):
    """Specialized log for GST sync operations"""
    __tablename__ = "sync_logs"
    
    # Sync identification
    sync_id = Column(String(100), unique=True, index=True, nullable=False)
    sync_type = Column(String(50), nullable=False)  # 'initial', 'scheduled', 'manual'
    
    # Sync details
    started_at = Column(DateTime(timezone=True), nullable=False)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    status = Column(String(20), nullable=False)  # 'running', 'completed', 'failed', 'partial'
    
    # Statistics
    clients_processed = Column(Integer, default=0, nullable=False)
    notices_found = Column(Integer, default=0, nullable=False)
    notices_downloaded = Column(Integer, default=0, nullable=False)
    notices_failed = Column(Integer, default=0, nullable=False)
    
    # Error handling
    error_message = Column(Text, nullable=True)
    error_details = Column(JSON, nullable=True)
    
    # Performance
    total_duration_seconds = Column(Integer, nullable=True)
    
    # Foreign Keys
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)  # Who triggered the sync
    
    def __repr__(self):
        return f"<SyncLog(sync_id='{self.sync_id}', status='{self.status}')>"
