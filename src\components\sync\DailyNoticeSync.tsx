import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON>pography,
  Button,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Stack,
  Alert,
  CircularProgress
} from '@mui/material';
import SyncIcon from '@mui/icons-material/Sync';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import WarningIcon from '@mui/icons-material/Warning';
import { NoticeSyncLog, Client } from '../../types/schema';
import { formatDate } from '../../utils/formatters';

interface DailyNoticeSyncProps {
  syncLogs: NoticeSyncLog[];
  clients: Client[];
  onRunSync: () => Promise<void>;
}

const DailyNoticeSync: React.FC<DailyNoticeSyncProps> = ({ 
  syncLogs, 
  clients, 
  onRunSync 
}) => {
  const [isRunning, setIsRunning] = useState(false);

  const handleRunSync = async () => {
    setIsRunning(true);
    try {
      await onRunSync();
    } finally {
      setIsRunning(false);
    }
  };

  const todayLogs = syncLogs.filter(log => {
    const logDate = new Date(log.syncDate);
    const today = new Date();
    return logDate.toDateString() === today.toDateString();
  });

  const totalNoticesFound = todayLogs.reduce((sum, log) => sum + log.noticesFound, 0);
  const totalNoticesDownloaded = todayLogs.reduce((sum, log) => sum + log.noticesDownloaded, 0);
  const successfulSyncs = todayLogs.filter(log => log.status === 'success').length;
  const failedSyncs = todayLogs.filter(log => log.status === 'failed').length;

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircleIcon color="success" />;
      case 'failed':
        return <ErrorIcon color="error" />;
      case 'partial':
        return <WarningIcon color="warning" />;
      default:
        return <SyncIcon />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'success';
      case 'failed':
        return 'error';
      case 'partial':
        return 'warning';
      default:
        return 'default';
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 3 }}>
        <Typography variant="h4" fontWeight="bold">
          Daily Notice Sync
        </Typography>
        <Button
          variant="contained"
          startIcon={isRunning ? <CircularProgress size={20} color="inherit" /> : <SyncIcon />}
          onClick={handleRunSync}
          disabled={isRunning}
        >
          {isRunning ? 'Running Sync...' : 'Run Manual Sync'}
        </Button>
      </Stack>

      {/* Today's Summary */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Clients Synced
              </Typography>
              <Typography variant="h4" component="div">
                {todayLogs.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Notices Found
              </Typography>
              <Typography variant="h4" component="div">
                {totalNoticesFound}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Successful Syncs
              </Typography>
              <Typography variant="h4" component="div" color="success.main">
                {successfulSyncs}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Failed Syncs
              </Typography>
              <Typography variant="h4" component="div" color="error.main">
                {failedSyncs}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Sync Status Alert */}
      {todayLogs.length > 0 && (
        <Alert 
          severity={failedSyncs > 0 ? 'warning' : 'success'} 
          sx={{ mb: 3 }}
        >
          {failedSyncs > 0 
            ? `${successfulSyncs} clients synced successfully, ${failedSyncs} failed. Check logs below for details.`
            : `All ${successfulSyncs} clients synced successfully today!`
          }
        </Alert>
      )}

      {/* Sync Logs Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Today's Sync Logs
          </Typography>
          <TableContainer component={Paper} variant="outlined">
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Client</TableCell>
                  <TableCell>GSTIN</TableCell>
                  <TableCell>Sync Time</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Notices Found</TableCell>
                  <TableCell>Downloaded</TableCell>
                  <TableCell>Error Message</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {todayLogs.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} align="center">
                      <Typography color="text.secondary">
                        No sync logs for today. Run manual sync to check for new notices.
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  todayLogs.map((log) => {
                    const client = clients.find(c => c.id === log.clientId);
                    return (
                      <TableRow key={log.id}>
                        <TableCell>
                          <Typography variant="body2" fontWeight="medium">
                            {client?.clientName || 'Unknown Client'}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" fontFamily="monospace">
                            {log.gstin}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {formatDate(new Date(log.syncDate))}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Stack direction="row" alignItems="center" spacing={1}>
                            {getStatusIcon(log.status)}
                            <Chip 
                              label={log.status} 
                              color={getStatusColor(log.status) as any}
                              size="small"
                            />
                          </Stack>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {log.noticesFound}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2">
                            {log.noticesDownloaded}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" color="error.main">
                            {log.errorMessage || '-'}
                          </Typography>
                        </TableCell>
                      </TableRow>
                    );
                  })
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </Box>
  );
};

export default DailyNoticeSync;
