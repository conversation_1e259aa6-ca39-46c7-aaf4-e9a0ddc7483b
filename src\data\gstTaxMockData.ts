import { NoticeStatus, EmailStatus, InvoiceType, UserRole, AuthProvider } from '../types/enums';

// Data for global state store
export const mockStore = {
  user: {
    id: '1',
    name: '<PERSON><PERSON>',
    email: 'r<PERSON><PERSON>.<EMAIL>',
    phone: '+919876543210',
    role: UserRole.CONSULTANT as const,
    authProvider: AuthProvider.GOOGLE as const,
    isAuthenticated: true
  },
  clients: [
    {
      id: '1',
      gstin: '27AAPFU0939F1ZV',
      clientName: 'ABC Enterprises Pvt Ltd',
      loginId: 'abc***@gmail.com',
      contactPerson: 'Am<PERSON> <PERSON>',
      phone: '+919876543210',
      email: '<EMAIL>',
      address: 'Mumbai, Maharashtra',
      addedDate: new Date('2024-01-15').toISOString(),
      status: 'active' as const,
      primaryTaxPortal: {
        username: 'abc_enterprises',
        password: '***encrypted***'
      },
      secondaryTaxPortal: {
        username: 'abc_backup',
        password: '***encrypted***'
      },
      lastNoticeSync: new Date('2024-12-20').toISOString(),
      totalNotices: 3,
      pendingReplies: 1,
      autoSyncEnabled: true
    },
    {
      id: '2', 
      gstin: '29AABCU9603R1ZX',
      clientName: 'XYZ Trading Company',
      loginId: 'xyz***@gmail.com',
      contactPerson: 'Priya Patel',
      phone: '+919876543211',
      email: '<EMAIL>',
      address: 'Bangalore, Karnataka',
      addedDate: new Date('2024-02-10').toISOString(),
      status: 'active' as const,
      primaryTaxPortal: {
        username: 'xyz_trading',
        password: '***encrypted***'
      },
      lastNoticeSync: new Date('2024-12-19').toISOString(),
      totalNotices: 2,
      pendingReplies: 2,
      autoSyncEnabled: true
    }
  ],
  notices: [
    {
      id: 'NOT001',
      gstin: '27AAPFU0939F1ZV',
      clientName: 'ABC Enterprises Pvt Ltd',
      noticeType: 'GST Notice',
      subject: 'Discrepancy in GSTR-1 Return',
      receivedDate: new Date('2024-12-20').toISOString(),
      dueDate: new Date('2024-12-30').toISOString(),
      status: NoticeStatus.NEW as const,
      description: 'Mismatch found in sales data reported in GSTR-1',
      attachmentUrl: '/documents/notice_001.pdf'
    },
    {
      id: 'NOT002',
      gstin: '29AABCU9603R1ZX',
      clientName: 'XYZ Trading Company',
      noticeType: 'Show Cause Notice',
      subject: 'Input Tax Credit Claim Verification',
      receivedDate: new Date('2024-12-18').toISOString(),
      dueDate: new Date('2024-12-28').toISOString(),
      status: NoticeStatus.REPLIED as const,
      description: 'Verification required for ITC claimed in previous quarter',
      attachmentUrl: '/documents/notice_002.pdf'
    },
    {
      id: 'NOT003',
      gstin: '27AAPFU0939F1ZV',
      clientName: 'ABC Enterprises Pvt Ltd',
      noticeType: 'Demand Notice',
      subject: 'Additional Tax Demand for FY 2023-24',
      receivedDate: new Date('2024-12-15').toISOString(),
      dueDate: new Date('2024-12-25').toISOString(),
      status: NoticeStatus.PENDING as const,
      description: 'Additional tax demand raised based on assessment order',
      attachmentUrl: '/documents/notice_003.pdf'
    },
    {
      id: 'NOT004',
      gstin: '29AABCU9603R1ZX',
      clientName: 'XYZ Trading Company',
      noticeType: 'GST Notice',
      subject: 'Late Filing Penalty - GSTR-3B',
      receivedDate: new Date('2024-12-14').toISOString(),
      dueDate: new Date('2024-12-24').toISOString(),
      status: NoticeStatus.NEW as const,
      description: 'Penalty imposed for late filing of GSTR-3B return',
      attachmentUrl: '/documents/notice_004.pdf'
    },
    {
      id: 'NOT005',
      gstin: '24AAGCC7409Q1ZZ',
      clientName: 'PQR Manufacturing Ltd',
      noticeType: 'Show Cause Notice',
      subject: 'Excess ITC Reversal Required',
      receivedDate: new Date('2024-12-13').toISOString(),
      dueDate: new Date('2024-12-23').toISOString(),
      status: NoticeStatus.PENDING as const,
      description: 'Excess input tax credit claimed needs to be reversed',
      attachmentUrl: '/documents/notice_005.pdf'
    },
    {
      id: 'NOT006',
      gstin: '27AAPFU0939F1ZV',
      clientName: 'ABC Enterprises Pvt Ltd',
      noticeType: 'Compliance Notice',
      subject: 'E-way Bill Discrepancy',
      receivedDate: new Date('2024-12-12').toISOString(),
      dueDate: new Date('2024-12-22').toISOString(),
      status: NoticeStatus.REPLIED as const,
      description: 'Discrepancy found in e-way bill generation',
      attachmentUrl: '/documents/notice_006.pdf'
    },
    {
      id: 'NOT007',
      gstin: '29AABCU9603R1ZX',
      clientName: 'XYZ Trading Company',
      noticeType: 'Assessment Notice',
      subject: 'Best Judgment Assessment Order',
      receivedDate: new Date('2024-12-11').toISOString(),
      dueDate: new Date('2024-12-21').toISOString(),
      status: NoticeStatus.NEW as const,
      description: 'Assessment completed under best judgment provisions',
      attachmentUrl: '/documents/notice_007.pdf'
    },
    {
      id: 'NOT008',
      gstin: '24AAGCC7409Q1ZZ',
      clientName: 'PQR Manufacturing Ltd',
      noticeType: 'GST Notice',
      subject: 'Mismatch in Purchase Returns',
      receivedDate: new Date('2024-12-10').toISOString(),
      dueDate: new Date('2024-12-20').toISOString(),
      status: NoticeStatus.PENDING as const,
      description: 'Mismatch found between GSTR-2A and GSTR-3B',
      attachmentUrl: '/documents/notice_008.pdf'
    },
    {
      id: 'NOT009',
      gstin: '27AAPFU0939F1ZV',
      clientName: 'ABC Enterprises Pvt Ltd',
      noticeType: 'Recovery Notice',
      subject: 'Outstanding Tax Recovery',
      receivedDate: new Date('2024-12-09').toISOString(),
      dueDate: new Date('2024-12-19').toISOString(),
      status: NoticeStatus.REPLIED as const,
      description: 'Recovery proceedings initiated for outstanding tax',
      attachmentUrl: '/documents/notice_009.pdf'
    },
    {
      id: 'NOT010',
      gstin: '29AABCU9603R1ZX',
      clientName: 'XYZ Trading Company',
      noticeType: 'Audit Notice',
      subject: 'GST Audit Selection Notice',
      receivedDate: new Date('2024-12-08').toISOString(),
      dueDate: new Date('2024-12-18').toISOString(),
      status: NoticeStatus.NEW as const,
      description: 'Selected for GST audit for FY 2023-24',
      attachmentUrl: '/documents/notice_010.pdf'
    },
    {
      id: 'NOT011',
      gstin: '24AAGCC7409Q1ZZ',
      clientName: 'PQR Manufacturing Ltd',
      noticeType: 'Show Cause Notice',
      subject: 'Classification Dispute',
      receivedDate: new Date('2024-12-07').toISOString(),
      dueDate: new Date('2024-12-17').toISOString(),
      status: NoticeStatus.PENDING as const,
      description: 'Dispute regarding HSN code classification',
      attachmentUrl: '/documents/notice_011.pdf'
    },
    {
      id: 'NOT012',
      gstin: '27AAPFU0939F1ZV',
      clientName: 'ABC Enterprises Pvt Ltd',
      noticeType: 'GST Notice',
      subject: 'Return Filing Default',
      receivedDate: new Date('2024-12-06').toISOString(),
      dueDate: new Date('2024-12-16').toISOString(),
      status: NoticeStatus.NEW as const,
      description: 'Default in filing monthly GST returns',
      attachmentUrl: '/documents/notice_012.pdf'
    }
  ],
  emailHistory: [
    {
      id: 'EMAIL001',
      noticeId: 'NOT002',
      subject: 'Response to Show Cause Notice - ITC Verification',
      sentDate: new Date('2024-12-19').toISOString(),
      status: EmailStatus.SENT as const,
      recipientEmail: '<EMAIL>',
      attachments: ['response_document.pdf']
    }
  ],
  invoices: [
    {
      id: 'INV001',
      gstin: '27AAPFU0939F1ZV',
      clientName: 'ABC Enterprises Pvt Ltd',
      invoiceNumber: 'ABC/2024/001',
      invoiceDate: new Date('2024-12-15').toISOString(),
      invoiceType: InvoiceType.B2B as const,
      totalAmount: 118000,
      taxAmount: 18000,
      downloadUrl: '/invoices/ABC_2024_001.pdf',
      status: 'downloaded' as const
    },
    {
      id: 'INV002',
      gstin: '29AABCU9603R1ZX',
      clientName: 'XYZ Trading Company',
      invoiceNumber: 'XYZ/2024/045',
      invoiceDate: new Date('2024-12-18').toISOString(),
      invoiceType: InvoiceType.B2C as const,
      totalAmount: 59000,
      taxAmount: 9000,
      downloadUrl: '/invoices/XYZ_2024_045.pdf',
      status: 'pending' as const
    },
    {
      id: 'INV003',
      gstin: '27AAPFU0939F1ZV',
      clientName: 'ABC Enterprises Pvt Ltd',
      invoiceNumber: 'ABC/2024/002',
      invoiceDate: new Date('2024-12-20').toISOString(),
      invoiceType: InvoiceType.EXPORT as const,
      totalAmount: 250000,
      taxAmount: 0,
      downloadUrl: '/invoices/ABC_2024_002.pdf',
      status: 'failed' as const
    }
  ]
};

// Data returned by API queries
export const mockQuery = {
  dashboardStats: {
    totalClients: 25,
    noticesToday: 3,
    repliesSent: 12,
    invoicesDownloaded: 45
  },
  noticesByStatus: [
    { status: 'New', count: 8 },
    { status: 'Replied', count: 15 },
    { status: 'Pending', count: 5 }
  ],
  recentNotices: [
    {
      id: 'NOT003',
      gstin: '27AAPFU0939F1ZV',
      clientName: 'ABC Enterprises Pvt Ltd',
      subject: 'Late Filing Penalty Notice',
      receivedDate: new Date('2024-12-21').toISOString(),
      status: NoticeStatus.NEW as const
    }
  ]
};

// Data passed as props to the root component
export const mockRootProps = {
  initialRoute: '/dashboard',
  theme: 'light' as const,
  apiBaseUrl: 'http://localhost:8000/api'
};