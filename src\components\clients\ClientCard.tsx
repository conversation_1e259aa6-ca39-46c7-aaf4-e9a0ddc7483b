import React from 'react';
import {
  <PERSON>,
  CardContent,
  Typo<PERSON>,
  Button,
  Stack,
  Box,
  Chip
} from '@mui/material';
import BusinessIcon from '@mui/icons-material/Business';
import PersonIcon from '@mui/icons-material/Person';
import PhoneIcon from '@mui/icons-material/Phone';
import EmailIcon from '@mui/icons-material/Email';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import VisibilityIcon from '@mui/icons-material/Visibility';
import CloudDownloadIcon from '@mui/icons-material/CloudDownload';
import { Client } from '../../types/schema';
import { formatDate, formatGSTIN } from '../../utils/formatters';

interface ClientCardProps {
  client: Client;
  onView: () => void;
  onDownload?: () => void;
}

const ClientCard: React.FC<ClientCardProps> = ({ client, onView, onDownload }) => {
  return (
    <Card sx={{ boxShadow: 2, '&:hover': { boxShadow: 4 } }}>
      <CardContent>
        <Stack spacing={2}>
          <Stack direction="row" justifyContent="space-between" alignItems="flex-start">
            <Stack spacing={1}>
              <Stack direction="row" alignItems="center" spacing={1}>
                <BusinessIcon color="primary" />
                <Typography variant="h6" fontWeight="bold">
                  {client.clientName}
                </Typography>
                <Chip
                  label={client.status}
                  color={client.status === 'active' ? 'success' : 'default'}
                  size="small"
                />
              </Stack>
              <Typography variant="body1" color="primary" fontWeight={500}>
                GSTIN: {formatGSTIN(client.gstin)}
              </Typography>
            </Stack>
            <Stack direction="row" spacing={1}>
              <Button
                variant="outlined"
                startIcon={<VisibilityIcon />}
                onClick={onView}
                size="small"
              >
                View Details
              </Button>
              {onDownload && (
                <Button
                  variant="contained"
                  startIcon={<CloudDownloadIcon />}
                  onClick={onDownload}
                  size="small"
                  color="secondary"
                >
                  Download
                </Button>
              )}
            </Stack>
          </Stack>

          <Stack direction={{ xs: 'column', md: 'row' }} spacing={3}>
            <Stack spacing={1} flex={1}>
              <Stack direction="row" alignItems="center" spacing={1}>
                <PersonIcon fontSize="small" color="action" />
                <Typography variant="body2">
                  <strong>Contact:</strong> {client.contactPerson}
                </Typography>
              </Stack>
              <Stack direction="row" alignItems="center" spacing={1}>
                <PhoneIcon fontSize="small" color="action" />
                <Typography variant="body2">
                  {client.phone}
                </Typography>
              </Stack>
            </Stack>

            <Stack spacing={1} flex={1}>
              <Stack direction="row" alignItems="center" spacing={1}>
                <EmailIcon fontSize="small" color="action" />
                <Typography variant="body2">
                  {client.email}
                </Typography>
              </Stack>
              <Stack direction="row" alignItems="center" spacing={1}>
                <LocationOnIcon fontSize="small" color="action" />
                <Typography variant="body2">
                  {client.address}
                </Typography>
              </Stack>
            </Stack>

            <Box>
              <Typography variant="caption" color="text.secondary">
                Added on {formatDate(new Date(client.addedDate))}
              </Typography>
            </Box>
          </Stack>
        </Stack>
      </CardContent>
    </Card>
  );
};

export default ClientCard;