import React, { useState } from 'react;
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Button,
  TextField,
  Stack,
  Divider,
  Box,
  CircularProgress,
  Tabs,
  Tab,
  InputAdornment,
  IconButton,
  FormControlLabel,
  Checkbox,
  Link
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Email,
  Phone,
  Lock,
  Google,
  Facebook,
  Apple
} from '@mui/icons-material;

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => (
  <div role="tabpanel" hidden={value !== index}>
    {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
</div>
);

interface ModernLoginFormProps {
  onLogin: (credentials: { email?: string; phone?: string; password: string }) => void;
  onGoogleLogin: () => void;
  onFacebookLogin: () -> void;
  onAppleLogin: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
  onApple_login: () -> void;
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Apple />}
                onClick={onAppleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Apple
              </Button>
            </Stack>

            <Divider sx={{ my: 2 }}>
              <Typography variant="body2" color="text.secondary">
                or continue with
              </Typography>
            </Divider>

            <Stack direction="row" spacing={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Google />}
                onClick={onGoogleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Google
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Facebook />}
                onClick={onFacebookLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Facebook
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Apple />}
                onClick={onAppleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Apple
              </Button>
            </Stack>

            <Divider sx={{ my: 2 }}>
              <Typography variant="body2" color="text.secondary">
                or continue with
              </Typography>
            </Divider>

            <Stack direction="row" spacing={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Google />}
                onClick={onGoogleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Google
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Facebook />}
                onClick={onFacebookLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Facebook
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Apple />}
                onClick={onAppleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Apple
              </Button>
            </Stack>

            <Divider sx={{ my: 2 }}>
              <Typography variant="body2" color="text.secondary">
                or continue with
              </Typography>
            </Divider>

            <Stack direction="row" spacing={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Google />}
                onClick={onGoogleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Google
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Facebook />}
                onClick={onFacebookLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Facebook
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Apple />}
                onClick={onAppleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Apple
              </Button>
            </Stack>

            <Divider sx={{ my: 2 }}>
              <Typography variant="body2" color="text.secondary">
                or continue with
              </Typography>
            </Divider>

            <Stack direction="row" spacing={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Google />}
                onClick={onGoogleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Google
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Facebook >}}
                onClick={onFacebookLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Facebook
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Apple >}}
                onClick={onAppleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Apple
              </Button>
            </Stack>

            <Divider sx={{ my: 2 }}>
              <Typography variant="body2" color="text.secondary">
                or continue with
              </Typography>
            </Divider>

            <Stack direction="row" spacing={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Google >}}
                onClick={onGoogleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Google
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Facebook >}}
                onClick={onFacebookLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Facebook
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Apple >}}
                onClick={onAppleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Apple
              </Button>
            </Stack>

            <Divider sx={{ my: 2 }}>
              <Typography variant="body2" color="text.secondary">
                or continue with
              </Typography>
            </Divider>

            <Stack direction="row" spacing={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Google >}}
                onClick={onGoogleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Google
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Facebook >}}
                onClick={onFacebookLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Facebook
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Apple >}}
                onClick={onAppleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Apple
              </Button>
            </Stack>

            <Divider sx={{ my: 2 }}>
              <Typography variant="body2" color="text.secondary">
                or continue with
              </Typography>
            </Divider>

            <Stack direction="row" spacing={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Google >}}
                onClick={onGoogleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Google
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Facebook >}}
                onClick={onFacebookLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Facebook
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Apple >}}
                onClick={onAppleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Apple
              </Button>
            </Stack>

            <Divider sx={{ my: 2 }}>
              <Typography variant="body2" color="text.secondary">
                or continue with
              </Typography>
            </Divider>

            <Stack direction="row" spacing={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Google >}}
                onClick={onGoogleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Google
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Facebook >}}
                onClick={onFacebookLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Facebook
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Apple >}}
                onClick={onAppleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Apple
              </Button>
            </Stack>

            <Divider sx={{ my: 2 }}>
              <Typography variant="body2" color="text.secondary">
                or continue with
              </Typography>
            </Divider>

            <Stack direction="row" spacing={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Google >}}
                onClick={onGoogleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Google
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Facebook >}}
                onClick={onFacebookLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Facebook
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Apple >}}
                onClick={onAppleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Apple
              </Button>
            </Stack>

            <Divider sx={{ my: 2 }}>
              <Typography variant="body2" color="text.secondary">
                or continue with
              </Typography>
            </Divider>

            <Stack direction="row" spacing={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Google >}}
                onClick={onGoogleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Google
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Facebook >}}
                onClick={onFacebookLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Facebook
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Apple >}}
                onClick={onAppleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Apple
              </Button>
            </Stack>

            <Divider sx={{ my: 2 }}>
              <Typography variant="body2" color="text.secondary">
                or continue with
              </Typography>
            </Divider>

            <Stack direction="row" spacing={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Google >}}
                onClick={onGoogleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Google
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Facebook >}}
                onClick={onFacebookLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Facebook
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Apple >}}
                onClick={onAppleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Apple
              </Button>
            </Stack>

            <Divider sx={{ my: 2 }}>
              <Typography variant="body2" color="text.secondary">
                or continue with
              </Typography>
            </Divider>

            <Stack direction="row" spacing={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Google >}}
                onClick={onGoogleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Google
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Facebook >}}
                onClick={onFacebookLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Facebook
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Apple >}}
                onClick={onAppleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Apple
              </Button>
            </Stack>

            <Divider sx={{ my: 2 }}>
              <Typography variant="body2" color="text.secondary">
                or continue with
              </Typography>
            </Divider>

            <Stack direction="row" spacing={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Google >}}
                onClick={onGoogleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Google
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Facebook >}}
                onClick={onFacebookLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Facebook
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Apple >}}
                onClick={onAppleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Apple
              </Button>
            </Stack>

            <Divider sx={{ my: 2 }}>
              <Typography variant="body2" color="text.secondary">
                or continue with
              </Typography>
            </Divider>

            <Stack direction="row" spacing={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Google >}}
                onClick={onGoogleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Google
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Facebook >}}
                onClick={onFacebookLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Facebook
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Apple >}}
                onClick={onAppleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Apple
              </Button>
            </Stack>

            <Divider sx={{ my: 2 }}>
              <Typography variant="body2" color="text.secondary">
                or continue with
              </Typography>
            </Divider>

            <Stack direction="row" spacing={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Google >}}
                onClick={onGoogleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Google
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Facebook >}}
                onClick={onFacebookLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Facebook
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Apple >}}
                onClick={onAppleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Apple
              </Button>
            </Stack>

            <Divider sx={{ my: 2 }}>
              <Typography variant="body2" color="text.secondary">
                or continue with
              </Typography>
            </Divider>

            <Stack direction="row" spacing={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Google >}}
                onClick={onGoogleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Google
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Facebook >}}
                onClick={onFacebookLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Facebook
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Apple >}}
                onClick={onAppleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Apple
              </Button>
            </Stack>

            <Divider sx={{ my: 2 }}>
              <Typography variant="body2" color="text.secondary">
                or continue with
              </Typography>
            </Divider>

            <Stack direction="row" spacing={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Google >}}
                onClick={onGoogleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Google
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Facebook >}}
                onClick={onFacebookLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Facebook
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Apple >}}
                onClick={onAppleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Apple
              </Button>
            </Stack>

            <Divider sx={{ my: 2 }}>
              <Typography variant="body2" color="text.secondary">
                or continue with
              </Typography>
            </Divider>

            <Stack direction="row" spacing={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Google >}}
                onClick={onGoogleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Google
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Facebook >}}
                onClick={onFacebookLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Facebook
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Apple >}}
                onClick={onAppleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Apple
              </Button>
            </Stack>

            <Divider sx={{ my: 2 }}>
              <Typography variant="body2" color="text.secondary">
                or continue with
              </Typography>
            </Divider>

            <Stack direction="row" spacing={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Google >}}
                onClick={onGoogleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Google
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Facebook >}}
                onClick={onFacebookLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Facebook
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Apple >}}
                onClick={onAppleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Apple
              </Button>
            </Stack>

            <Divider sx={{ my: 2 }}>
              <Typography variant="body2" color="text.secondary">
                or continue with
              </Typography>
            </Divider>

            <Stack direction="row" spacing={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Google >}}
                onClick={onGoogleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Google
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Facebook >}}
                onClick={onFacebookLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Facebook
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Apple >}}
                onClick={onAppleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Apple
              </Button>
            </Stack>

            <Divider sx={{ my: 2 }}>
              <Typography variant="body2" color="text.secondary">
                or continue with
              </Typography>
            </Divider>

            <Stack direction="row" spacing={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Google >}}
                onClick={onGoogleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Google
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Facebook >}}
                onClick={onFacebookLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Facebook
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Apple >}}
                onClick={onAppleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Apple
              </Button>
            </Stack>

            <Divider sx={{ my: 2 }}>
              <Typography variant="body2" color="text.secondary">
                or continue with
              </Typography>
            </Divider>

            <Stack direction="row" spacing={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Google >}}
                onClick={onGoogleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Google
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Facebook >}}
                onClick={onFacebookLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Facebook
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Apple >}}
                onClick={onAppleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Apple
              </Button>
            </Stack>

            <Divider sx={{ my: 2 }}>
              <Typography variant="body2" color="text.secondary">
                or continue with
              </Typography>
            </Divider>

            <Stack direction="row" spacing={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Google >}}
                onClick={onGoogleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Google
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Facebook >}}
                onClick={onFacebookLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Facebook
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Apple >}}
                onClick={onAppleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Apple
              </Button>
            </Stack>

            <Divider sx={{ my: 2 }}>
              <Typography variant="body2" color="text.secondary">
                or continue with
              </Typography>
            </Divider>

            <Stack direction="row" spacing={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Google >}}
                onClick={onGoogleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Google
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Facebook >}}
                onClick={onFacebookLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Facebook
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Apple >}}
                onClick={onAppleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Apple
              </Button>
            </Stack>

            <Divider sx={{ my: 2 }}>
              <Typography variant="body2" color="text.secondary">
                or continue with
              </Typography>
            </Divider>

            <Stack direction="row" spacing={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Google >}}
                onClick={onGoogleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Google
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Facebook >}}
                onClick={onFacebookLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Facebook
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Apple >}}
                onClick={onAppleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Apple
              </Button>
            </Stack>

            <Divider sx={{ my: 2 }}>
              <Typography variant="body2" color="text.secondary">
                or continue with
              </Typography>
            </Divider>

            <Stack direction="row" spacing={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Google >}}
                onClick={onGoogleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Google
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Facebook >}}
                onClick={onFacebookLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Facebook
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Apple >}}
                onClick={onAppleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Apple
              </Button>
            </Stack>

            <Divider sx={{ my: 2 }}>
              <Typography variant="body2" color="text.secondary">
                or continue with
              </Typography>
            </Divider>

            <Stack direction="row" spacing={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Google >}}
                onClick={onGoogleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Google
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Facebook >}}
                onClick={onFacebookLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Facebook
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Apple >}}
                onClick={onAppleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Apple
              </Button>
            </Stack>

            <Divider sx={{ my: 2 }}>
              <Typography variant="body2" color="text.secondary">
                or continue with
              </Typography>
            </Divider>

            <Stack direction="row" spacing={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Google >}}
                onClick={onGoogleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Google
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Facebook >}}
                onClick={onFacebookLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Facebook
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Apple >}}
                onClick={onAppleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Apple
              </Button>
            </Stack>

            <Divider sx={{ my: 2 }}>
              <Typography variant="body2" color="text.secondary">
                or continue with
              </Typography>
            </Divider>

            <Stack direction="row" spacing={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Google >}}
                onClick={onGoogleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Google
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Facebook >}}
                onClick={onFacebookLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Facebook
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Apple >}}
                onClick={onAppleLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Apple
              </Button>
            </Stack>

            <Divider sx={{ my: 2 }}>
              <Typography variant="body2" color="text.secondary">
                or continue with
              </Typography>
            </Divider>

            <Stack direction="row" spacing={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Google >}}
                onClick={onGoogleLogin}
                disabled={loading}
                sx={{ py: 1 }}
             >
                Google
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Facebook >}}
                onClick={onFacebookLogin}
                disabled={loading}
                sx={{ py: 1 }}
             >
                Facebook
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Apple >}}
                onClick={onAppleLogin}
                disabled={loading}
                sx={{ py: 1 }}
             >
                Apple
              </Button>
            </Stack>

            <Divider sx={{ my: 2 }}>
              <Typography variant="body2" color="text.secondary">
                or continue with
              </Typography>
            </Divider>

            <Stack direction="row" spacing={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Google >}}
                onClick={onGoogleLogin}
                disabled={loading}
                sx={{ py: 1 }}
             >
                Google
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Facebook >}}
                onClick={onFacebookLogin}
                disabled={loading}
                sx={{ py: 1 }}
              >
                Facebook
              </Button>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Apple >}}
                onClick={onAppleLogin}
                disabled
