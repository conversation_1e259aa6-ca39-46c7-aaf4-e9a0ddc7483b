// Simplified GST API Service using only fetch (no external dependencies)

// Types for GST API responses
export interface GSTClientDetails {
  gstin: string;
  legalName: string;
  tradeName: string;
  registrationDate: string;
  constitutionOfBusiness: string;
  taxpayerType: string;
  gstinStatus: string;
  lastUpdatedDate: string;
  registrationAddress: {
    buildingName: string;
    streetName: string;
    location: string;
    pincode: string;
    district: string;
    state: string;
  };
  principalPlaceOfBusiness: {
    buildingName: string;
    streetName: string;
    location: string;
    pincode: string;
    district: string;
    state: string;
  };
  filingStatus: {
    gstr1: string;
    gstr3b: string;
    lastFilingDate: string;
  };
  businessActivities: string[];
  authorizedSignatories: Array<{
    name: string;
    designation: string;
  }>;
}

export interface GSTReturn {
  returnType: 'GSTR1' | 'GSTR3B' | 'GSTR9';
  period: string;
  filingDate: string;
  status: 'Filed' | 'Not Filed' | 'Late Filed';
  dueDate: string;
  arn: string;
}

export interface GSTNotice {
  noticeNumber: string;
  noticeType: string;
  issueDate: string;
  dueDate: string;
  status: 'Open' | 'Closed' | 'Under Review';
  description: string;
  amount?: number;
  section: string;
}

class SimpleGSTApiService {
  private readonly baseUrl = process.env.REACT_APP_GST_API_URL || 'https://api.gst.gov.in';
  private readonly apiKey = process.env.REACT_APP_GST_API_KEY || 'your-api-key';
  private readonly timeout = 30000;

  /**
   * Validate GSTIN format
   */
  validateGSTIN(gstin: string): boolean {
    const gstinRegex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;
    return gstinRegex.test(gstin);
  }

  /**
   * Fetch client details from GST portal using GSTIN
   */
  async fetchClientDetails(gstin: string): Promise<GSTClientDetails> {
    console.log(`Fetching GST client details for GSTIN: ${gstin}`);

    // Validate GSTIN first
    if (!this.validateGSTIN(gstin)) {
      throw new Error('Invalid GSTIN format. Please enter a valid 15-character GSTIN.');
    }

    // Always return mock data for now (until real API is configured)
    console.log('Using mock data for GST client details');
    return this.getMockClientDetails(gstin);
  }

  /**
   * Fetch GST returns for a client
   */
  async fetchGSTReturns(gstin: string): Promise<GSTReturn[]> {
    // Always return mock data for now
    return this.getMockGSTReturns(gstin);
  }

  /**
   * Fetch GST notices for a client
   */
  async fetchGSTNotices(gstin: string): Promise<GSTNotice[]> {
    // Always return mock data for now
    return this.getMockGSTNotices(gstin);
  }

  /**
   * Mock client details for development/demo
   */
  private getMockClientDetails(gstin: string): Promise<GSTClientDetails> {
    // Simulate API delay
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          gstin: gstin,
          legalName: 'ABC ENTERPRISES PRIVATE LIMITED',
          tradeName: 'ABC Enterprises',
          registrationDate: '2018-07-01',
          constitutionOfBusiness: 'Private Limited Company',
          taxpayerType: 'Regular',
          gstinStatus: 'Active',
          lastUpdatedDate: '2024-01-15',
          registrationAddress: {
            buildingName: 'Tech Park Building',
            streetName: 'MG Road',
            location: 'Bangalore',
            pincode: '560001',
            district: 'Bangalore Urban',
            state: 'Karnataka',
          },
          principalPlaceOfBusiness: {
            buildingName: 'Tech Park Building',
            streetName: 'MG Road',
            location: 'Bangalore',
            pincode: '560001',
            district: 'Bangalore Urban',
            state: 'Karnataka',
          },
          filingStatus: {
            gstr1: 'Filed',
            gstr3b: 'Filed',
            lastFilingDate: '2024-01-20',
          },
          businessActivities: [
            'Software Development',
            'IT Consulting Services'
          ],
          authorizedSignatories: [
            {
              name: 'Rajesh Kumar',
              designation: 'Director'
            }
          ],
        });
      }, 2000); // 2 second delay to simulate API call
    });
  }

  /**
   * Mock GST returns for development
   */
  private getMockGSTReturns(gstin: string): Promise<GSTReturn[]> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve([
          {
            returnType: 'GSTR3B',
            period: '122023',
            filingDate: '2024-01-20',
            status: 'Filed',
            dueDate: '2024-01-20',
            arn: 'AA240120123456789'
          },
          {
            returnType: 'GSTR1',
            period: '122023',
            filingDate: '2024-01-11',
            status: 'Filed',
            dueDate: '2024-01-11',
            arn: 'AA240111123456789'
          }
        ]);
      }, 1500);
    });
  }

  /**
   * Mock GST notices for development
   */
  private getMockGSTNotices(gstin: string): Promise<GSTNotice[]> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve([
          {
            noticeNumber: 'GST-001-2024',
            noticeType: 'GSTR-3B Mismatch',
            issueDate: '2024-01-15',
            dueDate: '2024-02-15',
            status: 'Open',
            description: 'Discrepancy found in GSTR-3B filing for December 2023',
            amount: 25000,
            section: 'Section 61'
          },
          {
            noticeNumber: 'GST-002-2024',
            noticeType: 'Input Credit Mismatch',
            issueDate: '2024-02-01',
            dueDate: '2024-03-01',
            status: 'Open',
            description: 'ITC claimed more than eligible amount',
            amount: 15000,
            section: 'Section 16'
          }
        ]);
      }, 1500);
    });
  }
}

export const simpleGstApiService = new SimpleGSTApiService();
export default simpleGstApiService;
