"""
Simple FastAPI server for quick testing
"""
from fastapi import Fast<PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

# Create FastAPI app
app = FastAPI(
    title="GST Notice Management System",
    version="1.0.0",
    description="Backend API for GST Notice Management System"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "app_name": "GST Notice Management System",
        "version": "1.0.0",
        "environment": "development"
    }

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Welcome to GST Notice Management System",
        "version": "1.0.0",
        "docs_url": "/docs"
    }

# Mock API endpoints for testing
@app.get("/api/v1/dashboard/stats")
async def get_dashboard_stats():
    """Mock dashboard stats"""
    return {
        "total_clients": 25,
        "total_notices": 150,
        "new_notices": 8,
        "pending_replies": 12,
        "notices_today": 3,
        "overdue_notices": 2
    }

@app.get("/api/v1/clients/")
async def get_clients():
    """Mock clients list"""
    return [
        {
            "id": 1,
            "gstin": "27AAPFU0939F1ZV",
            "client_name": "ABC Enterprises Pvt Ltd",
            "contact_person": "Amit Sharma",
            "email": "<EMAIL>",
            "phone": "+**********10",
            "total_notices": 15,
            "pending_replies": 3,
            "last_sync_at": "2024-12-22T10:30:00Z",
            "auto_sync_enabled": True,
            "is_active": True
        },
        {
            "id": 2,
            "gstin": "29AABCU9603R1ZX",
            "client_name": "XYZ Trading Company",
            "contact_person": "Priya Patel",
            "email": "<EMAIL>",
            "phone": "+**********11",
            "total_notices": 8,
            "pending_replies": 2,
            "last_sync_at": "2024-12-22T09:15:00Z",
            "auto_sync_enabled": True,
            "is_active": True
        }
    ]

@app.get("/api/v1/notices/")
async def get_notices():
    """Mock notices list"""
    return {
        "success": True,
        "message": "Notices retrieved successfully",
        "data": [
            {
                "id": 1,
                "notice_id": "NOT001",
                "subject": "GSTR-1 Return Discrepancy",
                "notice_type": "gst_notice",
                "status": "new",
                "notice_date": "2024-12-20T00:00:00Z",
                "due_date": "2024-12-30T00:00:00Z",
                "received_date": "2024-12-20T10:00:00Z",
                "client_id": 1,
                "description": "Discrepancy found in sales data reported in GSTR-1 return"
            },
            {
                "id": 2,
                "notice_id": "NOT002",
                "subject": "Input Tax Credit Verification",
                "notice_type": "show_cause",
                "status": "replied",
                "notice_date": "2024-12-18T00:00:00Z",
                "due_date": "2024-12-28T00:00:00Z",
                "received_date": "2024-12-18T14:30:00Z",
                "client_id": 2,
                "description": "Verification required for ITC claimed in previous quarter"
            }
        ],
        "total": 2,
        "limit": 100,
        "offset": 0,
        "has_more": False
    }

@app.get("/api/v1/sync/status")
async def get_sync_status():
    """Mock sync status"""
    return [
        {
            "client_id": 1,
            "gstin": "27AAPFU0939F1ZV",
            "client_name": "ABC Enterprises Pvt Ltd",
            "status": "success",
            "last_sync": "2024-12-22T10:30:00Z",
            "last_successful_sync": "2024-12-22T10:30:00Z",
            "sync_due": False,
            "auto_sync_enabled": True,
            "total_notices": 15,
            "pending_replies": 3
        },
        {
            "client_id": 2,
            "gstin": "29AABCU9603R1ZX",
            "client_name": "XYZ Trading Company",
            "status": "success",
            "last_sync": "2024-12-22T09:15:00Z",
            "last_successful_sync": "2024-12-22T09:15:00Z",
            "sync_due": False,
            "auto_sync_enabled": True,
            "total_notices": 8,
            "pending_replies": 2
        }
    ]

@app.post("/api/v1/sync/manual")
async def manual_sync():
    """Mock manual sync"""
    return {
        "message": "Sync started for 2 clients",
        "client_ids": [1, 2]
    }

# Authentication endpoints (mock)
@app.post("/api/v1/auth/login")
async def login():
    """Mock login"""
    return {
        "access_token": "mock_token_12345",
        "token_type": "bearer",
        "expires_in": 1800,
        "user": {
            "id": 1,
            "email": "<EMAIL>",
            "username": "admin",
            "full_name": "System Administrator",
            "role": "admin",
            "is_active": True
        }
    }

if __name__ == "__main__":
    uvicorn.run(
        "simple_server:app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )
