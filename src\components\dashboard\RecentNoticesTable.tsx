import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Box
} from '@mui/material';
import VisibilityIcon from '@mui/icons-material/Visibility';
import StatusPill from '../common/StatusPill';
import { Notice } from '../../types/schema';
import { formatDate } from '../../utils/formatters';

interface RecentNoticesTableProps {
  notices: Notice[];
  onViewNotice: (noticeId: string) => void;
}

const RecentNoticesTable: React.FC<RecentNoticesTableProps> = ({ notices, onViewNotice }) => {
  return (
    <Card sx={{
      height: '100%',
      boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
      borderRadius: '12px',
      border: '1px solid #f0f0f0',
      backgroundColor: 'background.paper'
    }}>
      <CardContent sx={{ p: 3 }}>
        <Box sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 3
        }}>
          <Typography
            variant="h6"
            fontWeight="600"
            sx={{
              fontSize: '18px',
              color: '#1a1a1a',
              fontFamily: '"Inter", "Roboto", sans-serif'
            }}
          >
            Recent Notices
          </Typography>
          {notices.length > 8 && (
            <Typography
              variant="caption"
              sx={{
                color: '#666666',
                fontSize: '12px',
                fontFamily: '"Inter", "Roboto", sans-serif',
                backgroundColor: '#f8f9fa',
                px: 2,
                py: 0.5,
                borderRadius: '12px',
                border: '1px solid #e9ecef'
              }}
            >
              {notices.length} notices • Scroll to see more
            </Typography>
          )}
        </Box>
        <TableContainer sx={{
          maxHeight: 300,
          overflowY: 'auto',
          overflowX: 'hidden',
          border: '1px solid #e9ecef',
          borderRadius: '8px',
          '&::-webkit-scrollbar': {
            width: '8px',
          },
          '&::-webkit-scrollbar-track': {
            backgroundColor: '#f8f9fa',
            borderRadius: '4px',
            margin: '4px',
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: '#dee2e6',
            borderRadius: '4px',
            border: '1px solid #e9ecef',
            '&:hover': {
              backgroundColor: '#adb5bd',
            },
            '&:active': {
              backgroundColor: '#6c757d',
            },
          },
          // Firefox scrollbar styling
          scrollbarWidth: 'thin',
          scrollbarColor: '#dee2e6 #f8f9fa',
        }}>
          <Table stickyHeader size="small">
            <TableHead>
              <TableRow>
                <TableCell sx={{
                  backgroundColor: '#f8f9fa',
                  fontWeight: '600',
                  fontSize: '13px',
                  color: '#666666',
                  fontFamily: '"Inter", "Roboto", sans-serif',
                  borderBottom: '2px solid #e9ecef',
                  py: 2
                }}>
                  Notice ID
                </TableCell>
                <TableCell sx={{
                  backgroundColor: '#f8f9fa',
                  fontWeight: '600',
                  fontSize: '13px',
                  color: '#666666',
                  fontFamily: '"Inter", "Roboto", sans-serif',
                  borderBottom: '2px solid #e9ecef',
                  py: 2
                }}>
                  Client
                </TableCell>
                <TableCell sx={{
                  backgroundColor: '#f8f9fa',
                  fontWeight: '600',
                  fontSize: '13px',
                  color: '#666666',
                  fontFamily: '"Inter", "Roboto", sans-serif',
                  borderBottom: '2px solid #e9ecef',
                  py: 2
                }}>
                  Subject
                </TableCell>
                <TableCell sx={{
                  backgroundColor: '#f8f9fa',
                  fontWeight: '600',
                  fontSize: '13px',
                  color: '#666666',
                  fontFamily: '"Inter", "Roboto", sans-serif',
                  borderBottom: '2px solid #e9ecef',
                  py: 2
                }}>
                  Date
                </TableCell>
                <TableCell sx={{
                  backgroundColor: '#f8f9fa',
                  fontWeight: '600',
                  fontSize: '13px',
                  color: '#666666',
                  fontFamily: '"Inter", "Roboto", sans-serif',
                  borderBottom: '2px solid #e9ecef',
                  py: 2
                }}>
                  Status
                </TableCell>
                <TableCell align="center" sx={{
                  backgroundColor: '#f8f9fa',
                  fontWeight: '600',
                  fontSize: '13px',
                  color: '#666666',
                  fontFamily: '"Inter", "Roboto", sans-serif',
                  borderBottom: '2px solid #e9ecef',
                  py: 2
                }}>
                  Action
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {notices.map((notice) => (
                <TableRow
                  key={notice.id}
                  hover
                  sx={{
                    cursor: 'pointer',
                    transition: 'background-color 0.2s ease',
                    '&:hover': {
                      backgroundColor: '#f8f9fa',
                      '& .view-button': {
                        backgroundColor: '#1976d2',
                        color: '#ffffff',
                        transform: 'translateY(-1px)',
                        boxShadow: '0 2px 8px rgba(25, 118, 210, 0.3)',
                      }
                    },
                    '&:last-child td': {
                      borderBottom: 'none'
                    }
                  }}
                >
                  <TableCell sx={{ py: 2, borderBottom: '1px solid #f0f0f0' }}>
                    <Typography
                      variant="body2"
                      fontWeight="500"
                      sx={{
                        fontSize: '13px',
                        color: '#1a1a1a',
                        fontFamily: '"Inter", "Roboto", sans-serif'
                      }}
                    >
                      {notice.id}
                    </Typography>
                  </TableCell>
                  <TableCell sx={{ py: 2, borderBottom: '1px solid #f0f0f0' }}>
                    <Typography
                      variant="body2"
                      sx={{
                        fontSize: '13px',
                        color: '#1a1a1a',
                        fontFamily: '"Inter", "Roboto", sans-serif'
                      }}
                    >
                      {notice.clientName}
                    </Typography>
                  </TableCell>
                  <TableCell sx={{ py: 2, borderBottom: '1px solid #f0f0f0' }}>
                    <Typography
                      variant="body2"
                      sx={{
                        maxWidth: 200,
                        fontSize: '13px',
                        color: '#1a1a1a',
                        fontFamily: '"Inter", "Roboto", sans-serif'
                      }}
                      noWrap
                    >
                      {notice.subject}
                    </Typography>
                  </TableCell>
                  <TableCell sx={{ py: 2, borderBottom: '1px solid #f0f0f0' }}>
                    <Typography
                      variant="body2"
                      sx={{
                        fontSize: '13px',
                        color: '#666666',
                        fontFamily: '"Inter", "Roboto", sans-serif'
                      }}
                    >
                      {formatDate(new Date(notice.receivedDate))}
                    </Typography>
                  </TableCell>
                  <TableCell sx={{ py: 2, borderBottom: '1px solid #f0f0f0' }}>
                    <StatusPill status={notice.status} />
                  </TableCell>
                  <TableCell align="center" sx={{ py: 2, borderBottom: '1px solid #f0f0f0' }}>
                    <Button
                      className="view-button"
                      size="small"
                      variant="outlined"
                      startIcon={<VisibilityIcon sx={{ fontSize: '14px' }} />}
                      onClick={(e) => {
                        e.stopPropagation(); // Prevent row click if we add row click later
                        onViewNotice(notice.id);
                      }}
                      sx={{
                        fontSize: '12px',
                        fontFamily: '"Inter", "Roboto", sans-serif',
                        fontWeight: '500',
                        textTransform: 'none',
                        borderRadius: '6px',
                        px: 2,
                        py: 0.5,
                        minWidth: '80px',
                        border: '1px solid #e0e0e0',
                        color: '#666666',
                        transition: 'all 0.2s ease',
                        '&:hover': {
                          backgroundColor: '#1976d2',
                          borderColor: '#1976d2',
                          color: '#ffffff',
                          transform: 'translateY(-1px)',
                          boxShadow: '0 2px 8px rgba(25, 118, 210, 0.3)',
                        },
                        '&:active': {
                          transform: 'translateY(0)',
                        }
                      }}
                    >
                      View
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
        {notices.length === 0 && (
          <Box sx={{ textAlign: 'center', py: 6 }}>
            <Typography
              variant="body2"
              sx={{
                color: '#666666',
                fontSize: '14px',
                fontFamily: '"Inter", "Roboto", sans-serif'
              }}
            >
              No recent notices found
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default RecentNoticesTable;