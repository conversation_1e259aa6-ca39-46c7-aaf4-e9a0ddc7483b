#!/usr/bin/env python3
"""
Very simple test server
"""
import http.server
import socketserver
import json
from urllib.parse import urlparse, parse_qs

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        parsed_path = urlparse(self.path)
        
        # Set CORS headers
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        
        # Route handling
        if parsed_path.path == '/':
            response = {
                "message": "Welcome to GST Notice Management System",
                "version": "1.0.0",
                "docs_url": "/docs"
            }
        elif parsed_path.path == '/health':
            response = {
                "status": "healthy",
                "app_name": "GST Notice Management System",
                "version": "1.0.0"
            }
        elif parsed_path.path == '/api/v1/dashboard/stats':
            response = {
                "total_clients": 25,
                "total_notices": 150,
                "new_notices": 8,
                "pending_replies": 12,
                "notices_today": 3,
                "overdue_notices": 2
            }
        elif parsed_path.path == '/api/v1/clients/':
            response = [
                {
                    "id": 1,
                    "gstin": "27AAPFU0939F1ZV",
                    "client_name": "ABC Enterprises Pvt Ltd",
                    "contact_person": "Amit Sharma",
                    "email": "<EMAIL>",
                    "phone": "+919876543210",
                    "total_notices": 15,
                    "pending_replies": 3,
                    "last_sync_at": "2024-12-22T10:30:00Z",
                    "auto_sync_enabled": True,
                    "is_active": True
                }
            ]
        elif parsed_path.path == '/api/v1/notices/download/history':
            response = {
                "success": True,
                "message": "Download history retrieved successfully",
                "data": {
                    "history": [
                        {
                            "id": 1,
                            "client_gstin": "27AAPFU0939F1ZV",
                            "download_type": "first_time",
                            "start_date": "2023-01-01",
                            "end_date": "2024-01-01",
                            "notices_found": 45,
                            "notices_downloaded": 45,
                            "status": "completed",
                            "started_at": "2024-01-15T09:00:00Z",
                            "completed_at": "2024-01-15T09:15:00Z"
                        },
                        {
                            "id": 2,
                            "client_gstin": "27AAPFU0939F1ZV",
                            "download_type": "auto_fetch",
                            "start_date": "2024-01-10",
                            "end_date": "2024-01-17",
                            "notices_found": 3,
                            "notices_downloaded": 2,
                            "status": "completed",
                            "started_at": "2024-01-17T09:00:00Z",
                            "completed_at": "2024-01-17T09:05:00Z"
                        }
                    ],
                    "total": 2
                }
            }
        elif parsed_path.path == '/api/v1/notices/schedule/jobs':
            response = {
                "success": True,
                "message": "Scheduled jobs retrieved successfully",
                "data": {
                    "jobs": [
                        {
                            "id": "auto_fetch_27AAPFU0939F1ZV",
                            "name": "Auto-fetch notices for 27AAPFU0939F1ZV",
                            "next_run": "2024-01-18T09:00:00Z",
                            "trigger": "cron[hour=9, minute=0]"
                        }
                    ],
                    "total": 1
                }
            }
        elif parsed_path.path.startswith('/api/v1/notices/credentials/'):
            client_gstin = parsed_path.path.split('/')[-1]
            response = {
                "success": True,
                "message": "Credentials found",
                "data": {
                    "client_name": "Sample Client Ltd",
                    "username": "sample_user",
                    "last_used": "2024-01-15T09:00:00Z",
                    "has_credentials": True
                }
            }
        else:
            response = {"error": "Not found", "path": parsed_path.path}
        
        self.wfile.write(json.dumps(response).encode())
    
    def do_POST(self):
        # Handle POST requests
        parsed_path = urlparse(self.path)

        # Read request body
        content_length = int(self.headers.get('Content-Length', 0))
        post_data = self.rfile.read(content_length)

        try:
            request_data = json.loads(post_data.decode('utf-8')) if post_data else {}
        except:
            request_data = {}

        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS, DELETE')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

        # Route handling for POST requests
        if parsed_path.path == '/api/v1/notices/credentials':
            response = {
                "success": True,
                "message": "Credentials stored successfully",
                "data": {"client_gstin": request_data.get("client_gstin", "")}
            }
        elif parsed_path.path == '/api/v1/notices/download/first-time':
            response = {
                "success": True,
                "message": "First-time download started in background",
                "data": {
                    "client_gstin": request_data.get("client_gstin", ""),
                    "date_range": f"{request_data.get('start_date', '')} to {request_data.get('end_date', '')}",
                    "status": "started"
                }
            }
        elif parsed_path.path.startswith('/api/v1/notices/download/auto-fetch/'):
            client_gstin = parsed_path.path.split('/')[-1]
            response = {
                "success": True,
                "message": "Auto-fetch started in background",
                "data": {
                    "client_gstin": client_gstin,
                    "status": "started"
                }
            }
        elif parsed_path.path == '/api/v1/notices/schedule':
            response = {
                "success": True,
                "message": f"Auto-fetch scheduled successfully ({request_data.get('schedule_type', 'daily')})",
                "data": {
                    "client_gstin": request_data.get("client_gstin", ""),
                    "schedule_type": request_data.get("schedule_type", "daily"),
                    "schedule_details": {
                        "hour": request_data.get("hour", 9),
                        "minute": request_data.get("minute", 0),
                        "interval_hours": request_data.get("interval_hours", 12)
                    }
                }
            }
        elif parsed_path.path == '/api/v1/notices/download/manual-fetch-all':
            response = {
                "success": True,
                "message": "Manual fetch started for all active clients",
                "data": {"status": "started"}
            }
        else:
            response = {"success": False, "message": "Endpoint not found", "path": parsed_path.path}

        self.wfile.write(json.dumps(response).encode())

    def do_DELETE(self):
        # Handle DELETE requests
        parsed_path = urlparse(self.path)

        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS, DELETE')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

        # Route handling for DELETE requests
        if parsed_path.path.startswith('/api/v1/notices/schedule/'):
            client_gstin = parsed_path.path.split('/')[-1]
            response = {
                "success": True,
                "message": "Auto-fetch unscheduled successfully",
                "data": {"client_gstin": client_gstin}
            }
        else:
            response = {"success": False, "message": "Endpoint not found", "path": parsed_path.path}

        self.wfile.write(json.dumps(response).encode())
    
    def do_OPTIONS(self):
        # Handle preflight requests
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()

if __name__ == "__main__":
    PORT = 8000
    
    with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
        print(f"🚀 GST Notice Management Backend Server")
        print(f"📡 Server running at: http://localhost:{PORT}")
        print(f"📚 API Documentation: http://localhost:{PORT}/docs")
        print(f"❤️  Health Check: http://localhost:{PORT}/health")
        print(f"📊 Dashboard Stats: http://localhost:{PORT}/api/v1/dashboard/stats")
        print(f"👥 Clients: http://localhost:{PORT}/api/v1/clients/")
        print(f"\n🔥 Server is ready! Press Ctrl+C to stop.")
        print("-" * 60)
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 Server stopped.")
            httpd.shutdown()
