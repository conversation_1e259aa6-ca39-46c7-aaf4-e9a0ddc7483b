"""
Settings model for system configuration
"""
from sqlalchemy import <PERSON>um<PERSON>, <PERSON>, Inte<PERSON>, <PERSON>ole<PERSON>, Text, JSON, Enum as SQLEnum
import enum
from .base import BaseModel


class SettingType(str, enum.Enum):
    SYSTEM = "system"
    EMAIL = "email"
    GST_PORTAL = "gst_portal"
    NOTIFICATION = "notification"
    SYNC = "sync"


class Setting(BaseModel):
    __tablename__ = "settings"
    
    # Setting identification
    key = Column(String(100), unique=True, index=True, nullable=False)
    category = Column(SQLEnum(SettingType), nullable=False)
    
    # Setting values
    value = Column(Text, nullable=True)
    json_value = Column(JSON, nullable=True)  # For complex settings
    
    # Metadata
    description = Column(Text, nullable=True)
    is_encrypted = Column(Boolean, default=False, nullable=False)
    is_public = Column(Boolean, default=False, nullable=False)  # Can be accessed by frontend
    
    # Validation
    validation_rules = Column(JSO<PERSON>, nullable=True)  # JSON schema for validation
    
    def __repr__(self):
        return f"<Setting(key='{self.key}', category='{self.category}')>"


# Predefined settings with their default values
DEFAULT_SETTINGS = {
    # System Settings
    "system.app_name": {
        "category": SettingType.SYSTEM,
        "value": "GST Notice Management System",
        "description": "Application name",
        "is_public": True
    },
    "system.maintenance_mode": {
        "category": SettingType.SYSTEM,
        "value": "false",
        "description": "Enable maintenance mode",
        "is_public": True
    },
    
    # Email Settings
    "email.smtp_host": {
        "category": SettingType.EMAIL,
        "value": "smtp.gmail.com",
        "description": "SMTP server host"
    },
    "email.smtp_port": {
        "category": SettingType.EMAIL,
        "value": "587",
        "description": "SMTP server port"
    },
    "email.use_tls": {
        "category": SettingType.EMAIL,
        "value": "true",
        "description": "Use TLS for email"
    },
    "email.default_from_name": {
        "category": SettingType.EMAIL,
        "value": "GST Notice System",
        "description": "Default sender name"
    },
    
    # GST Portal Settings
    "gst.fetch_interval_hours": {
        "category": SettingType.GST_PORTAL,
        "value": "24",
        "description": "Notice fetch interval in hours"
    },
    "gst.max_fetch_years": {
        "category": SettingType.GST_PORTAL,
        "value": "2",
        "description": "Maximum years to fetch notices for new clients"
    },
    "gst.batch_size": {
        "category": SettingType.GST_PORTAL,
        "value": "100",
        "description": "Batch size for notice processing"
    },
    
    # Sync Settings
    "sync.auto_sync_enabled": {
        "category": SettingType.SYNC,
        "value": "true",
        "description": "Enable automatic notice synchronization"
    },
    "sync.retry_failed_after_hours": {
        "category": SettingType.SYNC,
        "value": "6",
        "description": "Retry failed syncs after specified hours"
    },
    
    # Notification Settings
    "notification.email_on_new_notice": {
        "category": SettingType.NOTIFICATION,
        "value": "true",
        "description": "Send email notification on new notice"
    },
    "notification.email_on_due_date": {
        "category": SettingType.NOTIFICATION,
        "value": "true",
        "description": "Send email notification before due date"
    }
}
