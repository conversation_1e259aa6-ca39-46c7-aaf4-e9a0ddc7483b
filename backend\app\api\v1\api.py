"""
API v1 router
"""
from fastapi import APIRouter
from .endpoints import auth, users, clients, notices, dashboard, settings, sync

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(auth.router, prefix="/auth", tags=["Authentication"])
api_router.include_router(users.router, prefix="/users", tags=["Users"])
api_router.include_router(clients.router, prefix="/clients", tags=["Clients"])
api_router.include_router(notices.router, prefix="/notices", tags=["Notices"])
api_router.include_router(dashboard.router, prefix="/dashboard", tags=["Dashboard"])
api_router.include_router(settings.router, prefix="/settings", tags=["Settings"])
api_router.include_router(sync.router, prefix="/sync", tags=["Sync"])
