import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  Button,
  Stack,
  Box,
  IconButton,
  TextField,
  Divider,
  Chip
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import DownloadIcon from '@mui/icons-material/Download';
import SendIcon from '@mui/icons-material/Send';
import EditIcon from '@mui/icons-material/Edit';
import StatusPill from '../common/StatusPill';
import { Notice } from '../../types/schema';
import { formatDate, formatGSTIN } from '../../utils/formatters';

interface NoticeDetailModalProps {
  notice: Notice | null;
  open: boolean;
  onClose: () => void;
  onSendReply: (noticeId: string, reply: string) => void;
}

const NoticeDetailModal: React.FC<NoticeDetailModalProps> = ({ 
  notice, 
  open, 
  onClose, 
  onSendReply 
}) => {
  const [reply, setReply] = useState('');
  const [editingReply, setEditingReply] = useState(false);

  if (!notice) return null;

  const isOverdue = new Date(notice.dueDate) < new Date();

  const generateAutoReply = () => {
    const autoReply = `Dear Sir/Madam,

We acknowledge receipt of the ${notice.noticeType} dated ${formatDate(new Date(notice.receivedDate))} regarding ${notice.subject}.

We are reviewing the matter and will provide a detailed response within the stipulated time frame.

For any clarifications, please contact us at the details mentioned below.

Thank you for your patience.

Regards,
[Your Name]
[Your Designation]
[Contact Details]`;

    setReply(autoReply);
    setEditingReply(true);
  };

  const handleSendReply = () => {
    if (reply.trim()) {
      onSendReply(notice.id, reply);
      setReply('');
      setEditingReply(false);
      onClose();
    }
  };

  const handleDownload = () => {
    // Simulate download
    window.open(notice.attachmentUrl, '_blank');
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Typography variant="h6">Notice Details</Typography>
          <IconButton onClick={onClose}>
            <CloseIcon />
          </IconButton>
        </Stack>
      </DialogTitle>

      <DialogContent>
        <Stack spacing={3}>
          {/* Notice Header */}
          <Box>
            <Stack direction="row" alignItems="center" spacing={2} mb={2}>
              <Typography variant="h5" fontWeight="bold">
                {notice.id}
              </Typography>
              <StatusPill status={notice.status} />
              {isOverdue && (
                <Chip label="Overdue" color="error" size="small" />
              )}
            </Stack>
            <Typography variant="h6" color="text.secondary">
              {notice.noticeType}
            </Typography>
          </Box>

          <Divider />

          {/* Notice Details */}
          <Stack spacing={2}>
            <Typography variant="h6">{notice.subject}</Typography>
            
            <Stack direction={{ xs: 'column', sm: 'row' }} spacing={3}>
              <Box flex={1}>
                <Typography variant="body2" color="text.secondary">Client</Typography>
                <Typography variant="body1" fontWeight={500}>{notice.clientName}</Typography>
                <Typography variant="body2" color="primary">
                  GSTIN: {formatGSTIN(notice.gstin)}
                </Typography>
              </Box>
              
              <Box flex={1}>
                <Typography variant="body2" color="text.secondary">Received Date</Typography>
                <Typography variant="body1">{formatDate(new Date(notice.receivedDate))}</Typography>
                
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>Due Date</Typography>
                <Typography variant="body1" color={isOverdue ? 'error.main' : 'text.primary'}>
                  {formatDate(new Date(notice.dueDate))}
                </Typography>
              </Box>
            </Stack>

            <Box>
              <Typography variant="body2" color="text.secondary">Description</Typography>
              <Typography variant="body1">{notice.description}</Typography>
            </Box>
          </Stack>

          <Divider />

          {/* Reply Section */}
          <Box>
            <Typography variant="h6" gutterBottom>Reply</Typography>
            
            {!editingReply ? (
              <Stack direction="row" spacing={2}>
                <Button
                  variant="contained"
                  onClick={generateAutoReply}
                  sx={{ flex: 1 }}
                >
                  Generate Auto Reply
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<EditIcon />}
                  onClick={() => setEditingReply(true)}
                  sx={{ flex: 1 }}
                >
                  Write Custom Reply
                </Button>
              </Stack>
            ) : (
              <Stack spacing={2}>
                <TextField
                  multiline
                  rows={8}
                  value={reply}
                  onChange={(e) => setReply(e.target.value)}
                  placeholder="Write your reply here..."
                  fullWidth
                />
                <Stack direction="row" spacing={2}>
                  <Button
                    variant="outlined"
                    onClick={() => {
                      setEditingReply(false);
                      setReply('');
                    }}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="contained"
                    startIcon={<SendIcon />}
                    onClick={handleSendReply}
                    disabled={!reply.trim()}
                  >
                    Send Reply
                  </Button>
                </Stack>
              </Stack>
            )}
          </Box>
        </Stack>
      </DialogContent>

      <DialogActions sx={{ p: 3 }}>
        <Button
          variant="outlined"
          startIcon={<DownloadIcon />}
          onClick={handleDownload}
        >
          Download Notice
        </Button>
        <Button onClick={onClose}>Close</Button>
      </DialogActions>
    </Dialog>
  );
};

export default NoticeDetailModal;