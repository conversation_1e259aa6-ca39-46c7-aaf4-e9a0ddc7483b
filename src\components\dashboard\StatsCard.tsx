import React from 'react';
import { Card, CardContent, Typography, Box, Stack } from '@mui/material';
import { SvgIconComponent } from '@mui/icons-material';

interface StatsCardProps {
  title: string;
  value: number;
  icon: SvgIconComponent;
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
  subtitle?: string;
}

const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  icon: Icon,
  color = 'primary',
  subtitle
}) => {
  const colorMap = {
    primary: '#1976d2',
    secondary: '#9c27b0',
    success: '#2e7d32',
    error: '#d32f2f',
    warning: '#ed6c02',
    info: '#0288d1'
  };

  return (
    <Card sx={{
      height: '100%',
      boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
      borderRadius: '12px',
      border: '1px solid #f0f0f0',
      transition: 'all 0.2s ease-in-out',
      '&:hover': {
        boxShadow: '0 4px 16px rgba(0,0,0,0.15)',
        transform: 'translateY(-2px)'
      },
      backgroundColor: 'background.paper'
    }}>
      <CardContent sx={{
        p: 3,
        '&:last-child': { pb: 3 }
      }}>
        {/* Icon and Title Row */}
        <Box display="flex" alignItems="center" mb={2}>
          <Box sx={{
            backgroundColor: `${colorMap[color]}15`,
            borderRadius: '8px',
            p: 1,
            mr: 2,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <Icon sx={{
              color: colorMap[color],
              fontSize: '20px'
            }} />
          </Box>
          <Typography
            variant="h6"
            fontWeight="600"
            sx={{
              fontSize: '16px',
              color: '#1a1a1a',
              fontFamily: '"Inter", "Roboto", sans-serif'
            }}
          >
            {title}
          </Typography>
        </Box>

        {/* Value */}
        <Typography
          variant="h3"
          fontWeight="700"
          sx={{
            fontSize: '32px',
            color: colorMap[color],
            mb: 1,
            fontFamily: '"Inter", "Roboto", sans-serif'
          }}
        >
          {value.toLocaleString()}
        </Typography>

        {/* Subtitle */}
        {subtitle && (
          <Typography
            variant="body2"
            sx={{
              color: '#666666',
              fontSize: '13px',
              fontWeight: '400',
              lineHeight: 1.4,
              fontFamily: '"Inter", "Roboto", sans-serif'
            }}
          >
            {subtitle}
          </Typography>
        )}
      </CardContent>
    </Card>
  );
};

export default StatsCard;