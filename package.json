{"name": "ae7c17fba7578239f8b3705448428de9018ca9991f43cf34f65d3bb974563b75", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@kombai/react-error-boundary": "^1.1.0", "@mui/icons-material": "^7.3.1", "@mui/material": "^7.3.1", "@mui/x-charts": "^8.9.2", "@mui/x-data-grid": "^8.9.2", "@mui/x-date-pickers": "^8.9.2", "@mui/x-tree-view": "^8.9.2", "@reduxjs/toolkit": "^2.8.2", "axios": "^1.11.0", "date-fns": "^4.1.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-redux": "^9.2.0", "react-router": "^7.7.1", "react-router-dom": "^7.7.1"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/node": "^24.2.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.7.0", "comment-json": "^4.2.5", "env-cmd": "^10.1.0", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "rollup": "^4.44.1", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0", "vite-plugin-static-copy": "^3.1.1", "vite-tsconfig-paths": "^5.1.4"}, "strictDependencies": {}}