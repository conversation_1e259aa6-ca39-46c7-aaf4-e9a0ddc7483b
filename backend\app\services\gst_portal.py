"""
GST Portal integration service for fetching notices and managing sessions
"""
import asyncio
import hashlib
import json
from datetime import datetime, timed<PERSON><PERSON>
from typing import List, Dict, Optional, <PERSON><PERSON>
import httpx
from sqlalchemy.orm import Session
from ..core.config import settings
from ..core.security import decrypt_sensitive_data, SecurityUtils
from ..models import Client, Notice, NoticeStatus, NoticeType, SyncLog
from ..schemas.notice import NoticeCreate
import logging

logger = logging.getLogger(__name__)


class GSTPortalError(Exception):
    """Custom exception for GST Portal related errors"""
    pass


class GSTPortalService:
    """Service for interacting with GST Portal APIs"""
    
    def __init__(self):
        self.base_url = settings.GST_PORTAL_BASE_URL
        self.api_key = settings.GST_API_KEY
        self.client_id = settings.GST_CLIENT_ID
        self.client_secret = settings.GST_CLIENT_SECRET
        self.session_cache = {}  # In-memory session cache
        
    async def authenticate_client(self, client: Client) -> str:
        """
        Authenticate with GST portal for a specific client
        Returns session token
        """
        try:
            # Decrypt client credentials
            username = client.gst_username
            password = decrypt_sensitive_data(client.gst_password_encrypted)
            
            # Check if we have a valid cached session
            cache_key = f"{client.gstin}_{username}"
            if cache_key in self.session_cache:
                session_data = self.session_cache[cache_key]
                if session_data['expires_at'] > datetime.utcnow():
                    return session_data['token']
            
            # Authenticate with GST portal
            auth_data = {
                "gstin": client.gstin,
                "username": username,
                "password": password,
                "client_id": self.client_id,
                "client_secret": self.client_secret
            }
            
            async with httpx.AsyncClient(timeout=30.0) as http_client:
                response = await http_client.post(
                    f"{self.base_url}/auth/login",
                    json=auth_data,
                    headers={"X-API-Key": self.api_key}
                )
                
                if response.status_code != 200:
                    raise GSTPortalError(f"Authentication failed: {response.text}")
                
                auth_response = response.json()
                session_token = auth_response.get("session_token")
                expires_in = auth_response.get("expires_in", 3600)  # Default 1 hour
                
                if not session_token:
                    raise GSTPortalError("No session token received from GST portal")
                
                # Cache the session
                self.session_cache[cache_key] = {
                    "token": session_token,
                    "expires_at": datetime.utcnow() + timedelta(seconds=expires_in - 300)  # 5 min buffer
                }
                
                logger.info(f"Successfully authenticated client {client.gstin}")
                return session_token
                
        except httpx.TimeoutException:
            raise GSTPortalError("GST Portal authentication timeout")
        except Exception as e:
            logger.error(f"Authentication error for client {client.gstin}: {str(e)}")
            raise GSTPortalError(f"Authentication failed: {str(e)}")
    
    async def fetch_notices(
        self, 
        client: Client, 
        from_date: datetime, 
        to_date: datetime,
        session_token: str
    ) -> List[Dict]:
        """
        Fetch notices from GST portal for a specific client and date range
        """
        try:
            headers = {
                "Authorization": f"Bearer {session_token}",
                "X-API-Key": self.api_key,
                "Content-Type": "application/json"
            }
            
            params = {
                "gstin": client.gstin,
                "from_date": from_date.strftime("%Y-%m-%d"),
                "to_date": to_date.strftime("%Y-%m-%d"),
                "limit": settings.BATCH_SIZE
            }
            
            all_notices = []
            offset = 0
            
            async with httpx.AsyncClient(timeout=60.0) as http_client:
                while True:
                    params["offset"] = offset
                    
                    response = await http_client.get(
                        f"{self.base_url}/notices/list",
                        params=params,
                        headers=headers
                    )
                    
                    if response.status_code == 401:
                        raise GSTPortalError("Session expired, re-authentication required")
                    elif response.status_code != 200:
                        raise GSTPortalError(f"Failed to fetch notices: {response.text}")
                    
                    data = response.json()
                    notices = data.get("notices", [])
                    
                    if not notices:
                        break
                    
                    all_notices.extend(notices)
                    
                    # Check if we have more data
                    if len(notices) < settings.BATCH_SIZE:
                        break
                    
                    offset += settings.BATCH_SIZE
                    
                    # Add small delay to avoid rate limiting
                    await asyncio.sleep(0.1)
            
            logger.info(f"Fetched {len(all_notices)} notices for client {client.gstin}")
            return all_notices
            
        except httpx.TimeoutException:
            raise GSTPortalError("GST Portal fetch timeout")
        except Exception as e:
            logger.error(f"Error fetching notices for client {client.gstin}: {str(e)}")
            raise GSTPortalError(f"Failed to fetch notices: {str(e)}")
    
    async def download_notice_document(
        self, 
        notice_id: str, 
        session_token: str,
        client_gstin: str
    ) -> Tuple[bytes, str]:
        """
        Download notice document from GST portal
        Returns (file_content, filename)
        """
        try:
            headers = {
                "Authorization": f"Bearer {session_token}",
                "X-API-Key": self.api_key
            }
            
            async with httpx.AsyncClient(timeout=120.0) as http_client:
                response = await http_client.get(
                    f"{self.base_url}/notices/{notice_id}/download",
                    headers=headers
                )
                
                if response.status_code == 401:
                    raise GSTPortalError("Session expired, re-authentication required")
                elif response.status_code != 200:
                    raise GSTPortalError(f"Failed to download notice: {response.text}")
                
                # Extract filename from headers
                content_disposition = response.headers.get("content-disposition", "")
                filename = f"notice_{notice_id}_{client_gstin}.pdf"
                if "filename=" in content_disposition:
                    filename = content_disposition.split("filename=")[1].strip('"')
                
                return response.content, filename
                
        except httpx.TimeoutException:
            raise GSTPortalError("Notice download timeout")
        except Exception as e:
            logger.error(f"Error downloading notice {notice_id}: {str(e)}")
            raise GSTPortalError(f"Failed to download notice: {str(e)}")
    
    def parse_notice_data(self, notice_data: Dict, client: Client) -> NoticeCreate:
        """
        Parse notice data from GST portal response into our schema
        """
        # Map notice type from GST portal to our enum
        notice_type_mapping = {
            "GST_NOTICE": NoticeType.GST_NOTICE,
            "SHOW_CAUSE": NoticeType.SHOW_CAUSE,
            "DEMAND_NOTICE": NoticeType.DEMAND_NOTICE,
            "PENALTY": NoticeType.PENALTY_NOTICE,
            "ASSESSMENT": NoticeType.ASSESSMENT_ORDER,
        }
        
        notice_type = notice_type_mapping.get(
            notice_data.get("type", "").upper(), 
            NoticeType.OTHER
        )
        
        # Parse dates
        notice_date = datetime.fromisoformat(notice_data.get("notice_date", ""))
        due_date = None
        if notice_data.get("due_date"):
            due_date = datetime.fromisoformat(notice_data["due_date"])
        
        # Generate file hash for duplicate detection
        content_hash = hashlib.sha256(
            f"{notice_data.get('notice_id', '')}{client.gstin}".encode()
        ).hexdigest()
        
        return NoticeCreate(
            notice_id=notice_data.get("notice_id", ""),
            notice_number=notice_data.get("notice_number"),
            subject=notice_data.get("subject", ""),
            notice_type=notice_type,
            status=NoticeStatus.NEW,
            notice_date=notice_date,
            due_date=due_date,
            received_date=datetime.utcnow(),
            description=notice_data.get("description"),
            content=notice_data.get("content"),
            file_hash=content_hash,
            auto_downloaded=True,
            portal_metadata=notice_data,
            client_id=client.id
        )
    
    def calculate_file_hash(self, file_content: bytes) -> str:
        """Calculate SHA256 hash of file content"""
        return hashlib.sha256(file_content).hexdigest()
    
    async def logout_session(self, session_token: str):
        """Logout from GST portal session"""
        try:
            headers = {
                "Authorization": f"Bearer {session_token}",
                "X-API-Key": self.api_key
            }
            
            async with httpx.AsyncClient(timeout=10.0) as http_client:
                await http_client.post(
                    f"{self.base_url}/auth/logout",
                    headers=headers
                )
        except Exception as e:
            logger.warning(f"Failed to logout session: {str(e)}")
    
    def clear_session_cache(self, client_gstin: str = None):
        """Clear session cache for specific client or all clients"""
        if client_gstin:
            # Clear cache for specific client
            keys_to_remove = [k for k in self.session_cache.keys() if k.startswith(client_gstin)]
            for key in keys_to_remove:
                del self.session_cache[key]
        else:
            # Clear all cache
            self.session_cache.clear()


# Global instance
gst_portal_service = GSTPortalService()
