"""
Notice model for GST notices
"""
from sqlalchemy import Column, <PERSON>, Integer, ForeignKey, DateTime, Text, Enum as SQL<PERSON><PERSON>, <PERSON>SO<PERSON>, Boolean
from sqlalchemy.orm import relationship
import enum
from .base import BaseModel


class NoticeStatus(str, enum.Enum):
    NEW = "new"
    REVIEWED = "reviewed"
    REPLIED = "replied"
    PENDING = "pending"
    CLOSED = "closed"


class NoticeType(str, enum.Enum):
    GST_NOTICE = "gst_notice"
    SHOW_CAUSE = "show_cause"
    DEMAND_NOTICE = "demand_notice"
    PENALTY_NOTICE = "penalty_notice"
    ASSESSMENT_ORDER = "assessment_order"
    OTHER = "other"


class NoticePriority(str, enum.Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"


class Notice(BaseModel):
    __tablename__ = "notices"
    
    # Notice Identification
    notice_id = Column(String(100), unique=True, index=True, nullable=False)  # From GST portal
    notice_number = Column(String(100), nullable=True)
    
    # Basic Information
    subject = Column(String(500), nullable=False)
    notice_type = Column(SQLEnum(NoticeType), default=NoticeType.GST_NOTICE, nullable=False)
    status = Column(SQLEnum(NoticeStatus), default=NoticeStatus.NEW, nullable=False)
    priority = Column(SQLEnum(NoticePriority), default=NoticePriority.MEDIUM, nullable=False)
    
    # Dates
    notice_date = Column(DateTime(timezone=True), nullable=False)
    due_date = Column(DateTime(timezone=True), nullable=True)
    received_date = Column(DateTime(timezone=True), nullable=False)
    
    # Content
    description = Column(Text, nullable=True)
    content = Column(Text, nullable=True)  # Full notice content
    
    # File Information
    original_filename = Column(String(255), nullable=True)
    file_path = Column(String(500), nullable=True)  # Local storage path
    file_size = Column(Integer, nullable=True)
    file_hash = Column(String(64), nullable=True)  # For duplicate detection
    
    # Processing Information
    auto_downloaded = Column(Boolean, default=False, nullable=False)
    processed_at = Column(DateTime(timezone=True), nullable=True)
    
    # Additional metadata from GST portal
    portal_metadata = Column(JSON, nullable=True)
    
    # Foreign Keys
    client_id = Column(Integer, ForeignKey("clients.id"), nullable=False)
    
    # Relationships
    client = relationship("Client", back_populates="notices")
    email_replies = relationship("EmailReply", back_populates="notice", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Notice(id='{self.notice_id}', subject='{self.subject[:50]}...')>"
