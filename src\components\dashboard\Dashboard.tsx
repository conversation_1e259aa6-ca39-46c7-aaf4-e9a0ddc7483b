import React, { useState } from 'react';
import { Box, Stack, Typography, CircularProgress, Alert } from '@mui/material';
import PeopleIcon from '@mui/icons-material/People';
import DescriptionIcon from '@mui/icons-material/Description';
import EmailIcon from '@mui/icons-material/Email';
import DownloadIcon from '@mui/icons-material/Download';
import StatsCard from './StatsCard';
import NoticeChart from './NoticeChart';
import RecentNoticesTable from './RecentNoticesTable';
import NoticeDetailModal from '../notices/NoticeDetailModal';
import { DashboardStats, ChartData, Notice } from '../../types/schema';

interface DashboardProps {
  stats: DashboardStats;
  chartData: ChartData[];
  recentNotices: Notice[];
  onViewNotice: (noticeId: string) => void;
  onSendReply?: (noticeId: string, reply: string) => void;
  loading?: boolean;
  error?: string | null;
}

const Dashboard: React.FC<DashboardProps> = ({
  stats,
  chartData,
  recentNotices,
  onViewNotice,
  onSendReply,
  loading = false,
  error = null
}) => {
  const [selectedNotice, setSelectedNotice] = useState<Notice | null>(null);
  const [noticeModalOpen, setNoticeModalOpen] = useState(false);

  const handleViewNotice = (noticeId: string) => {
    const notice = recentNotices.find(n => n.id === noticeId);
    if (notice) {
      setSelectedNotice(notice);
      setNoticeModalOpen(true);
    }
    // Also call the original handler if provided
    onViewNotice(noticeId);
  };

  const handleCloseNoticeModal = () => {
    setNoticeModalOpen(false);
    setSelectedNotice(null);
  };

  const handleSendReply = (noticeId: string, reply: string) => {
    if (onSendReply) {
      onSendReply(noticeId, reply);
    }
    handleCloseNoticeModal();
  };
  if (loading) {
    return (
      <Box sx={{ p: 3, display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px' }}>
        <Stack alignItems="center" spacing={2}>
          <CircularProgress size={40} />
          <Typography variant="body1" color="text.secondary">
            Loading dashboard data...
          </Typography>
        </Stack>
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          Failed to load dashboard data: {error}
        </Alert>
        <Typography variant="h4" gutterBottom fontWeight="bold">
          Dashboard Overview
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{
      p: 3,
      backgroundColor: 'background.default',
      minHeight: '100vh',
      fontFamily: '"Inter", "Roboto", sans-serif'
    }}>
      <Box sx={{ mb: 4 }}>
        <Typography
          variant="h4"
          gutterBottom
          fontWeight="700"
          sx={{
            color: '#1a1a1a',
            fontSize: '28px',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            mb: 1
          }}
        >
          ERPCA Tax Engine Dashboard
        </Typography>
        <Typography
          variant="body1"
          sx={{
            color: '#666666',
            fontSize: '16px',
            fontFamily: '"Inter", "Roboto", sans-serif'
          }}
        >
          AI-powered GST, IT & TDS notice management system
        </Typography>
      </Box>

      <Stack spacing={4}>
        {/* Stats Cards - Single Row Grid Layout */}
        <Box sx={{
          display: 'grid',
          gridTemplateColumns: {
            xs: '1fr',
            sm: 'repeat(2, 1fr)',
            md: 'repeat(3, 1fr)',
            lg: 'repeat(5, 1fr)'
          },
          gap: 3,
          mb: 4
        }}>
          <StatsCard
            title="Total Notices Accessed"
            value={stats.totalClients * 8} // Mock calculation
            icon={DescriptionIcon}
            color="primary"
            subtitle="Across all tax modules"
          />
          <StatsCard
            title="Notices Downloaded"
            value={stats.totalClients * 6}
            icon={DownloadIcon}
            color="info"
            subtitle="Successfully retrieved"
          />
          <StatsCard
            title="AI Replies Drafted"
            value={stats.repliesSent + 15} // Mock calculation
            icon={EmailIcon}
            color="success"
            subtitle="Generated by AI engine"
          />
          <StatsCard
            title="Active Clients"
            value={stats.totalClients}
            icon={PeopleIcon}
            color="warning"
            subtitle="GST registrations"
          />
          <StatsCard
            title="Pending Reviews"
            value={Math.max(0, 8)}
            icon={EmailIcon}
            color="error"
            subtitle="Awaiting user review"
          />
        </Box>

        {/* Chart and Recent Notices */}
        <Box sx={{
          display: 'grid',
          gridTemplateColumns: { xs: '1fr', lg: '1fr 1.5fr' },
          gap: 4,
          mt: 2
        }}>
          {/* Pie Chart Section */}
          <Box>
            <NoticeChart data={chartData} />
          </Box>

          {/* Recent Notices Table */}
          <Box>
            <RecentNoticesTable
              notices={recentNotices}
              onViewNotice={handleViewNotice}
            />
          </Box>
        </Box>
      </Stack>

      {/* Notice Detail Modal */}
      <NoticeDetailModal
        notice={selectedNotice}
        open={noticeModalOpen}
        onClose={handleCloseNoticeModal}
        onSendReply={handleSendReply}
      />
    </Box>
  );
};

export default Dashboard;