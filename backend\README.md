# GST Notice Management System - Backend

A comprehensive backend system for managing GST notices, automating downloads from tax portals, and handling email communications for tax professionals.

## Features

### 🔐 Authentication & Authorization
- JWT-based authentication
- Role-based access control (Admin, Consultant, Client)
- Secure password hashing with bcrypt
- Session management

### 👥 Client Management
- Add clients with GST credentials
- Bulk client upload
- Client-wise notice organization
- Automatic sync configuration

### 📄 Notice Management
- Automated notice download from GST portal
- First-time bulk download (configurable years)
- Daily automated sync
- Notice status tracking (New, Reviewed, Replied, Pending, Closed)
- File storage and management

### 🔄 Automated Sync System
- Daily scheduled notice fetching
- Manual sync triggers
- Client-specific sync intervals
- Comprehensive sync logging
- Error handling and retry mechanisms

### 📧 Email System (Framework Ready)
- Email template management
- SMTP configuration
- Automated reply drafts
- Email tracking and status

### 📊 Dashboard & Analytics
- Real-time statistics
- Notice trends and charts
- Client performance metrics
- Sync status monitoring

### ⚙️ Settings Management
- Configurable system settings
- Email templates
- Sync intervals
- Portal configurations

## Technology Stack

- **Framework**: FastAPI (Python 3.11+)
- **Database**: PostgreSQL with SQLAlchemy ORM
- **Background Tasks**: Celery with Redis
- **Authentication**: JWT with python-jose
- **File Storage**: Local filesystem (configurable)
- **API Documentation**: Swagger/OpenAPI
- **Containerization**: Docker & Docker Compose

## Quick Start

### Prerequisites
- Docker and Docker Compose
- Python 3.11+ (for local development)

### Using Docker (Recommended)

1. **Clone and setup**:
```bash
git clone <repository>
cd backend
cp .env.example .env
# Edit .env with your configurations
```

2. **Start services**:
```bash
docker-compose up -d
```

3. **Access the application**:
- API: http://localhost:8000
- API Docs: http://localhost:8000/docs
- Flower (Celery Monitor): http://localhost:5555

### Local Development

1. **Install dependencies**:
```bash
pip install -r requirements.txt
```

2. **Setup database**:
```bash
# Start PostgreSQL and Redis
# Update .env with local database URLs
```

3. **Run migrations**:
```bash
alembic upgrade head
```

4. **Start services**:
```bash
# Terminal 1: API Server
uvicorn app.main:app --reload

# Terminal 2: Celery Worker
celery -A app.celery_app worker --loglevel=info

# Terminal 3: Celery Beat
celery -A app.celery_app beat --loglevel=info
```

## API Endpoints

### Authentication
- `POST /api/v1/auth/register` - Register new user
- `POST /api/v1/auth/login` - Login user
- `GET /api/v1/auth/me` - Get current user
- `POST /api/v1/auth/change-password` - Change password

### Clients
- `GET /api/v1/clients/` - List clients
- `POST /api/v1/clients/` - Create client (triggers initial sync)
- `GET /api/v1/clients/{id}` - Get client details
- `PUT /api/v1/clients/{id}` - Update client
- `DELETE /api/v1/clients/{id}` - Delete client
- `GET /api/v1/clients/stats/overview` - Client statistics

### Notices
- `GET /api/v1/notices/` - List notices with filters
- `GET /api/v1/notices/{id}` - Get notice details
- `PUT /api/v1/notices/{id}` - Update notice
- `GET /api/v1/notices/stats` - Notice statistics

### Sync
- `POST /api/v1/sync/manual` - Manual sync for specific clients
- `POST /api/v1/sync/all` - Sync all clients
- `GET /api/v1/sync/status` - Get sync status
- `GET /api/v1/sync/logs` - Get sync logs

### Dashboard
- `GET /api/v1/dashboard/stats` - Dashboard statistics
- `GET /api/v1/dashboard/recent-notices` - Recent notices
- `GET /api/v1/dashboard/notice-trends` - Notice trends

### Settings
- `GET /api/v1/settings/` - Get settings
- `PUT /api/v1/settings/{key}` - Update setting
- `POST /api/v1/settings/` - Create setting

## Configuration

### Environment Variables

Key environment variables in `.env`:

```env
# Database
DATABASE_URL=postgresql://username:password@localhost:5432/gst_notice_db
DATABASE_URL_ASYNC=postgresql+asyncpg://username:password@localhost:5432/gst_notice_db

# Redis
REDIS_URL=redis://localhost:6379/0

# JWT
SECRET_KEY=your-super-secret-key
ACCESS_TOKEN_EXPIRE_MINUTES=30

# GST Portal
GST_PORTAL_BASE_URL=https://services.gst.gov.in
GST_API_KEY=your-api-key

# Email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
```

### Scheduled Tasks

The system includes several automated tasks:

- **Daily Notice Sync**: 6:00 AM daily
- **Urgent Notice Check**: Every hour
- **Email Digest**: 9:00 AM daily
- **Due Date Reminders**: Every 4 hours
- **Weekly Cleanup**: Sunday 2:00 AM

## Database Schema

### Core Tables
- `users` - User accounts and authentication
- `clients` - GST clients with portal credentials
- `notices` - GST notices and documents
- `email_replies` - Email communications
- `settings` - System configuration
- `logs` - System activity logs
- `sync_logs` - Sync operation logs

## Security Features

- Password encryption with bcrypt
- Sensitive data encryption (GST passwords)
- JWT token authentication
- Role-based access control
- Input validation and sanitization
- SQL injection prevention
- CORS protection

## Monitoring & Logging

- Structured logging with different levels
- Request/response logging
- Performance metrics
- Error tracking
- Celery task monitoring with Flower
- Health check endpoints

## Development

### Adding New Features

1. **Models**: Add to `app/models/`
2. **Schemas**: Add to `app/schemas/`
3. **Services**: Add to `app/services/`
4. **APIs**: Add to `app/api/v1/endpoints/`
5. **Tasks**: Add to `app/tasks/`

### Testing

```bash
# Run tests
pytest

# With coverage
pytest --cov=app tests/
```

### Database Migrations

```bash
# Create migration
alembic revision --autogenerate -m "Description"

# Apply migrations
alembic upgrade head
```

## Production Deployment

### Security Checklist
- [ ] Change default SECRET_KEY
- [ ] Use strong database passwords
- [ ] Configure proper CORS origins
- [ ] Set up SSL/TLS certificates
- [ ] Configure firewall rules
- [ ] Set up monitoring and alerting
- [ ] Regular database backups
- [ ] Log rotation and management

### Performance Optimization
- Database indexing
- Connection pooling
- Caching strategies
- Load balancing
- CDN for static files

## Support

For issues and questions:
1. Check the API documentation at `/docs`
2. Review logs in the `logs/` directory
3. Monitor Celery tasks in Flower
4. Check database connectivity and migrations

## License

[Your License Here]
