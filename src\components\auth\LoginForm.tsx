import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>,
  Button,
  TextField,
  Stack,
  Divider,
  Box,
  CircularProgress
} from '@mui/material';
import GoogleIcon from '@mui/icons-material/Google';
import PhoneIcon from '@mui/icons-material/Phone';

interface LoginFormProps {
  onGoogleLogin: () => void;
  onMobileLogin: (phone: string) => void;
  loading: boolean;
}

const LoginForm: React.FC<LoginFormProps> = ({ onGoogleLogin, onMobileLogin, loading }) => {
  const [phone, setPhone] = useState('');
  const [phoneError, setPhoneError] = useState('');

  const validatePhone = (phoneNumber: string) => {
    const phoneRegex = /^[6-9]\d{9}$/;
    return phoneRegex.test(phoneNumber);
  };

  const handleMobileLogin = () => {
    if (!validatePhone(phone)) {
      setPhoneError('Please enter a valid 10-digit mobile number');
      return;
    }
    setPhoneError('');
    onMobileLogin(phone);
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, '').slice(0, 10);
    setPhone(value);
    if (phoneError) setPhoneError('');
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        bgcolor: 'background.default',
        p: 2
      }}
    >
      <Card sx={{ maxWidth: 400, width: '100%', boxShadow: 3 }}>
        <CardContent sx={{ p: 4 }}>
          <Stack spacing={3} alignItems="center">
            <Typography variant="h4" component="h1" textAlign="center" color="primary">
              GST Tax Portal
            </Typography>
            <Typography variant="body2" color="text.secondary" textAlign="center">
              Sign in to manage your GST compliance
            </Typography>

            <Button
              variant="contained"
              fullWidth
              size="large"
              startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <GoogleIcon />}
              onClick={onGoogleLogin}
              disabled={loading}
              sx={{ py: 1.5 }}
            >
              Continue with Google
            </Button>

            <Divider sx={{ width: '100%' }}>
              <Typography variant="body2" color="text.secondary">
                OR
              </Typography>
            </Divider>

            <Stack spacing={2} sx={{ width: '100%' }}>
              <TextField
                label="Mobile Number"
                value={phone}
                onChange={handlePhoneChange}
                error={!!phoneError}
                helperText={phoneError || 'Enter 10-digit mobile number'}
                placeholder="9876543210"
                InputProps={{
                  startAdornment: (
                    <Typography variant="body2" color="text.secondary" sx={{ mr: 1 }}>
                      +91
                    </Typography>
                  ),
                }}
                fullWidth
              />

              <Button
                variant="outlined"
                fullWidth
                size="large"
                startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <PhoneIcon />}
                onClick={handleMobileLogin}
                disabled={loading || !phone}
                sx={{ py: 1.5 }}
              >
                Send OTP
              </Button>
            </Stack>
          </Stack>
        </CardContent>
      </Card>
    </Box>
  );
};

export default LoginForm;