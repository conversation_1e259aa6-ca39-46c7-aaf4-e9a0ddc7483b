import React from 'react';
import { Card, CardContent, Typography, Box } from '@mui/material';
import { PieChart } from '@mui/x-charts/PieChart';
import { ChartData } from '../../types/schema';

interface NoticeChartProps {
  data: ChartData[];
}

const NoticeChart: React.FC<NoticeChartProps> = ({ data }) => {
  const chartData = data.map((item, index) => ({
    id: index,
    value: item.count,
    label: item.status,
    color: getStatusColor(item.status)
  }));

  function getStatusColor(status: string): string {
    switch (status.toLowerCase()) {
      case 'new':
        return '#1976d2'; // Blue
      case 'replied':
        return '#2e7d32'; // Green
      case 'pending':
        return '#ed6c02'; // Orange
      default:
        return '#64748B'; // gray
    }
  }

  return (
    <Card sx={{
      height: '100%',
      boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
      borderRadius: '12px',
      border: '1px solid #f0f0f0',
      backgroundColor: 'background.paper'
    }}>
      <CardContent sx={{ p: 3 }}>
        <Typography
          variant="h6"
          gutterBottom
          fontWeight="600"
          sx={{
            fontSize: '18px',
            color: '#1a1a1a',
            fontFamily: '"Inter", "Roboto", sans-serif',
            mb: 3
          }}
        >
          Notices by Status
        </Typography>
        <Box sx={{
          height: 280,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center'
        }}>
          <PieChart
            series={[
              {
                data: chartData,
                highlightScope: { faded: 'global', highlighted: 'item' },
                faded: { innerRadius: 30, additionalRadius: -30, color: 'gray' },
                innerRadius: 40,
                outerRadius: 100,
                paddingAngle: 2,
                cornerRadius: 4,
              },
            ]}
            width={350}
            height={280}
            slotProps={{
              legend: {
                direction: 'row',
                position: { vertical: 'bottom', horizontal: 'middle' },
                padding: 0,
                itemMarkWidth: 12,
                itemMarkHeight: 12,
                markGap: 8,
                itemGap: 16,
                labelStyle: {
                  fontSize: '13px',
                  fontFamily: '"Inter", "Roboto", sans-serif',
                  fill: '#666666'
                }
              },
            }}
          />
        </Box>
      </CardContent>
    </Card>
  );
};

export default NoticeChart;