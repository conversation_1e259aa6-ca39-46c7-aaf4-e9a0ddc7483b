"""
Sync management endpoints
"""
from typing import List
from fastapi import APIRouter, Depends, BackgroundTasks, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import and_
from ....core.database import get_db
from ....models import <PERSON>lient, User, SyncLog
from ....schemas.client import ClientSyncRequest
from ....schemas.notice import NoticeSyncResult, DailyNoticeReport
from ....services.notice_service import notice_service
from ..endpoints.auth import get_current_active_user, get_current_admin_user
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/manual")
async def manual_sync(
    sync_request: ClientSyncRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Trigger manual sync for specific clients"""
    # Verify all clients belong to user
    clients = db.query(Client).filter(
        and_(
            Client.id.in_(sync_request.client_ids),
            Client.user_id == current_user.id,
            Client.is_active == True
        )
    ).all()
    
    if len(clients) != len(sync_request.client_ids):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Some clients not found"
        )
    
    # Start sync tasks
    for client in clients:
        background_tasks.add_task(
            sync_client_notices_task,
            client.id,
            current_user.id,
            sync_request.force_sync
        )
    
    logger.info(f"Manual sync started for {len(clients)} clients by user {current_user.email}")
    
    return {
        "message": f"Sync started for {len(clients)} clients",
        "client_ids": [client.id for client in clients]
    }


@router.post("/all")
async def sync_all_clients(
    background_tasks: BackgroundTasks,
    force_sync: bool = False,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Sync all active clients for current user"""
    clients = db.query(Client).filter(
        and_(
            Client.user_id == current_user.id,
            Client.is_active == True,
            Client.auto_sync_enabled == True
        )
    ).all()
    
    # Start sync tasks
    for client in clients:
        background_tasks.add_task(
            sync_client_notices_task,
            client.id,
            current_user.id,
            force_sync
        )
    
    logger.info(f"Sync all started for {len(clients)} clients by user {current_user.email}")
    
    return {
        "message": f"Sync started for {len(clients)} clients",
        "client_count": len(clients)
    }


@router.get("/status")
async def get_sync_status(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get sync status for user's clients"""
    clients = db.query(Client).filter(
        and_(Client.user_id == current_user.id, Client.is_active == True)
    ).all()
    
    sync_status = []
    for client in clients:
        # Determine sync status
        status = "never"
        if client.last_sync_at:
            if client.last_successful_sync_at and client.last_successful_sync_at >= client.last_sync_at:
                status = "success"
            else:
                status = "failed"
        
        # Check if sync is due
        sync_due = False
        if client.auto_sync_enabled and client.last_successful_sync_at:
            next_sync = client.last_successful_sync_at + timedelta(hours=client.sync_interval_hours)
            sync_due = datetime.utcnow() >= next_sync
        
        sync_status.append({
            "client_id": client.id,
            "gstin": client.gstin,
            "client_name": client.client_name,
            "status": status,
            "last_sync": client.last_sync_at,
            "last_successful_sync": client.last_successful_sync_at,
            "sync_due": sync_due,
            "auto_sync_enabled": client.auto_sync_enabled,
            "total_notices": client.total_notices,
            "pending_replies": client.pending_replies
        })
    
    return sync_status


@router.get("/logs")
async def get_sync_logs(
    limit: int = 50,
    offset: int = 0,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get sync logs"""
    logs = db.query(SyncLog).filter(
        SyncLog.user_id == current_user.id
    ).order_by(SyncLog.started_at.desc()).offset(offset).limit(limit).all()
    
    return [
        {
            "id": log.id,
            "sync_id": log.sync_id,
            "sync_type": log.sync_type,
            "started_at": log.started_at,
            "completed_at": log.completed_at,
            "status": log.status,
            "clients_processed": log.clients_processed,
            "notices_found": log.notices_found,
            "notices_downloaded": log.notices_downloaded,
            "notices_failed": log.notices_failed,
            "total_duration_seconds": log.total_duration_seconds,
            "error_message": log.error_message
        }
        for log in logs
    ]


@router.get("/daily-report")
async def get_daily_sync_report(
    date: str = None,  # Format: YYYY-MM-DD
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get daily sync report"""
    if date:
        report_date = datetime.strptime(date, "%Y-%m-%d").date()
    else:
        report_date = datetime.utcnow().date()
    
    # Get sync logs for the date
    start_datetime = datetime.combine(report_date, datetime.min.time())
    end_datetime = start_datetime + timedelta(days=1)
    
    logs = db.query(SyncLog).filter(
        and_(
            SyncLog.user_id == current_user.id,
            SyncLog.started_at >= start_datetime,
            SyncLog.started_at < end_datetime
        )
    ).all()
    
    # Calculate summary
    total_clients_synced = len(set(log.clients_processed for log in logs if log.clients_processed))
    total_notices_found = sum(log.notices_found for log in logs)
    total_notices_downloaded = sum(log.notices_downloaded for log in logs)
    successful_syncs = len([log for log in logs if log.status == "completed"])
    failed_syncs = len([log for log in logs if log.status == "failed"])
    
    # Create sync results
    sync_results = []
    for log in logs:
        sync_results.append(NoticeSyncResult(
            client_id=0,  # Would need to track this in sync log
            client_gstin="",  # Would need to track this in sync log
            notices_found=log.notices_found,
            notices_downloaded=log.notices_downloaded,
            notices_failed=log.notices_failed,
            errors=[log.error_message] if log.error_message else [],
            sync_duration_seconds=log.total_duration_seconds or 0,
            success=log.status == "completed"
        ))
    
    return DailyNoticeReport(
        date=start_datetime,
        total_clients_synced=total_clients_synced,
        total_notices_found=total_notices_found,
        total_notices_downloaded=total_notices_downloaded,
        successful_syncs=successful_syncs,
        failed_syncs=failed_syncs,
        sync_results=sync_results
    )


@router.post("/admin/global-sync")
async def global_sync(
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """Global sync for all active clients (admin only)"""
    clients = db.query(Client).filter(
        and_(Client.is_active == True, Client.auto_sync_enabled == True)
    ).all()
    
    # Start sync tasks
    for client in clients:
        background_tasks.add_task(
            sync_client_notices_task,
            client.id,
            current_user.id,
            False
        )
    
    logger.info(f"Global sync started for {len(clients)} clients by admin {current_user.email}")
    
    return {
        "message": f"Global sync started for {len(clients)} clients",
        "client_count": len(clients)
    }


async def sync_client_notices_task(client_id: int, user_id: int, force_sync: bool = False):
    """Background task to sync notices for a client"""
    try:
        db = next(get_db())
        client = db.query(Client).filter(Client.id == client_id).first()
        
        if not client:
            logger.error(f"Client {client_id} not found for sync")
            return
        
        # Check if sync is needed (unless forced)
        if not force_sync and client.last_successful_sync_at:
            next_sync = client.last_successful_sync_at + timedelta(hours=client.sync_interval_hours)
            if datetime.utcnow() < next_sync:
                logger.info(f"Sync not due for client {client.gstin}, skipping")
                return
        
        # Perform sync
        notices_found, notices_downloaded, errors = await notice_service.sync_new_notices(client, db=db)
        
        logger.info(f"Sync completed for client {client.gstin}: {notices_downloaded} new notices")
        
    except Exception as e:
        logger.error(f"Sync failed for client {client_id}: {str(e)}")
    finally:
        db.close()
