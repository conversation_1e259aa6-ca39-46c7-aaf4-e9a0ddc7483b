import React, { useState } from 'react';
import {
  Box,
  Ty<PERSON>graphy,
  Button,
  TextField,
  Stack,
  InputAdornment,
  Card,
  CardContent,
  <PERSON>bs,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Chip
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import SearchIcon from '@mui/icons-material/Search';
import UploadIcon from '@mui/icons-material/Upload';
import AccountBalanceIcon from '@mui/icons-material/AccountBalance';
import BusinessIcon from '@mui/icons-material/Business';
import ReceiptIcon from '@mui/icons-material/Receipt';
import ScheduleIcon from '@mui/icons-material/Schedule';
import ClientCard from './ClientCard';
import AddClientModal from './AddClientModal';
import BulkUploadModal from './BulkUploadModal';
import { Client } from '../../types/schema';
import { simpleGstApiService as gstApiService, GSTClientDetails } from '../../services/gstApiServiceSimple';

interface ClientListProps {
  clients: Client[];
  onAddClient: (client: Omit<Client, 'id'>) => void;
  onViewClient: (clientId: string) => void;
  onBulkUpload: (file: File) => void;
  onDownloadNotices?: (clientId: string) => void;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => {
  return (
    <div hidden={value !== index}>
      {value === index && <Box>{children}</Box>}
    </div>
  );
};

const ClientList: React.FC<ClientListProps> = ({
  clients,
  onAddClient,
  onViewClient,
  onBulkUpload,
  onDownloadNotices
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [addModalOpen, setAddModalOpen] = useState(false);
  const [bulkUploadOpen, setBulkUploadOpen] = useState(false);
  const [tabValue, setTabValue] = useState(0);

  const filteredClients = clients.filter(client =>
    client.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    client.gstin.toLowerCase().includes(searchTerm.toLowerCase()) ||
    client.contactPerson.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Stack direction="row" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h4" fontWeight="bold">
            Client Management
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage GST, IT, and TDS clients across different tax modules
          </Typography>
        </Box>
        <Stack direction="row" spacing={2}>
          <Button
            variant="outlined"
            startIcon={<UploadIcon />}
            onClick={() => setBulkUploadOpen(true)}
          >
            Bulk Upload
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setAddModalOpen(true)}
          >
            Add Client
          </Button>
        </Stack>
      </Stack>

      {/* Tax Module Tabs */}
      <Card sx={{ mb: 3 }}>
        <CardContent sx={{ p: 0 }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            sx={{ borderBottom: 1, borderColor: 'divider', px: 3 }}
          >
            <Tab
              label={
                <Stack direction="row" alignItems="center" spacing={1}>
                  <AccountBalanceIcon />
                  <span>GST Clients</span>
                  <Chip label={clients.length} size="small" color="primary" />
                </Stack>
              }
            />
            <Tab
              label={
                <Stack direction="row" alignItems="center" spacing={1}>
                  <BusinessIcon />
                  <span>IT Clients</span>
                  <Chip label="Coming Soon" size="small" color="warning" />
                </Stack>
              }
              disabled
            />
            <Tab
              label={
                <Stack direction="row" alignItems="center" spacing={1}>
                  <ReceiptIcon />
                  <span>TDS Clients</span>
                  <Chip label="Coming Soon" size="small" color="warning" />
                </Stack>
              }
              disabled
            />
          </Tabs>

          <TabPanel value={tabValue} index={0}>
            <Box sx={{ p: 3 }}>
              {/* Search and Filter */}
              <TextField
                fullWidth
                placeholder="Search GST clients by name, GSTIN, or contact person..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                sx={{ mb: 3 }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />

              {/* Client Cards */}
              <Stack spacing={2}>
                {filteredClients.map((client) => (
                  <ClientCard
                    key={client.id}
                    client={client}
                    onView={() => onViewClient(client.id)}
                    onDownload={onDownloadNotices ? () => onDownloadNotices(client.id) : undefined}
                  />
                ))}
                {filteredClients.length === 0 && (
                  <Alert severity="info">
                    No GST clients found. {searchTerm ? 'Try adjusting your search terms.' : 'Add your first client to get started.'}
                  </Alert>
                )}
              </Stack>
            </Box>
          </TabPanel>

          <TabPanel value={tabValue} index={1}>
            <Box sx={{ p: 3, textAlign: 'center' }}>
              <ScheduleIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" color="text.secondary">
                IT Clients Coming Soon
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Income Tax client management will be available in the next update
              </Typography>
            </Box>
          </TabPanel>

          <TabPanel value={tabValue} index={2}>
            <Box sx={{ p: 3, textAlign: 'center' }}>
              <ScheduleIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" color="text.secondary">
                TDS Clients Coming Soon
              </Typography>
              <Typography variant="body2" color="text.secondary">
                TDS client management will be available in the next update
              </Typography>
            </Box>
          </TabPanel>
        </CardContent>
      </Card>

      {filteredClients.length === 0 && (
        <Card>
          <CardContent sx={{ textAlign: 'center', py: 6 }}>
            <Typography variant="h6" color="text.secondary" gutterBottom>
              No clients found
            </Typography>
            <Typography variant="body2" color="text.secondary" mb={3}>
              {searchTerm ? 'Try adjusting your search criteria' : 'Add your first client to get started'}
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => setAddModalOpen(true)}
            >
              Add Client
            </Button>
          </CardContent>
        </Card>
      )}

      <AddClientModal
        open={addModalOpen}
        onClose={() => setAddModalOpen(false)}
        onAdd={onAddClient}
      />

      <BulkUploadModal
        open={bulkUploadOpen}
        onClose={() => setBulkUploadOpen(false)}
        onUpload={onBulkUpload}
      />
    </Box>
  );
};

export default ClientList;