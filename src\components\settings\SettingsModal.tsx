import React, { useState } from 'react';
import {
  <PERSON>alog,
  <PERSON>alogTitle,
  <PERSON>alogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Tabs,
  Tab,
  TextField,
  Stack,
  Switch,
  FormControlLabel,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Alert,
  Divider,
  Avatar,
  IconButton,
  InputAdornment,
  Grid,
  Paper
} from '@mui/material';
import {
  Close as CloseIcon,
  Person as PersonIcon,
  Api as ApiIcon,
  Security as SecurityIcon,
  Palette as PaletteIcon,
  Schedule as ScheduleIcon,
  Visibility,
  VisibilityOff,
  PhotoCamera as PhotoCameraIcon,
  Save as SaveIcon,
  Brightness4 as DarkModeIcon,
  Brightness7 as LightModeIcon,
  Logout as LogoutIcon,
  ExitToApp as ExitToAppIcon
} from '@mui/icons-material';
import { useTheme } from '../../contexts/ThemeContext';
import { User } from '../../types/schema';
import ApiConfiguration from './ApiConfiguration';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => {
  return (
    <div hidden={value !== index}>
      {value === index && <Box sx={{ py: 2 }}>{children}</Box>}
    </div>
  );
};

interface SettingsModalProps {
  user: User | null;
  open: boolean;
  onClose: () => void;
  onLogout?: () => void;
}

const SettingsModal: React.FC<SettingsModalProps> = ({ 
  user, 
  open, 
  onClose, 
  onLogout 
}) => {
  const { isDarkMode, toggleTheme } = useTheme();
  const [tabValue, setTabValue] = useState(0);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [saveMessage, setSaveMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [logoutConfirmOpen, setLogoutConfirmOpen] = useState(false);

  // User Settings State
  const [userSettings, setUserSettings] = useState({
    name: user?.name || 'Rajesh Kumar',
    username: 'rajeshkumar123',
    email: user?.email || '<EMAIL>',
    firmName: 'Kumar & Associates',
    mobile: '+919876543210',
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  // Scheduler Settings State
  const [schedulerSettings, setSchedulerSettings] = useState({
    frequency: 'once', // 'once' or 'twice'
    timeOnce: '09:00',
    timeFirst: '09:00',
    timeSecond: '18:00',
    enabled: true
  });

  if (!user) return null;

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleUserSettingsChange = (field: string) => (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setUserSettings(prev => ({
      ...prev,
      [field]: e.target.value
    }));
  };

  const handleSchedulerChange = (field: string) => (
    e: React.ChangeEvent<HTMLInputElement> | any
  ) => {
    const value = e.target.type === 'checkbox' ? e.target.checked : e.target.value;
    setSchedulerSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSaveUserSettings = () => {
    // Validate passwords if changing
    if (userSettings.newPassword) {
      if (userSettings.newPassword !== userSettings.confirmPassword) {
        setSaveMessage({ type: 'error', text: 'New passwords do not match' });
        return;
      }
      if (userSettings.newPassword.length < 6) {
        setSaveMessage({ type: 'error', text: 'Password must be at least 6 characters' });
        return;
      }
    }

    // TODO: Implement actual save logic
    console.log('Saving user settings:', userSettings);
    setSaveMessage({ type: 'success', text: 'Settings saved successfully!' });
    
    // Clear password fields
    setUserSettings(prev => ({
      ...prev,
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    }));

    setTimeout(() => setSaveMessage(null), 3000);
  };

  const handleSaveSchedulerSettings = () => {
    // TODO: Implement actual save logic
    console.log('Saving scheduler settings:', schedulerSettings);
    setSaveMessage({ type: 'success', text: 'Scheduler settings saved successfully!' });
    setTimeout(() => setSaveMessage(null), 3000);
  };

  const handleToggleDarkMode = () => {
    toggleTheme();
    console.log('Theme toggled to:', !isDarkMode ? 'dark' : 'light');
  };

  const handleLogoutClick = () => {
    setLogoutConfirmOpen(true);
  };

  const confirmLogout = () => {
    setLogoutConfirmOpen(false);
    onClose(); // Close settings modal first
    if (onLogout) {
      onLogout();
    }
  };

  return (
    <>
      <Dialog
        open={open}
        onClose={onClose}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: '16px',
            boxShadow: '0 8px 32px rgba(0,0,0,0.12)',
            maxHeight: '90vh'
          }
        }}
      >
        <DialogTitle sx={{ 
          p: 3,
          pb: 1,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <Box>
            <Typography 
              variant="h5" 
              fontWeight="700"
              sx={{ 
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                fontFamily: '"Inter", "Roboto", sans-serif'
              }}
            >
              Settings
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Manage your account settings and preferences
            </Typography>
          </Box>
          <IconButton 
            onClick={onClose}
            sx={{ 
              color: 'text.secondary',
              '&:hover': { 
                backgroundColor: 'action.hover'
              }
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>

        <DialogContent sx={{ p: 0 }}>
          {saveMessage && (
            <Alert 
              severity={saveMessage.type} 
              onClose={() => setSaveMessage(null)}
              sx={{ m: 3, mb: 0 }}
            >
              {saveMessage.text}
            </Alert>
          )}

          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            sx={{ borderBottom: 1, borderColor: 'divider', px: 3, mt: saveMessage ? 2 : 0 }}
          >
            <Tab
              label="User Settings"
              icon={<PersonIcon />}
              iconPosition="start"
            />
            <Tab
              label="Security"
              icon={<SecurityIcon />}
              iconPosition="start"
            />
            <Tab
              label="Appearance"
              icon={<PaletteIcon />}
              iconPosition="start"
            />
            <Tab
              label="Scheduler"
              icon={<ScheduleIcon />}
              iconPosition="start"
            />
            <Tab
              label="Account"
              icon={<ExitToAppIcon />}
              iconPosition="start"
            />
            <Tab
              label="API Config"
              icon={<ApiIcon />}
              iconPosition="start"
            />
          </Tabs>

          {/* User Settings Tab */}
          <TabPanel value={tabValue} index={0}>
            <Box sx={{ px: 3 }}>
              <Stack spacing={3}>
                {/* Profile Picture */}
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>
                  <Avatar
                    sx={{ width: 60, height: 60, fontSize: '1.5rem' }}
                  >
                    {userSettings.name.charAt(0)}
                  </Avatar>
                  <Box>
                    <Typography variant="h6">{userSettings.name}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      {userSettings.firmName}
                    </Typography>
                    <Button
                      startIcon={<PhotoCameraIcon />}
                      size="small"
                      sx={{ mt: 1 }}
                    >
                      Change Photo
                    </Button>
                  </Box>
                </Box>

                <Divider />

                {/* User Information */}
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Full Name"
                      value={userSettings.name}
                      onChange={handleUserSettingsChange('name')}
                      size="small"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Username"
                      value={userSettings.username}
                      onChange={handleUserSettingsChange('username')}
                      size="small"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Email Address"
                      type="email"
                      value={userSettings.email}
                      onChange={handleUserSettingsChange('email')}
                      size="small"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Mobile Number"
                      value={userSettings.mobile}
                      onChange={handleUserSettingsChange('mobile')}
                      size="small"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Firm Name"
                      value={userSettings.firmName}
                      onChange={handleUserSettingsChange('firmName')}
                      size="small"
                    />
                  </Grid>
                </Grid>

                <Button
                  variant="contained"
                  startIcon={<SaveIcon />}
                  onClick={handleSaveUserSettings}
                  sx={{ alignSelf: 'flex-start' }}
                >
                  Save Changes
                </Button>
              </Stack>
            </Box>
          </TabPanel>

          {/* Security Tab */}
          <TabPanel value={tabValue} index={1}>
            <Box sx={{ px: 3 }}>
              <Stack spacing={3}>
                <Typography variant="h6">Change Password</Typography>
                
                <TextField
                  fullWidth
                  label="Current Password"
                  type={showCurrentPassword ? 'text' : 'password'}
                  value={userSettings.currentPassword}
                  onChange={handleUserSettingsChange('currentPassword')}
                  size="small"
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                          edge="end"
                        >
                          {showCurrentPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />

                <TextField
                  fullWidth
                  label="New Password"
                  type={showNewPassword ? 'text' : 'password'}
                  value={userSettings.newPassword}
                  onChange={handleUserSettingsChange('newPassword')}
                  size="small"
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={() => setShowNewPassword(!showNewPassword)}
                          edge="end"
                        >
                          {showNewPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />

                <TextField
                  fullWidth
                  label="Confirm New Password"
                  type={showConfirmPassword ? 'text' : 'password'}
                  value={userSettings.confirmPassword}
                  onChange={handleUserSettingsChange('confirmPassword')}
                  size="small"
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                          edge="end"
                        >
                          {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />

                <Button
                  variant="contained"
                  startIcon={<SaveIcon />}
                  onClick={handleSaveUserSettings}
                  sx={{ alignSelf: 'flex-start' }}
                >
                  Update Password
                </Button>
              </Stack>
            </Box>
          </TabPanel>

          {/* Appearance Tab */}
          <TabPanel value={tabValue} index={2}>
            <Box sx={{ px: 3 }}>
              <Stack spacing={3}>
                <Typography variant="h6">Theme Settings</Typography>
                
                <Paper sx={{ p: 3, border: '1px solid', borderColor: 'divider' }}>
                  <Stack direction="row" justifyContent="space-between" alignItems="center">
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      {isDarkMode ? <DarkModeIcon /> : <LightModeIcon />}
                      <Box>
                        <Typography variant="body1" fontWeight="500">
                          Dark Mode
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Switch between light and dark themes
                        </Typography>
                      </Box>
                    </Box>
                    <Switch
                      checked={isDarkMode}
                      onChange={handleToggleDarkMode}
                    />
                  </Stack>
                </Paper>

                <Alert severity="info">
                  Theme changes will be applied immediately across the application.
                </Alert>
              </Stack>
            </Box>
          </TabPanel>

          {/* Scheduler Tab */}
          <TabPanel value={tabValue} index={3}>
            <Box sx={{ px: 3 }}>
              <Stack spacing={3}>
                <Typography variant="h6">Scheduler Configuration</Typography>
                <Typography variant="body2" color="text.secondary">
                  Configure automatic notice downloading schedule
                </Typography>

                <FormControlLabel
                  control={
                    <Switch
                      checked={schedulerSettings.enabled}
                      onChange={handleSchedulerChange('enabled')}
                    />
                  }
                  label="Enable Automatic Scheduling"
                />

                {schedulerSettings.enabled && (
                  <>
                    <FormControl fullWidth size="small">
                      <InputLabel>Frequency</InputLabel>
                      <Select
                        value={schedulerSettings.frequency}
                        label="Frequency"
                        onChange={handleSchedulerChange('frequency')}
                      >
                        <MenuItem value="once">Once a day</MenuItem>
                        <MenuItem value="twice">Twice a day</MenuItem>
                      </Select>
                    </FormControl>

                    {schedulerSettings.frequency === 'once' ? (
                      <TextField
                        fullWidth
                        label="Time"
                        type="time"
                        value={schedulerSettings.timeOnce}
                        onChange={handleSchedulerChange('timeOnce')}
                        InputLabelProps={{ shrink: true }}
                        size="small"
                      />
                    ) : (
                      <Grid container spacing={2}>
                        <Grid item xs={6}>
                          <TextField
                            fullWidth
                            label="First Time"
                            type="time"
                            value={schedulerSettings.timeFirst}
                            onChange={handleSchedulerChange('timeFirst')}
                            InputLabelProps={{ shrink: true }}
                            size="small"
                          />
                        </Grid>
                        <Grid item xs={6}>
                          <TextField
                            fullWidth
                            label="Second Time"
                            type="time"
                            value={schedulerSettings.timeSecond}
                            onChange={handleSchedulerChange('timeSecond')}
                            InputLabelProps={{ shrink: true }}
                            size="small"
                          />
                        </Grid>
                      </Grid>
                    )}

                    <Alert severity="info">
                      The system will automatically download new notices at the scheduled time(s).
                    </Alert>
                  </>
                )}

                <Button
                  variant="contained"
                  startIcon={<SaveIcon />}
                  onClick={handleSaveSchedulerSettings}
                  sx={{ alignSelf: 'flex-start' }}
                >
                  Save Scheduler Settings
                </Button>
              </Stack>
            </Box>
          </TabPanel>

          {/* Account Tab */}
          <TabPanel value={tabValue} index={4}>
            <Box sx={{ px: 3 }}>
              <Stack spacing={3}>
                <Typography variant="h6">Account Management</Typography>
                <Typography variant="body2" color="text.secondary">
                  Manage your account and session settings
                </Typography>

                {/* Account Info */}
                <Paper sx={{ p: 3, border: '1px solid', borderColor: 'divider' }}>
                  <Stack spacing={2}>
                    <Typography variant="subtitle1" fontWeight="600">
                      Account Information
                    </Typography>
                    <Box>
                      <Typography variant="body2">
                        <strong>Name:</strong> {userSettings.name}
                      </Typography>
                      <Typography variant="body2">
                        <strong>Firm:</strong> {userSettings.firmName}
                      </Typography>
                      <Typography variant="body2">
                        <strong>Email:</strong> {userSettings.email}
                      </Typography>
                      <Typography variant="body2">
                        <strong>Username:</strong> {userSettings.username}
                      </Typography>
                    </Box>
                  </Stack>
                </Paper>

                {/* Logout Section */}
                <Paper sx={{ 
                  p: 3, 
                  border: '1px solid', 
                  borderColor: 'error.light',
                  backgroundColor: 'error.light',
                  backgroundImage: 'linear-gradient(rgba(255,255,255,0.9), rgba(255,255,255,0.9))'
                }}>
                  <Stack spacing={2}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <LogoutIcon sx={{ color: 'error.main' }} />
                      <Box>
                        <Typography variant="subtitle1" fontWeight="600" color="error.main">
                          Sign Out
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          End your current session and return to login
                        </Typography>
                      </Box>
                    </Box>
                    
                    <Button
                      variant="outlined"
                      color="error"
                      startIcon={<LogoutIcon />}
                      onClick={handleLogoutClick}
                      sx={{ 
                        alignSelf: 'flex-start',
                        '&:hover': {
                          backgroundColor: 'error.main',
                          color: 'white'
                        }
                      }}
                    >
                      Logout
                    </Button>
                  </Stack>
                </Paper>

                <Alert severity="info">
                  You can also logout from the profile dropdown in the top navigation bar.
                </Alert>
              </Stack>
            </Box>
          </TabPanel>

          {/* API Configuration Tab */}
          <TabPanel value={tabValue} index={5}>
            <Box sx={{ px: 3 }}>
              <ApiConfiguration
                onApiConfigured={(config) => {
                  console.log('API configured:', config);
                  // You can handle the API configuration here
                }}
              />
            </Box>
          </TabPanel>
        </DialogContent>

        <DialogActions sx={{ 
          p: 3, 
          pt: 2,
          justifyContent: 'center'
        }}>
          <Button 
            onClick={onClose}
            variant="contained"
            sx={{
              px: 4,
              py: 1,
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              '&:hover': {
                background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
              },
              fontFamily: '"Inter", "Roboto", sans-serif',
              textTransform: 'none',
              borderRadius: '8px'
            }}
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>

      {/* Logout Confirmation Dialog */}
      <Dialog
        open={logoutConfirmOpen}
        onClose={() => setLogoutConfirmOpen(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: { borderRadius: '12px' }
        }}
      >
        <DialogTitle sx={{ textAlign: 'center' }}>
          <LogoutIcon sx={{ fontSize: 48, color: 'warning.main', mb: 1 }} />
          <Typography variant="h5" fontWeight="600">
            Confirm Logout
          </Typography>
        </DialogTitle>
        
        <DialogContent sx={{ textAlign: 'center' }}>
          <Typography variant="body1" color="text.secondary">
            Are you sure you want to logout?
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            You will need to login again to access the ERPCA Tax Engine dashboard.
          </Typography>
        </DialogContent>

        <DialogActions sx={{ p: 3, gap: 2, justifyContent: 'center' }}>
          <Button 
            onClick={() => setLogoutConfirmOpen(false)}
            variant="outlined"
            sx={{ minWidth: 100 }}
          >
            Cancel
          </Button>
          <Button 
            onClick={confirmLogout}
            variant="contained"
            color="error"
            startIcon={<LogoutIcon />}
            sx={{ minWidth: 100 }}
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default SettingsModal;
