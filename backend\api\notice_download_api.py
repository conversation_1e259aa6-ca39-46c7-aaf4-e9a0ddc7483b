"""
Notice Download API Endpoints
FastAPI endpoints for notice download functionality
"""
from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel
from typing import List, Dict, Optional
from datetime import datetime, timedelta
import logging

from ..services.notice_downloader import NoticeDownloader, GSTCredentials
from ..services.notice_scheduler import notice_scheduler

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/v1/notices", tags=["Notice Download"])

# Initialize downloader
downloader = NoticeDownloader()

# Pydantic models
class CredentialsRequest(BaseModel):
    client_gstin: str
    client_name: str
    username: str
    password: str

class FirstTimeDownloadRequest(BaseModel):
    client_gstin: str
    start_date: str  # YYYY-MM-DD format
    end_date: str    # YYYY-MM-DD format

class ScheduleRequest(BaseModel):
    client_gstin: str
    schedule_type: str = "daily"  # daily or interval
    hour: int = 9
    minute: int = 0
    interval_hours: int = 12

class DownloadResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Dict] = None

# API Endpoints

@router.post("/credentials", response_model=DownloadResponse)
async def store_gst_credentials(request: CredentialsRequest):
    """Store GST portal credentials for a client"""
    try:
        credentials = GSTCredentials(
            username=request.username,
            password=request.password,
            client_id=request.client_gstin,
            client_name=request.client_name
        )
        
        success = downloader.store_credentials(credentials)
        
        if success:
            return DownloadResponse(
                success=True,
                message="Credentials stored successfully",
                data={"client_gstin": request.client_gstin}
            )
        else:
            raise HTTPException(status_code=500, detail="Failed to store credentials")
            
    except Exception as e:
        logger.error(f"Error storing credentials: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/credentials/{client_gstin}")
async def get_stored_credentials(client_gstin: str):
    """Get stored credentials info (without password) for a client"""
    try:
        creds = downloader.get_stored_credentials(client_gstin)
        
        if creds:
            # Remove sensitive data
            safe_creds = {
                'client_name': creds['client_name'],
                'username': creds['username'],
                'last_used': creds['last_used'],
                'has_credentials': True
            }
            return DownloadResponse(
                success=True,
                message="Credentials found",
                data=safe_creds
            )
        else:
            return DownloadResponse(
                success=False,
                message="No credentials found for this client",
                data={"has_credentials": False}
            )
            
    except Exception as e:
        logger.error(f"Error getting credentials: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/download/first-time", response_model=DownloadResponse)
async def first_time_download(request: FirstTimeDownloadRequest, background_tasks: BackgroundTasks):
    """Perform first-time bulk download of notices"""
    try:
        # Validate date format
        try:
            datetime.strptime(request.start_date, "%Y-%m-%d")
            datetime.strptime(request.end_date, "%Y-%m-%d")
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid date format. Use YYYY-MM-DD")
        
        # Check if credentials exist
        creds = downloader.get_stored_credentials(request.client_gstin)
        if not creds:
            raise HTTPException(status_code=400, detail="No stored credentials found for this client")
        
        # Run download in background
        background_tasks.add_task(
            downloader.first_time_download,
            request.client_gstin,
            request.start_date,
            request.end_date
        )
        
        return DownloadResponse(
            success=True,
            message="First-time download started in background",
            data={
                "client_gstin": request.client_gstin,
                "date_range": f"{request.start_date} to {request.end_date}",
                "status": "started"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting first-time download: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/download/auto-fetch/{client_gstin}", response_model=DownloadResponse)
async def manual_auto_fetch(client_gstin: str, background_tasks: BackgroundTasks):
    """Manually trigger auto-fetch for a client"""
    try:
        # Check if credentials exist
        creds = downloader.get_stored_credentials(client_gstin)
        if not creds:
            raise HTTPException(status_code=400, detail="No stored credentials found for this client")
        
        # Run auto-fetch in background
        background_tasks.add_task(downloader.auto_fetch_daily, client_gstin)
        
        return DownloadResponse(
            success=True,
            message="Auto-fetch started in background",
            data={
                "client_gstin": client_gstin,
                "status": "started"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting auto-fetch: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/schedule", response_model=DownloadResponse)
async def schedule_auto_fetch(request: ScheduleRequest):
    """Schedule automated notice downloads for a client"""
    try:
        # Check if credentials exist
        creds = downloader.get_stored_credentials(request.client_gstin)
        if not creds:
            raise HTTPException(status_code=400, detail="No stored credentials found for this client")
        
        success = False
        if request.schedule_type == "daily":
            success = notice_scheduler.schedule_daily_auto_fetch(
                request.client_gstin, 
                request.hour, 
                request.minute
            )
        elif request.schedule_type == "interval":
            success = notice_scheduler.schedule_interval_auto_fetch(
                request.client_gstin, 
                request.interval_hours
            )
        else:
            raise HTTPException(status_code=400, detail="Invalid schedule_type. Use 'daily' or 'interval'")
        
        if success:
            return DownloadResponse(
                success=True,
                message=f"Auto-fetch scheduled successfully ({request.schedule_type})",
                data={
                    "client_gstin": request.client_gstin,
                    "schedule_type": request.schedule_type,
                    "schedule_details": {
                        "hour": request.hour,
                        "minute": request.minute,
                        "interval_hours": request.interval_hours
                    }
                }
            )
        else:
            raise HTTPException(status_code=500, detail="Failed to schedule auto-fetch")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error scheduling auto-fetch: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/schedule/{client_gstin}", response_model=DownloadResponse)
async def unschedule_auto_fetch(client_gstin: str):
    """Remove scheduled auto-fetch for a client"""
    try:
        success = notice_scheduler.unschedule_client(client_gstin)
        
        if success:
            return DownloadResponse(
                success=True,
                message="Auto-fetch unscheduled successfully",
                data={"client_gstin": client_gstin}
            )
        else:
            raise HTTPException(status_code=500, detail="Failed to unschedule auto-fetch")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error unscheduling auto-fetch: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/schedule/jobs")
async def get_scheduled_jobs():
    """Get list of all scheduled jobs"""
    try:
        jobs = notice_scheduler.get_scheduled_jobs()
        return DownloadResponse(
            success=True,
            message="Scheduled jobs retrieved successfully",
            data={"jobs": jobs, "total": len(jobs)}
        )
        
    except Exception as e:
        logger.error(f"Error getting scheduled jobs: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/download/history")
async def get_download_history(client_gstin: Optional[str] = None):
    """Get download history"""
    try:
        history = downloader.get_download_history(client_gstin)
        return DownloadResponse(
            success=True,
            message="Download history retrieved successfully",
            data={"history": history, "total": len(history)}
        )
        
    except Exception as e:
        logger.error(f"Error getting download history: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/client/{client_gstin}")
async def get_client_notices(client_gstin: str, limit: int = 50):
    """Get notices for a specific client"""
    try:
        notices = downloader.get_client_notices(client_gstin, limit)
        return DownloadResponse(
            success=True,
            message="Client notices retrieved successfully",
            data={"notices": notices, "total": len(notices), "client_gstin": client_gstin}
        )
        
    except Exception as e:
        logger.error(f"Error getting client notices: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/scheduler/logs")
async def get_scheduler_logs(client_gstin: Optional[str] = None, limit: int = 50):
    """Get scheduler run logs"""
    try:
        logs = notice_scheduler.get_scheduler_logs(client_gstin, limit)
        return DownloadResponse(
            success=True,
            message="Scheduler logs retrieved successfully",
            data={"logs": logs, "total": len(logs)}
        )
        
    except Exception as e:
        logger.error(f"Error getting scheduler logs: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/download/manual-fetch-all", response_model=DownloadResponse)
async def manual_fetch_all(background_tasks: BackgroundTasks):
    """Manually trigger auto-fetch for all active clients"""
    try:
        background_tasks.add_task(notice_scheduler.run_manual_fetch_all)
        
        return DownloadResponse(
            success=True,
            message="Manual fetch started for all active clients",
            data={"status": "started"}
        )
        
    except Exception as e:
        logger.error(f"Error starting manual fetch all: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
