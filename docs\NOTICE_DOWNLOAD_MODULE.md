# Notice Download Module

## Overview

The Notice Download Module is a comprehensive system for downloading and managing GST notices from the GST portal. It supports both first-time bulk downloads and automated daily fetching with duplicate detection.

## Features

### 🔄 **First-Time Download**
- **Date Range Selection**: Download notices for any specified period (e.g., past 1-2 years)
- **Bulk Processing**: Efficiently handles large volumes of historical notices
- **Client-wise Storage**: Organizes notices by client GSTIN
- **Progress Tracking**: Real-time status updates and completion reports

### 🤖 **Auto Fetch**
- **Scheduled Downloads**: Daily or interval-based automatic fetching
- **Duplicate Detection**: Skips already downloaded notices using notice ID matching
- **Incremental Updates**: Only downloads new notices since last sync
- **Background Processing**: Non-blocking operations with status monitoring

### 🔐 **Security & Credentials**
- **Encrypted Storage**: GST portal credentials stored securely
- **Per-Client Management**: Individual credential management for each client
- **Secure Authentication**: Encrypted password storage with hashing

### 📊 **Monitoring & Logging**
- **Download History**: Complete audit trail of all download operations
- **Scheduler Logs**: Detailed logs of automated runs
- **Status Tracking**: Real-time status updates and error reporting
- **Performance Metrics**: Download statistics and timing information

## Architecture

### Backend Components

#### 1. **NoticeDownloader Service** (`backend/services/notice_downloader.py`)
- Core download logic and GST portal integration
- Database operations for notices and credentials
- File management and storage organization
- Error handling and retry mechanisms

#### 2. **NoticeScheduler Service** (`backend/services/notice_scheduler.py`)
- APScheduler integration for automated downloads
- Job management (create, update, delete schedules)
- Background task execution
- Scheduler monitoring and logging

#### 3. **API Endpoints** (`backend/api/notice_download_api.py`)
- RESTful API for frontend integration
- Credential management endpoints
- Download trigger endpoints
- Status and history retrieval

### Frontend Components

#### 1. **NoticeDownloadManager** (`src/components/notices/NoticeDownloadManager.tsx`)
- Complete UI for notice download management
- Multi-tab interface (First-time, Auto-fetch, Schedule, History)
- Credential management dialog
- Real-time status updates

### Database Schema

#### Tables Created:
1. **notices** - Stores downloaded notice information
2. **download_logs** - Tracks download operations
3. **gst_credentials** - Encrypted credential storage
4. **scheduler_logs** - Automated run history

## API Endpoints

### Credential Management
- `POST /api/v1/notices/credentials` - Store GST credentials
- `GET /api/v1/notices/credentials/{client_gstin}` - Get credential info

### Download Operations
- `POST /api/v1/notices/download/first-time` - Start bulk download
- `POST /api/v1/notices/download/auto-fetch/{client_gstin}` - Manual auto-fetch
- `POST /api/v1/notices/download/manual-fetch-all` - Fetch all clients

### Scheduling
- `POST /api/v1/notices/schedule` - Schedule automated downloads
- `DELETE /api/v1/notices/schedule/{client_gstin}` - Remove schedule
- `GET /api/v1/notices/schedule/jobs` - List scheduled jobs

### Monitoring
- `GET /api/v1/notices/download/history` - Download history
- `GET /api/v1/notices/client/{client_gstin}` - Client notices
- `GET /api/v1/notices/scheduler/logs` - Scheduler logs

## Usage Guide

### 1. **Setup Credentials**
```typescript
// Store GST portal credentials for a client
const response = await fetch('/api/v1/notices/credentials', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    client_gstin: '27AAPFU0939F1ZV',
    client_name: 'ABC Enterprises Pvt Ltd',
    username: 'gst_username',
    password: 'gst_password'
  })
});
```

### 2. **First-Time Download**
```typescript
// Download historical notices
const response = await fetch('/api/v1/notices/download/first-time', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    client_gstin: '27AAPFU0939F1ZV',
    start_date: '2023-01-01',
    end_date: '2024-01-01'
  })
});
```

### 3. **Schedule Auto-Fetch**
```typescript
// Schedule daily auto-fetch at 9:00 AM
const response = await fetch('/api/v1/notices/schedule', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    client_gstin: '27AAPFU0939F1ZV',
    schedule_type: 'daily',
    hour: 9,
    minute: 0
  })
});
```

## File Organization

### Storage Structure
```
downloads/
├── 27AAPFU0939F1ZV/          # Client GSTIN folder
│   ├── NOT001_GSTR-1.pdf     # Notice files
│   ├── NOT002_GSTR-3B.pdf
│   └── ...
├── 29AABCU9603R1ZX/          # Another client
│   └── ...
└── ...
```

### Database Files
- `notices.db` - SQLite database with all tables
- `downloads/` - Directory for notice files

## Configuration

### Environment Variables
```bash
# Database
DATABASE_URL=sqlite:///notices.db

# Storage
NOTICE_STORAGE_PATH=downloads

# Scheduler
SCHEDULER_TIMEZONE=Asia/Kolkata
DEFAULT_FETCH_HOUR=9
DEFAULT_FETCH_MINUTE=0

# GST Portal (for future integration)
GST_PORTAL_BASE_URL=https://services.gst.gov.in
GST_PORTAL_TIMEOUT=30
```

### Scheduler Settings
- **Default Schedule**: Daily at 9:00 AM
- **Retry Policy**: 3 attempts with exponential backoff
- **Timeout**: 30 seconds per API call
- **Batch Size**: Process 10 notices at a time

## Security Considerations

### 1. **Credential Protection**
- Passwords are hashed using SHA-256
- Consider implementing proper encryption for production
- Credentials are never logged or exposed in API responses

### 2. **API Security**
- Add authentication middleware for production
- Implement rate limiting for download endpoints
- Validate all input parameters

### 3. **File Security**
- Store files outside web root
- Implement access controls for notice files
- Consider encryption for sensitive documents

## Monitoring & Maintenance

### 1. **Health Checks**
- Monitor scheduler status
- Check database connectivity
- Verify storage space availability

### 2. **Log Management**
- Rotate log files regularly
- Monitor error rates
- Set up alerts for failed downloads

### 3. **Performance Optimization**
- Index database tables for better query performance
- Implement connection pooling
- Consider caching for frequently accessed data

## Future Enhancements

### 1. **Advanced Features**
- Multi-threaded downloads for better performance
- Resume interrupted downloads
- Notification system for download completion
- Integration with email alerts

### 2. **GST Portal Integration**
- Real GST portal API integration (replace simulation)
- Handle different notice types and formats
- Support for multiple GST portals/states

### 3. **Analytics & Reporting**
- Download statistics dashboard
- Notice trend analysis
- Client activity reports
- Performance metrics

## Troubleshooting

### Common Issues

#### 1. **Download Failures**
- Check GST portal credentials
- Verify network connectivity
- Review error logs for specific issues

#### 2. **Scheduler Not Running**
- Check APScheduler status
- Verify cron expressions
- Review scheduler logs

#### 3. **Storage Issues**
- Check disk space availability
- Verify directory permissions
- Review file path configurations

### Debug Mode
Enable debug logging by setting:
```python
logging.basicConfig(level=logging.DEBUG)
```

## Support

For issues or questions:
1. Check the logs in `scheduler_logs` and `download_logs` tables
2. Review the API response messages
3. Verify configuration settings
4. Test with a single client first before bulk operations
