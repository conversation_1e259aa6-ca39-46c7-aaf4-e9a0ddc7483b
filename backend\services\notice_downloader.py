"""
GST Notice Download Service
Handles both first-time bulk downloads and automated daily fetching
"""
import os
import json
import sqlite3
import requests
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
import logging
from dataclasses import dataclass
import hashlib

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class GSTCredentials:
    """GST Portal credentials"""
    username: str
    password: str
    client_id: str
    client_name: str

@dataclass
class NoticeInfo:
    """Notice information structure"""
    notice_id: str
    client_gstin: str
    client_name: str
    notice_type: str
    subject: str
    notice_date: str
    due_date: str
    status: str
    description: str
    file_url: Optional[str] = None
    file_path: Optional[str] = None
    downloaded_at: Optional[str] = None

class NoticeDownloader:
    """Main notice download service"""
    
    def __init__(self, db_path: str = "notices.db", storage_path: str = "downloads"):
        self.db_path = db_path
        self.storage_path = storage_path
        self.session = requests.Session()
        self._init_database()
        self._ensure_storage_directory()
    
    def _init_database(self):
        """Initialize SQLite database for notices"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create notices table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS notices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                notice_id TEXT UNIQUE NOT NULL,
                client_gstin TEXT NOT NULL,
                client_name TEXT NOT NULL,
                notice_type TEXT NOT NULL,
                subject TEXT NOT NULL,
                notice_date TEXT NOT NULL,
                due_date TEXT,
                status TEXT DEFAULT 'new',
                description TEXT,
                file_url TEXT,
                file_path TEXT,
                downloaded_at TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Create download_logs table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS download_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                client_gstin TEXT NOT NULL,
                download_type TEXT NOT NULL,
                start_date TEXT,
                end_date TEXT,
                notices_found INTEGER DEFAULT 0,
                notices_downloaded INTEGER DEFAULT 0,
                status TEXT DEFAULT 'running',
                error_message TEXT,
                started_at TEXT DEFAULT CURRENT_TIMESTAMP,
                completed_at TEXT
            )
        ''')
        
        # Create credentials table (encrypted)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS gst_credentials (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                client_gstin TEXT UNIQUE NOT NULL,
                client_name TEXT NOT NULL,
                username TEXT NOT NULL,
                password_hash TEXT NOT NULL,
                is_active BOOLEAN DEFAULT 1,
                last_used TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
        logger.info("Database initialized successfully")
    
    def _ensure_storage_directory(self):
        """Create storage directory structure"""
        if not os.path.exists(self.storage_path):
            os.makedirs(self.storage_path)
        logger.info(f"Storage directory ensured: {self.storage_path}")
    
    def store_credentials(self, credentials: GSTCredentials) -> bool:
        """Store encrypted GST credentials"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Hash password for security
            password_hash = hashlib.sha256(credentials.password.encode()).hexdigest()
            
            cursor.execute('''
                INSERT OR REPLACE INTO gst_credentials 
                (client_gstin, client_name, username, password_hash, last_used)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                credentials.client_id,
                credentials.client_name,
                credentials.username,
                password_hash,
                datetime.now().isoformat()
            ))
            
            conn.commit()
            conn.close()
            logger.info(f"Credentials stored for client: {credentials.client_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error storing credentials: {str(e)}")
            return False
    
    def get_stored_credentials(self, client_gstin: str) -> Optional[Dict]:
        """Retrieve stored credentials for a client"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT client_name, username, password_hash, last_used
                FROM gst_credentials 
                WHERE client_gstin = ? AND is_active = 1
            ''', (client_gstin,))
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                return {
                    'client_name': result[0],
                    'username': result[1],
                    'password_hash': result[2],
                    'last_used': result[3]
                }
            return None
            
        except Exception as e:
            logger.error(f"Error retrieving credentials: {str(e)}")
            return None
    
    def simulate_gst_portal_login(self, credentials: GSTCredentials) -> bool:
        """Simulate GST portal login (replace with actual implementation)"""
        try:
            # This is a simulation - replace with actual GST portal API calls
            logger.info(f"Attempting login for {credentials.username}")
            
            # Simulate login delay
            import time
            time.sleep(2)
            
            # For demo purposes, assume login is successful
            logger.info("GST portal login successful")
            return True
            
        except Exception as e:
            logger.error(f"GST portal login failed: {str(e)}")
            return False
    
    def simulate_fetch_notices(self, client_gstin: str, start_date: str, end_date: str) -> List[NoticeInfo]:
        """Simulate fetching notices from GST portal (replace with actual implementation)"""
        try:
            logger.info(f"Fetching notices for {client_gstin} from {start_date} to {end_date}")
            
            # Simulate API delay
            import time
            time.sleep(3)
            
            # Generate mock notices for demonstration
            mock_notices = []
            base_date = datetime.strptime(start_date, "%Y-%m-%d")
            
            for i in range(5):  # Generate 5 mock notices
                notice_date = base_date + timedelta(days=i*30)
                due_date = notice_date + timedelta(days=30)
                
                notice = NoticeInfo(
                    notice_id=f"NOT{client_gstin[-4:]}{notice_date.strftime('%Y%m%d')}{i+1:03d}",
                    client_gstin=client_gstin,
                    client_name="Sample Client Ltd",
                    notice_type=["GSTR-1", "GSTR-3B", "Annual Return", "Notice u/s 61"][i % 4],
                    subject=f"Notice for {['GSTR-1 Filing', 'GSTR-3B Return', 'Annual Return Filing', 'Tax Payment'][i % 4]}",
                    notice_date=notice_date.strftime("%Y-%m-%d"),
                    due_date=due_date.strftime("%Y-%m-%d"),
                    status="new",
                    description=f"This is a sample notice for demonstration purposes. Notice #{i+1}",
                    file_url=f"https://gst.gov.in/notices/{client_gstin}/notice_{i+1}.pdf"
                )
                mock_notices.append(notice)
            
            logger.info(f"Found {len(mock_notices)} notices")
            return mock_notices
            
        except Exception as e:
            logger.error(f"Error fetching notices: {str(e)}")
            return []
    
    def download_notice_file(self, notice: NoticeInfo) -> bool:
        """Download notice file from GST portal"""
        try:
            if not notice.file_url:
                return False
            
            # Create client-specific directory
            client_dir = os.path.join(self.storage_path, notice.client_gstin)
            if not os.path.exists(client_dir):
                os.makedirs(client_dir)
            
            # Generate filename
            filename = f"{notice.notice_id}_{notice.notice_type.replace(' ', '_')}.pdf"
            file_path = os.path.join(client_dir, filename)
            
            # Simulate file download (replace with actual download)
            logger.info(f"Downloading notice file: {filename}")
            
            # Create a dummy PDF file for demonstration
            with open(file_path, 'w') as f:
                f.write(f"Dummy PDF content for notice {notice.notice_id}")
            
            notice.file_path = file_path
            notice.downloaded_at = datetime.now().isoformat()
            
            logger.info(f"Notice file downloaded: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error downloading notice file: {str(e)}")
            return False
    
    def save_notice_to_db(self, notice: NoticeInfo) -> bool:
        """Save notice information to database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO notices 
                (notice_id, client_gstin, client_name, notice_type, subject, 
                 notice_date, due_date, status, description, file_url, file_path, downloaded_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                notice.notice_id, notice.client_gstin, notice.client_name,
                notice.notice_type, notice.subject, notice.notice_date,
                notice.due_date, notice.status, notice.description,
                notice.file_url, notice.file_path, notice.downloaded_at
            ))
            
            conn.commit()
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"Error saving notice to database: {str(e)}")
            return False
    
    def is_notice_already_downloaded(self, notice_id: str) -> bool:
        """Check if notice is already downloaded"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('SELECT id FROM notices WHERE notice_id = ?', (notice_id,))
            result = cursor.fetchone()
            conn.close()
            
            return result is not None
            
        except Exception as e:
            logger.error(f"Error checking notice existence: {str(e)}")
            return False

    def first_time_download(self, client_gstin: str, start_date: str, end_date: str) -> Dict:
        """Perform first-time bulk download of notices"""
        logger.info(f"Starting first-time download for {client_gstin}: {start_date} to {end_date}")

        # Log download attempt
        log_id = self._log_download_start(client_gstin, "first_time", start_date, end_date)

        try:
            # Get stored credentials
            creds_data = self.get_stored_credentials(client_gstin)
            if not creds_data:
                error_msg = "No stored credentials found for client"
                self._log_download_error(log_id, error_msg)
                return {"success": False, "error": error_msg}

            # Create credentials object (password would need to be decrypted in real implementation)
            credentials = GSTCredentials(
                username=creds_data['username'],
                password="decrypted_password",  # Implement proper decryption
                client_id=client_gstin,
                client_name=creds_data['client_name']
            )

            # Login to GST portal
            if not self.simulate_gst_portal_login(credentials):
                error_msg = "GST portal login failed"
                self._log_download_error(log_id, error_msg)
                return {"success": False, "error": error_msg}

            # Fetch notices
            notices = self.simulate_fetch_notices(client_gstin, start_date, end_date)

            downloaded_count = 0
            skipped_count = 0

            for notice in notices:
                # Check if already downloaded
                if self.is_notice_already_downloaded(notice.notice_id):
                    logger.info(f"Notice {notice.notice_id} already exists, skipping")
                    skipped_count += 1
                    continue

                # Download notice file
                if self.download_notice_file(notice):
                    # Save to database
                    if self.save_notice_to_db(notice):
                        downloaded_count += 1
                        logger.info(f"Successfully processed notice: {notice.notice_id}")
                    else:
                        logger.error(f"Failed to save notice to database: {notice.notice_id}")
                else:
                    logger.error(f"Failed to download notice file: {notice.notice_id}")

            # Log completion
            self._log_download_completion(log_id, len(notices), downloaded_count)

            result = {
                "success": True,
                "total_found": len(notices),
                "downloaded": downloaded_count,
                "skipped": skipped_count,
                "client_gstin": client_gstin,
                "date_range": f"{start_date} to {end_date}"
            }

            logger.info(f"First-time download completed: {result}")
            return result

        except Exception as e:
            error_msg = f"First-time download failed: {str(e)}"
            logger.error(error_msg)
            self._log_download_error(log_id, error_msg)
            return {"success": False, "error": error_msg}

    def auto_fetch_daily(self, client_gstin: str, days_back: int = 7) -> Dict:
        """Perform automated daily fetch of new notices"""
        end_date = datetime.now().strftime("%Y-%m-%d")
        start_date = (datetime.now() - timedelta(days=days_back)).strftime("%Y-%m-%d")

        logger.info(f"Starting auto-fetch for {client_gstin}: {start_date} to {end_date}")

        # Log download attempt
        log_id = self._log_download_start(client_gstin, "auto_fetch", start_date, end_date)

        try:
            # Get stored credentials
            creds_data = self.get_stored_credentials(client_gstin)
            if not creds_data:
                error_msg = "No stored credentials found for client"
                self._log_download_error(log_id, error_msg)
                return {"success": False, "error": error_msg}

            # Create credentials object
            credentials = GSTCredentials(
                username=creds_data['username'],
                password="decrypted_password",  # Implement proper decryption
                client_id=client_gstin,
                client_name=creds_data['client_name']
            )

            # Login to GST portal
            if not self.simulate_gst_portal_login(credentials):
                error_msg = "GST portal login failed"
                self._log_download_error(log_id, error_msg)
                return {"success": False, "error": error_msg}

            # Fetch recent notices
            notices = self.simulate_fetch_notices(client_gstin, start_date, end_date)

            new_notices = []
            for notice in notices:
                if not self.is_notice_already_downloaded(notice.notice_id):
                    new_notices.append(notice)

            downloaded_count = 0
            for notice in new_notices:
                if self.download_notice_file(notice):
                    if self.save_notice_to_db(notice):
                        downloaded_count += 1
                        logger.info(f"New notice downloaded: {notice.notice_id}")

            # Log completion
            self._log_download_completion(log_id, len(notices), downloaded_count)

            result = {
                "success": True,
                "total_found": len(notices),
                "new_notices": len(new_notices),
                "downloaded": downloaded_count,
                "client_gstin": client_gstin,
                "date_range": f"{start_date} to {end_date}"
            }

            logger.info(f"Auto-fetch completed: {result}")
            return result

        except Exception as e:
            error_msg = f"Auto-fetch failed: {str(e)}"
            logger.error(error_msg)
            self._log_download_error(log_id, error_msg)
            return {"success": False, "error": error_msg}

    def _log_download_start(self, client_gstin: str, download_type: str, start_date: str, end_date: str) -> int:
        """Log download start"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO download_logs
                (client_gstin, download_type, start_date, end_date, status)
                VALUES (?, ?, ?, ?, 'running')
            ''', (client_gstin, download_type, start_date, end_date))

            log_id = cursor.lastrowid
            conn.commit()
            conn.close()
            return log_id

        except Exception as e:
            logger.error(f"Error logging download start: {str(e)}")
            return 0

    def _log_download_completion(self, log_id: int, notices_found: int, notices_downloaded: int):
        """Log download completion"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                UPDATE download_logs
                SET notices_found = ?, notices_downloaded = ?, status = 'completed',
                    completed_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (notices_found, notices_downloaded, log_id))

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"Error logging download completion: {str(e)}")

    def _log_download_error(self, log_id: int, error_message: str):
        """Log download error"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                UPDATE download_logs
                SET status = 'failed', error_message = ?, completed_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (error_message, log_id))

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"Error logging download error: {str(e)}")

    def get_download_history(self, client_gstin: str = None) -> List[Dict]:
        """Get download history"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            if client_gstin:
                cursor.execute('''
                    SELECT * FROM download_logs
                    WHERE client_gstin = ?
                    ORDER BY started_at DESC
                ''', (client_gstin,))
            else:
                cursor.execute('SELECT * FROM download_logs ORDER BY started_at DESC')

            columns = [desc[0] for desc in cursor.description]
            results = [dict(zip(columns, row)) for row in cursor.fetchall()]

            conn.close()
            return results

        except Exception as e:
            logger.error(f"Error getting download history: {str(e)}")
            return []

    def get_client_notices(self, client_gstin: str, limit: int = 50) -> List[Dict]:
        """Get notices for a specific client"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT * FROM notices
                WHERE client_gstin = ?
                ORDER BY notice_date DESC
                LIMIT ?
            ''', (client_gstin, limit))

            columns = [desc[0] for desc in cursor.description]
            results = [dict(zip(columns, row)) for row in cursor.fetchall()]

            conn.close()
            return results

        except Exception as e:
            logger.error(f"Error getting client notices: {str(e)}")
            return []
