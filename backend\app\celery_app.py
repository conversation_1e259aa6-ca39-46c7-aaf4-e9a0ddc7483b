"""
Celery application for background tasks
"""
from celery import Celery
from celery.schedules import crontab
from .core.config import settings

# Create Celery app
celery_app = Celery(
    "gst_notice_system",
    broker=settings.CELERY_BROKER_URL,
    backend=settings.CELERY_RESULT_BACKEND,
    include=[
        "app.tasks.notice_sync",
        "app.tasks.email_tasks",
        "app.tasks.maintenance"
    ]
)

# Celery configuration
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30 minutes
    task_soft_time_limit=25 * 60,  # 25 minutes
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
)

# Periodic tasks schedule
celery_app.conf.beat_schedule = {
    # Daily notice sync at 6 AM
    "daily-notice-sync": {
        "task": "app.tasks.notice_sync.daily_notice_sync",
        "schedule": crontab(hour=6, minute=0),
    },
    
    # Hourly check for urgent notices
    "hourly-urgent-check": {
        "task": "app.tasks.notice_sync.check_urgent_notices",
        "schedule": crontab(minute=0),
    },
    
    # Daily email digest at 9 AM
    "daily-email-digest": {
        "task": "app.tasks.email_tasks.send_daily_digest",
        "schedule": crontab(hour=9, minute=0),
    },
    
    # Weekly maintenance on Sunday at 2 AM
    "weekly-maintenance": {
        "task": "app.tasks.maintenance.weekly_cleanup",
        "schedule": crontab(hour=2, minute=0, day_of_week=0),
    },
    
    # Check due date reminders every 4 hours
    "due-date-reminders": {
        "task": "app.tasks.email_tasks.send_due_date_reminders",
        "schedule": crontab(minute=0, hour="*/4"),
    },
}

celery_app.conf.timezone = "UTC"


if __name__ == "__main__":
    celery_app.start()
