import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>nt,
  TextField,
  Button,
  Stack,
  <PERSON>po<PERSON>,
  Alert,
  CircularProgress,
  Box,
  Chip,
  Divider
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import BusinessIcon from '@mui/icons-material/Business';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import { simpleGstApiService as gstApiService, GSTClientDetails } from '../../services/gstApiServiceSimple';
import { erpcaApiService, ERPCAClientDetails } from '../../services/erpcaApiService';
import { whitebooksApiService, WhitebooksClientDetails } from '../../services/whitebooksApiService';

interface GSTINData {
  gstin: string;
  clientName: string;
  contactPerson: string;
  phone: string;
  email: string;
  address: string;
}

interface GSTINFetcherProps {
  onDataFetched: (data: GSTINData) => void;
  onSkip: () => void;
  userEmail?: string;
  userPhone?: string;
}

const GSTINFetcher: React.FC<GSTINFetcherProps> = ({
  onDataFetched,
  onSkip,
  userEmail = '<EMAIL>',
  userPhone = '9930711084'
}) => {
  const [gstin, setGstin] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [gstDetails, setGstDetails] = useState<GSTClientDetails | null>(null);
  const [erpcaDetails, setErpcaDetails] = useState<ERPCAClientDetails | null>(null);
  const [whitebooksDetails, setWhitebooksDetails] = useState<WhitebooksClientDetails | null>(null);
  const [fetchMethod, setFetchMethod] = useState<'gstin' | 'erpca' | 'whitebooks'>('gstin');

  const validateGSTIN = (gstinValue: string) => {
    const gstinRegex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;
    return gstinRegex.test(gstinValue);
  };

  const handleGSTINChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.toUpperCase();
    setGstin(value);
    setError('');
    setSuccess(false);
  };

  const fetchFromGSTPortal = async () => {
    if (!validateGSTIN(gstin)) {
      setError('Please enter a valid 15-character GSTIN');
      return;
    }

    setLoading(true);
    setError('');
    setGstDetails(null);
    setErpcaDetails(null);

    try {
      console.log('Fetching from GST Portal for GSTIN:', gstin);

      // Fetch client details from GST API
      const gstClientDetails = await gstApiService.fetchClientDetails(gstin);
      setGstDetails(gstClientDetails);

      // Transform GST API response to our format
      const clientData: GSTINData = {
        gstin: gstClientDetails.gstin,
        clientName: gstClientDetails.legalName || gstClientDetails.tradeName,
        contactPerson: gstClientDetails.authorizedSignatories[0]?.name || 'Not Available',
        phone: '+91 9876543210', // This would come from additional API or manual entry
        email: '<EMAIL>', // This would come from additional API or manual entry
        address: `${gstClientDetails.registrationAddress.buildingName}, ${gstClientDetails.registrationAddress.streetName}, ${gstClientDetails.registrationAddress.location}, ${gstClientDetails.registrationAddress.district}, ${gstClientDetails.registrationAddress.state} - ${gstClientDetails.registrationAddress.pincode}`
      };

      console.log('GST data fetched successfully:', clientData);
      setSuccess(true);
      setTimeout(() => {
        onDataFetched(clientData);
      }, 1000);

    } catch (err: any) {
      console.error('GST API Error:', err);
      setError(err.message || 'Failed to fetch data from GST portal. Please try again or enter details manually.');
    } finally {
      setLoading(false);
    }
  };

  const fetchFromERPCAPortal = async () => {
    setLoading(true);
    setError('');
    setErpcaDetails(null);
    setGstDetails(null);

    try {
      console.log('Fetching from ERPCA Portal for:', userEmail, userPhone, gstin);

      // Fetch client details from ERPCA API
      const erpcaClientDetails = await erpcaApiService.fetchClientDetails(userEmail, userPhone, gstin);
      setErpcaDetails(erpcaClientDetails);

      // Transform ERPCA API response to our format
      const clientData: GSTINData = {
        gstin: erpcaClientDetails.gstin,
        clientName: erpcaClientDetails.businessName,
        contactPerson: `${erpcaClientDetails.firstName} ${erpcaClientDetails.lastName}`,
        phone: erpcaClientDetails.phone,
        email: erpcaClientDetails.email,
        address: erpcaClientDetails.address
      };

      console.log('ERPCA data fetched successfully:', clientData);
      setSuccess(true);
      setTimeout(() => {
        onDataFetched(clientData);
      }, 1000);

    } catch (err: any) {
      console.error('ERPCA API Error:', err);
      setError(err.message || 'Failed to fetch data from ERPCA portal. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const fetchFromWhitebooksAPI = async () => {
    setLoading(true);
    setError('');
    setGstDetails(null);
    setErpcaDetails(null);
    setWhitebooksDetails(null);

    try {
      console.log('Fetching from Whitebooks API for:', userEmail, gstin);

      // Fetch client details from Whitebooks API
      const whitebooksClientDetails = await whitebooksApiService.searchClientDetails(userEmail, gstin);
      setWhitebooksDetails(whitebooksClientDetails);

      // Transform Whitebooks API response to our format
      const clientData: GSTINData = {
        gstin: whitebooksClientDetails.gstin,
        clientName: whitebooksClientDetails.businessName,
        contactPerson: whitebooksClientDetails.authorizedSignatories?.[0]?.name || 'Not Available',
        phone: '+91 9876543210', // This would come from API if available
        email: whitebooksClientDetails.email,
        address: `${whitebooksClientDetails.address.buildingName || ''} ${whitebooksClientDetails.address.streetName || ''}, ${whitebooksClientDetails.address.location || ''}, ${whitebooksClientDetails.address.district || ''}, ${whitebooksClientDetails.address.state || ''} - ${whitebooksClientDetails.address.pincode || ''}`.trim()
      };

      console.log('Whitebooks data fetched successfully:', clientData);
      setSuccess(true);
      setTimeout(() => {
        onDataFetched(clientData);
      }, 1000);

    } catch (err: any) {
      console.error('Whitebooks API Error:', err);
      setError(err.message || 'Failed to fetch data from Whitebooks API. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      fetchFromGSTPortal();
    }
  };

  return (
    <Card sx={{ mb: 3, bgcolor: 'primary.50', border: '1px solid', borderColor: 'primary.200' }}>
      <CardContent>
        <Stack spacing={3}>
          <Box>
            <Typography variant="h6" color="primary" gutterBottom>
              Fetch Client Details from API
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Enter GSTIN to fetch from GST portal, use ERPCA account details, or fetch from Whitebooks API
            </Typography>
          </Box>

          <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} alignItems="flex-start">
            <TextField
              label="GSTIN (Optional for ERPCA)"
              value={gstin}
              onChange={handleGSTINChange}
              onKeyPress={handleKeyPress}
              placeholder="27AAPFU0939F1ZV"
              error={!!error}
              helperText={error || 'Enter 15-character GSTIN or leave empty for ERPCA'}
              disabled={loading || success}
              sx={{ flex: 1 }}
              inputProps={{ maxLength: 15 }}
            />

            <Stack direction="row" spacing={2}>
              <Button
                variant="contained"
                startIcon={
                  loading && fetchMethod === 'gstin' ? (
                    <CircularProgress size={20} color="inherit" />
                  ) : success ? (
                    <CheckCircleIcon />
                  ) : (
                    <SearchIcon />
                  )
                }
                onClick={() => {
                  setFetchMethod('gstin');
                  fetchFromGSTPortal();
                }}
                disabled={!gstin || loading || success}
                sx={{ minWidth: 160 }}
              >
                {loading && fetchMethod === 'gstin' ? 'Fetching...' : success ? 'Fetched!' : 'Fetch from GST'}
              </Button>

              <Button
                variant="contained"
                color="secondary"
                startIcon={
                  loading && fetchMethod === 'erpca' ? (
                    <CircularProgress size={20} color="inherit" />
                  ) : success ? (
                    <CheckCircleIcon />
                  ) : (
                    <BusinessIcon />
                  )
                }
                onClick={() => {
                  setFetchMethod('erpca');
                  fetchFromERPCAPortal();
                }}
                disabled={loading || success}
                sx={{ minWidth: 160 }}
              >
                {loading && fetchMethod === 'erpca' ? 'Fetching...' : success ? 'Fetched!' : 'Fetch from ERPCA'}
              </Button>

              <Button
                variant="contained"
                color="success"
                startIcon={
                  loading && fetchMethod === 'whitebooks' ? (
                    <CircularProgress size={20} color="inherit" />
                  ) : success ? (
                    <CheckCircleIcon />
                  ) : (
                    <SearchIcon />
                  )
                }
                onClick={() => {
                  setFetchMethod('whitebooks');
                  fetchFromWhitebooksAPI();
                }}
                disabled={loading || success}
                sx={{ minWidth: 160 }}
              >
                {loading && fetchMethod === 'whitebooks' ? 'Fetching...' : success ? 'Fetched!' : 'Fetch from Whitebooks'}
              </Button>

              <Button
                variant="outlined"
                onClick={onSkip}
                disabled={loading}
              >
                Skip & Enter Manually
              </Button>
            </Stack>
          </Stack>

          {success && (
            <Alert severity="success" icon={<CheckCircleIcon />}>
              <Typography variant="body2">
                Client details fetched successfully! The form will be auto-populated with the retrieved information.
              </Typography>
            </Alert>
          )}

          {gstDetails && (
            <Card sx={{ bgcolor: 'background.paper', border: '1px solid', borderColor: 'divider' }}>
              <CardContent>
                <Stack spacing={2}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <BusinessIcon color="primary" />
                    <Typography variant="h6" color="primary">
                      GST Client Details
                    </Typography>
                  </Box>

                  <Divider />

                  <Stack spacing={2}>
                    <Box>
                      <Typography variant="subtitle2" color="text.secondary">
                        Legal Name
                      </Typography>
                      <Typography variant="body1" fontWeight="500">
                        {gstDetails.legalName}
                      </Typography>
                    </Box>

                    <Box>
                      <Typography variant="subtitle2" color="text.secondary">
                        Trade Name
                      </Typography>
                      <Typography variant="body1">
                        {gstDetails.tradeName || 'Not Available'}
                      </Typography>
                    </Box>

                    <Box>
                      <Typography variant="subtitle2" color="text.secondary">
                        GSTIN Status
                      </Typography>
                      <Chip
                        label={gstDetails.gstinStatus}
                        color={gstDetails.gstinStatus === 'Active' ? 'success' : 'warning'}
                        size="small"
                      />
                    </Box>

                    <Box>
                      <Typography variant="subtitle2" color="text.secondary">
                        Business Type
                      </Typography>
                      <Typography variant="body1">
                        {gstDetails.constitutionOfBusiness}
                      </Typography>
                    </Box>

                    <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                      <LocationOnIcon color="action" sx={{ mt: 0.5 }} />
                      <Box>
                        <Typography variant="subtitle2" color="text.secondary">
                          Registration Address
                        </Typography>
                        <Typography variant="body2">
                          {gstDetails.registrationAddress.buildingName}, {gstDetails.registrationAddress.streetName}
                          <br />
                          {gstDetails.registrationAddress.location}, {gstDetails.registrationAddress.district}
                          <br />
                          {gstDetails.registrationAddress.state} - {gstDetails.registrationAddress.pincode}
                        </Typography>
                      </Box>
                    </Box>

                    {gstDetails.authorizedSignatories.length > 0 && (
                      <Box>
                        <Typography variant="subtitle2" color="text.secondary">
                          Authorized Signatory
                        </Typography>
                        <Typography variant="body1">
                          {gstDetails.authorizedSignatories[0].name} ({gstDetails.authorizedSignatories[0].designation})
                        </Typography>
                      </Box>
                    )}

                    <Box>
                      <Typography variant="subtitle2" color="text.secondary">
                        Registration Date
                      </Typography>
                      <Typography variant="body1">
                        {new Date(gstDetails.registrationDate).toLocaleDateString()}
                      </Typography>
                    </Box>
                  </Stack>
                </Stack>
              </CardContent>
            </Card>
          )}

          {erpcaDetails && (
            <Card sx={{ bgcolor: 'background.paper', border: '1px solid', borderColor: 'secondary.main' }}>
              <CardContent>
                <Stack spacing={2}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <BusinessIcon color="secondary" />
                    <Typography variant="h6" color="secondary">
                      ERPCA Account Details
                    </Typography>
                  </Box>

                  <Divider />

                  <Stack spacing={2}>
                    <Box>
                      <Typography variant="subtitle2" color="text.secondary">
                        Business Name
                      </Typography>
                      <Typography variant="body1" fontWeight="500">
                        {erpcaDetails.businessName}
                      </Typography>
                    </Box>

                    <Box>
                      <Typography variant="subtitle2" color="text.secondary">
                        Contact Person
                      </Typography>
                      <Typography variant="body1">
                        {erpcaDetails.firstName} {erpcaDetails.lastName}
                      </Typography>
                    </Box>

                    <Box>
                      <Typography variant="subtitle2" color="text.secondary">
                        GSTIN
                      </Typography>
                      <Typography variant="body1" fontWeight="500">
                        {erpcaDetails.gstin || 'Not Available'}
                      </Typography>
                    </Box>

                    <Box>
                      <Typography variant="subtitle2" color="text.secondary">
                        Account Status
                      </Typography>
                      <Chip
                        label={erpcaDetails.gstinStatus}
                        color={erpcaDetails.gstinStatus === 'Active' ? 'success' : 'warning'}
                        size="small"
                      />
                    </Box>

                    <Box>
                      <Typography variant="subtitle2" color="text.secondary">
                        Email
                      </Typography>
                      <Typography variant="body1">
                        {erpcaDetails.email}
                      </Typography>
                    </Box>

                    <Box>
                      <Typography variant="subtitle2" color="text.secondary">
                        Phone
                      </Typography>
                      <Typography variant="body1">
                        {erpcaDetails.phone}
                      </Typography>
                    </Box>

                    <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                      <LocationOnIcon color="action" sx={{ mt: 0.5 }} />
                      <Box>
                        <Typography variant="subtitle2" color="text.secondary">
                          Billing Address
                        </Typography>
                        <Typography variant="body2">
                          {erpcaDetails.address}
                        </Typography>
                      </Box>
                    </Box>

                    {erpcaDetails.subscriptionDetails && (
                      <Box>
                        <Typography variant="subtitle2" color="text.secondary">
                          Subscription Plan
                        </Typography>
                        <Typography variant="body1">
                          {erpcaDetails.subscriptionDetails.plan_type.toUpperCase()} - Team Size: {erpcaDetails.subscriptionDetails.team_size}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Payment Status: {erpcaDetails.subscriptionDetails.payment_status.toUpperCase()}
                        </Typography>
                      </Box>
                    )}
                  </Stack>
                </Stack>
              </CardContent>
            </Card>
          )}

          {whitebooksDetails && (
            <Card sx={{ bgcolor: 'background.paper', border: '1px solid', borderColor: 'success.main' }}>
              <CardContent>
                <Stack spacing={2}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <SearchIcon color="success" />
                    <Typography variant="h6" color="success">
                      Whitebooks API Details
                    </Typography>
                  </Box>

                  <Divider />

                  <Stack spacing={2}>
                    <Box>
                      <Typography variant="subtitle2" color="text.secondary">
                        Business Name
                      </Typography>
                      <Typography variant="body1" fontWeight="500">
                        {whitebooksDetails.businessName}
                      </Typography>
                    </Box>

                    <Box>
                      <Typography variant="subtitle2" color="text.secondary">
                        Legal Name
                      </Typography>
                      <Typography variant="body1">
                        {whitebooksDetails.legalName}
                      </Typography>
                    </Box>

                    <Box>
                      <Typography variant="subtitle2" color="text.secondary">
                        GSTIN
                      </Typography>
                      <Typography variant="body1" fontWeight="500">
                        {whitebooksDetails.gstin}
                      </Typography>
                    </Box>

                    <Box>
                      <Typography variant="subtitle2" color="text.secondary">
                        Status
                      </Typography>
                      <Chip
                        label={whitebooksDetails.gstinStatus}
                        color={whitebooksDetails.gstinStatus === 'Active' ? 'success' : 'warning'}
                        size="small"
                      />
                    </Box>

                    <Box>
                      <Typography variant="subtitle2" color="text.secondary">
                        Email
                      </Typography>
                      <Typography variant="body1">
                        {whitebooksDetails.email}
                      </Typography>
                    </Box>

                    <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                      <LocationOnIcon color="action" sx={{ mt: 0.5 }} />
                      <Box>
                        <Typography variant="subtitle2" color="text.secondary">
                          Address
                        </Typography>
                        <Typography variant="body2">
                          {whitebooksDetails.address.buildingName} {whitebooksDetails.address.streetName}
                          <br />
                          {whitebooksDetails.address.location}, {whitebooksDetails.address.district}
                          <br />
                          {whitebooksDetails.address.state} - {whitebooksDetails.address.pincode}
                        </Typography>
                      </Box>
                    </Box>

                    {whitebooksDetails.authorizedSignatories && whitebooksDetails.authorizedSignatories.length > 0 && (
                      <Box>
                        <Typography variant="subtitle2" color="text.secondary">
                          Authorized Signatory
                        </Typography>
                        <Typography variant="body1">
                          {whitebooksDetails.authorizedSignatories[0].name} ({whitebooksDetails.authorizedSignatories[0].designation})
                        </Typography>
                      </Box>
                    )}

                    <Box>
                      <Typography variant="subtitle2" color="text.secondary">
                        Registration Date
                      </Typography>
                      <Typography variant="body1">
                        {new Date(whitebooksDetails.registrationDate).toLocaleDateString()}
                      </Typography>
                    </Box>
                  </Stack>
                </Stack>
              </CardContent>
            </Card>
          )}

          {error && !loading && (
            <Alert severity="error">
              <Typography variant="body2">{error}</Typography>
            </Alert>
          )}
        </Stack>
      </CardContent>
    </Card>
  );
};

export default GSTINFetcher;