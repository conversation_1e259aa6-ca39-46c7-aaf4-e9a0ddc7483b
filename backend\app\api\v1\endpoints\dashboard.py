"""
Dashboard endpoints
"""
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from sqlalchemy import and_, func, desc
from ....core.database import get_db
from ....models import Client, Notice, NoticeStatus, User
from ..endpoints.auth import get_current_active_user
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/stats")
async def get_dashboard_stats(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get dashboard statistics"""
    # Client stats
    total_clients = db.query(Client).filter(
        and_(Client.user_id == current_user.id, Client.is_active == True)
    ).count()
    
    # Notice stats
    notice_query = db.query(Notice).join(Client).filter(Client.user_id == current_user.id)
    
    total_notices = notice_query.count()
    new_notices = notice_query.filter(Notice.status == NoticeStatus.NEW).count()
    pending_replies = notice_query.filter(Notice.status == NoticeStatus.PENDING).count()
    
    # Today's notices
    today = datetime.utcnow().date()
    notices_today = notice_query.filter(
        func.date(Notice.received_date) == today
    ).count()
    
    # Overdue notices
    overdue_notices = notice_query.filter(
        and_(
            Notice.due_date < datetime.utcnow(),
            Notice.status.in_([NoticeStatus.NEW, NoticeStatus.PENDING])
        )
    ).count()
    
    return {
        "total_clients": total_clients,
        "total_notices": total_notices,
        "new_notices": new_notices,
        "pending_replies": pending_replies,
        "notices_today": notices_today,
        "overdue_notices": overdue_notices
    }


@router.get("/recent-notices")
async def get_recent_notices(
    limit: int = 10,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get recent notices"""
    notices = db.query(Notice).join(Client).filter(
        Client.user_id == current_user.id
    ).order_by(desc(Notice.received_date)).limit(limit).all()
    
    return [
        {
            "id": notice.id,
            "notice_id": notice.notice_id,
            "subject": notice.subject,
            "status": notice.status,
            "received_date": notice.received_date,
            "due_date": notice.due_date,
            "client_name": notice.client.client_name,
            "client_gstin": notice.client.gstin
        }
        for notice in notices
    ]


@router.get("/notice-trends")
async def get_notice_trends(
    days: int = 30,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get notice trends over time"""
    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=days)
    
    # Get daily notice counts
    daily_counts = db.query(
        func.date(Notice.received_date).label('date'),
        func.count(Notice.id).label('count')
    ).join(Client).filter(
        and_(
            Client.user_id == current_user.id,
            Notice.received_date >= start_date,
            Notice.received_date <= end_date
        )
    ).group_by(func.date(Notice.received_date)).all()
    
    # Format response
    trends = []
    for date, count in daily_counts:
        trends.append({
            "date": date.isoformat(),
            "count": count
        })
    
    return sorted(trends, key=lambda x: x["date"])


@router.get("/status-distribution")
async def get_status_distribution(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get notice status distribution"""
    status_counts = db.query(
        Notice.status,
        func.count(Notice.id).label('count')
    ).join(Client).filter(
        Client.user_id == current_user.id
    ).group_by(Notice.status).all()
    
    return [
        {
            "status": status,
            "count": count
        }
        for status, count in status_counts
    ]


@router.get("/client-summary")
async def get_client_summary(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get client summary with notice counts"""
    clients = db.query(Client).filter(
        and_(Client.user_id == current_user.id, Client.is_active == True)
    ).all()
    
    client_summary = []
    for client in clients:
        notice_count = db.query(Notice).filter(Notice.client_id == client.id).count()
        pending_count = db.query(Notice).filter(
            and_(
                Notice.client_id == client.id,
                Notice.status.in_([NoticeStatus.NEW, NoticeStatus.PENDING])
            )
        ).count()
        
        client_summary.append({
            "id": client.id,
            "gstin": client.gstin,
            "client_name": client.client_name,
            "total_notices": notice_count,
            "pending_notices": pending_count,
            "last_sync": client.last_sync_at,
            "auto_sync_enabled": client.auto_sync_enabled
        })
    
    return sorted(client_summary, key=lambda x: x["total_notices"], reverse=True)
