import React, { useState, useRef } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Avatar,
  Stack,
  Divider,
  Paper,
  IconButton,
  Chip,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Alert,
  CircularProgress
} from '@mui/material';
import {
  Close as CloseIcon,
  Person as PersonIcon,
  Business as BusinessIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  Badge as BadgeIcon,
  CalendarToday as CalendarIcon,
  Settings as SettingsIcon,
  PhotoCamera as PhotoCameraIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Upload as UploadIcon
} from '@mui/icons-material';
import { User } from '../../types/schema';

interface ProfileModalProps {
  user: User | null;
  open: boolean;
  onClose: () => void;
  onProfilePictureUpdate?: (imageUrl: string) => void;
}

const ProfileModal: React.FC<ProfileModalProps> = ({
  user,
  open,
  onClose,
  onProfilePictureUpdate
}) => {
  const [profilePicMenuOpen, setProfilePicMenuOpen] = useState(false);
  const [profilePicture, setProfilePicture] = useState<string | null>(null);
  const [uploading, setUploading] = useState(false);
  const [uploadMessage, setUploadMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const profilePicButtonRef = useRef<HTMLDivElement>(null);

  if (!user) return null;

  const handleProfilePicMenuOpen = () => {
    setProfilePicMenuOpen(true);
  };

  const handleProfilePicMenuClose = () => {
    setProfilePicMenuOpen(false);
  };

  const handleFileSelect = () => {
    handleProfilePicMenuClose();
    fileInputRef.current?.click();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        setUploadMessage({ type: 'error', text: 'Please select a valid image file' });
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        setUploadMessage({ type: 'error', text: 'Image size should be less than 5MB' });
        return;
      }

      setUploading(true);

      // Create preview URL
      const reader = new FileReader();
      reader.onload = (e) => {
        const imageUrl = e.target?.result as string;
        setProfilePicture(imageUrl);
        setUploading(false);
        setUploadMessage({ type: 'success', text: 'Profile picture updated successfully!' });

        // Call the callback if provided
        if (onProfilePictureUpdate) {
          onProfilePictureUpdate(imageUrl);
        }

        // Clear message after 3 seconds
        setTimeout(() => setUploadMessage(null), 3000);
      };

      reader.onerror = () => {
        setUploading(false);
        setUploadMessage({ type: 'error', text: 'Failed to upload image' });
      };

      reader.readAsDataURL(file);
    }
  };

  const handleRemoveProfilePic = () => {
    handleProfilePicMenuClose();
    setProfilePicture(null);
    setUploadMessage({ type: 'success', text: 'Profile picture removed successfully!' });

    if (onProfilePictureUpdate) {
      onProfilePictureUpdate('');
    }

    setTimeout(() => setUploadMessage(null), 3000);
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: '16px',
          boxShadow: '0 8px 32px rgba(0,0,0,0.12)',
        }
      }}
    >
      <DialogTitle sx={{ 
        p: 0,
        position: 'relative'
      }}>
        {/* Header with gradient background */}
        <Box sx={{
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          p: 3,
          pb: 4,
          borderRadius: '16px 16px 0 0',
          position: 'relative'
        }}>
          <IconButton 
            onClick={onClose}
            sx={{ 
              position: 'absolute',
              top: 16,
              right: 16,
              color: 'white',
              '&:hover': { 
                backgroundColor: 'rgba(255,255,255,0.1)'
              }
            }}
          >
            <CloseIcon />
          </IconButton>

          {/* Profile Header */}
          <Stack alignItems="center" spacing={2}>
            <Avatar
              src={profilePicture || undefined}
              sx={{
                width: 80,
                height: 80,
                fontSize: '2rem',
                bgcolor: profilePicture ? 'transparent' : 'rgba(255,255,255,0.2)',
                color: 'white',
                border: '3px solid rgba(255,255,255,0.3)'
              }}
            >
              {!profilePicture && user.name.charAt(0).toUpperCase()}
            </Avatar>
            
            <Box sx={{ textAlign: 'center' }}>
              <Typography 
                variant="h5" 
                fontWeight="700"
                sx={{ 
                  color: 'white',
                  fontFamily: '"Inter", "Roboto", sans-serif'
                }}
              >
                {user.name}
              </Typography>
              <Chip 
                label={user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                sx={{
                  mt: 1,
                  backgroundColor: 'rgba(255,255,255,0.2)',
                  color: 'white',
                  fontWeight: '500',
                  '& .MuiChip-label': {
                    px: 2
                  }
                }}
              />
            </Box>
          </Stack>
        </Box>
      </DialogTitle>

      <DialogContent sx={{ p: 0 }}>
        <Box sx={{ p: 3 }}>
          {/* Upload Message */}
          {uploadMessage && (
            <Alert
              severity={uploadMessage.type}
              onClose={() => setUploadMessage(null)}
              sx={{ mb: 3 }}
            >
              {uploadMessage.text}
            </Alert>
          )}

          <Typography
            variant="h6"
            fontWeight="600"
            sx={{
              mb: 3,
              color: '#1a1a1a',
              fontFamily: '"Inter", "Roboto", sans-serif'
            }}
          >
            Profile Information
          </Typography>

          <Stack spacing={3}>
            {/* Personal Information */}
            <Paper sx={{ 
              p: 3, 
              backgroundColor: '#f8f9fa',
              borderRadius: '12px',
              border: '1px solid #e9ecef'
            }}>
              <Typography 
                variant="subtitle1" 
                fontWeight="600"
                sx={{ 
                  mb: 2,
                  color: '#1a1a1a',
                  fontFamily: '"Inter", "Roboto", sans-serif'
                }}
              >
                Personal Details
              </Typography>
              
              <Stack spacing={2}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <PersonIcon sx={{ color: '#666666', fontSize: '20px' }} />
                  <Box>
                    <Typography variant="caption" color="text.secondary">
                      Full Name
                    </Typography>
                    <Typography variant="body1" fontWeight="500">
                      {user.name}
                    </Typography>
                  </Box>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <EmailIcon sx={{ color: '#666666', fontSize: '20px' }} />
                  <Box>
                    <Typography variant="caption" color="text.secondary">
                      Email Address
                    </Typography>
                    <Typography variant="body1" fontWeight="500">
                      {user.email}
                    </Typography>
                  </Box>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <PhoneIcon sx={{ color: '#666666', fontSize: '20px' }} />
                  <Box>
                    <Typography variant="caption" color="text.secondary">
                      Mobile Number
                    </Typography>
                    <Typography variant="body1" fontWeight="500">
                      +91 98765 43210
                    </Typography>
                  </Box>
                </Box>
              </Stack>
            </Paper>

            {/* Professional Information */}
            <Paper sx={{ 
              p: 3, 
              backgroundColor: '#f8f9fa',
              borderRadius: '12px',
              border: '1px solid #e9ecef'
            }}>
              <Typography 
                variant="subtitle1" 
                fontWeight="600"
                sx={{ 
                  mb: 2,
                  color: '#1a1a1a',
                  fontFamily: '"Inter", "Roboto", sans-serif'
                }}
              >
                Professional Details
              </Typography>
              
              <Stack spacing={2}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <BadgeIcon sx={{ color: '#666666', fontSize: '20px' }} />
                  <Box>
                    <Typography variant="caption" color="text.secondary">
                      Role
                    </Typography>
                    <Typography variant="body1" fontWeight="500">
                      {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                    </Typography>
                  </Box>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <BusinessIcon sx={{ color: '#666666', fontSize: '20px' }} />
                  <Box>
                    <Typography variant="caption" color="text.secondary">
                      Firm Name
                    </Typography>
                    <Typography variant="body1" fontWeight="500">
                      Kumar & Associates
                    </Typography>
                  </Box>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <CalendarIcon sx={{ color: '#666666', fontSize: '20px' }} />
                  <Box>
                    <Typography variant="caption" color="text.secondary">
                      Member Since
                    </Typography>
                    <Typography variant="body1" fontWeight="500">
                      January 2024
                    </Typography>
                  </Box>
                </Box>
              </Stack>
            </Paper>

            {/* Account Status */}
            <Paper sx={{ 
              p: 3, 
              backgroundColor: '#e8f5e8',
              borderRadius: '12px',
              border: '1px solid #c8e6c9'
            }}>
              <Stack direction="row" alignItems="center" spacing={2}>
                <Box sx={{
                  width: 12,
                  height: 12,
                  borderRadius: '50%',
                  backgroundColor: '#4caf50'
                }} />
                <Box>
                  <Typography variant="subtitle2" fontWeight="600" color="#2e7d32">
                    Account Active
                  </Typography>
                  <Typography variant="caption" color="#388e3c">
                    Your ERPCA Tax Engine account is active and in good standing
                  </Typography>
                </Box>
              </Stack>
            </Paper>
          </Stack>
        </Box>
      </DialogContent>

      <DialogActions sx={{ 
        p: 3, 
        pt: 0,
        justifyContent: 'center'
      }}>
        <Button 
          onClick={onClose}
          variant="contained"
          sx={{
            px: 4,
            py: 1,
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            '&:hover': {
              background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
            },
            fontFamily: '"Inter", "Roboto", sans-serif',
            textTransform: 'none',
            borderRadius: '8px'
          }}
        >
          Close
        </Button>
      </DialogActions>

      {/* Profile Picture Menu */}
      <Menu
        anchorEl={profilePicButtonRef.current}
        open={profilePicMenuOpen}
        onClose={handleProfilePicMenuClose}
        onClick={handleProfilePicMenuClose}
        PaperProps={{
          elevation: 8,
          sx: {
            overflow: 'visible',
            filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.12))',
            mt: 1.5,
            minWidth: 180,
            borderRadius: '8px',
            border: '1px solid #e9ecef',
            '&:before': {
              content: '""',
              display: 'block',
              position: 'absolute',
              top: 0,
              left: '50%',
              width: 10,
              height: 10,
              bgcolor: 'background.paper',
              transform: 'translateX(-50%) translateY(-50%) rotate(45deg)',
              zIndex: 0,
              border: '1px solid #e9ecef',
              borderBottom: 'none',
              borderRight: 'none',
            },
          },
        }}
        transformOrigin={{ horizontal: 'center', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'center', vertical: 'bottom' }}
      >
        <MenuItem onClick={handleFileSelect}>
          <ListItemIcon>
            <UploadIcon sx={{ fontSize: '20px', color: '#1976d2' }} />
          </ListItemIcon>
          <ListItemText
            primary="Upload Photo"
            primaryTypographyProps={{
              fontSize: '14px',
              fontFamily: '"Inter", "Roboto", sans-serif'
            }}
          />
        </MenuItem>

        <MenuItem onClick={handleFileSelect}>
          <ListItemIcon>
            <PhotoCameraIcon sx={{ fontSize: '20px', color: '#1976d2' }} />
          </ListItemIcon>
          <ListItemText
            primary="Change Photo"
            primaryTypographyProps={{
              fontSize: '14px',
              fontFamily: '"Inter", "Roboto", sans-serif'
            }}
          />
        </MenuItem>

        {profilePicture && (
          <MenuItem onClick={handleRemoveProfilePic}>
            <ListItemIcon>
              <DeleteIcon sx={{ fontSize: '20px', color: '#d32f2f' }} />
            </ListItemIcon>
            <ListItemText
              primary="Remove Photo"
              primaryTypographyProps={{
                fontSize: '14px',
                fontFamily: '"Inter", "Roboto", sans-serif',
                color: '#d32f2f'
              }}
            />
          </MenuItem>
        )}
      </Menu>

      {/* Hidden File Input */}
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept="image/*"
        style={{ display: 'none' }}
      />
    </Dialog>
  );
};

export default ProfileModal;
