import React, { useState, useRef } from 'react';
import {
  App<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>pography,
  IconButton,
  Avatar,
  Box,
  Stack,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Badge,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button
} from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import NotificationsIcon from '@mui/icons-material/Notifications';
import PersonIcon from '@mui/icons-material/Person';
import SettingsIcon from '@mui/icons-material/Settings';
import LogoutIcon from '@mui/icons-material/Logout';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import { User } from '../../types/schema';
import ProfileModal from '../profile/ProfileModal';
import SettingsModal from '../settings/SettingsModal';

interface NavbarProps {
  user: User;
  onMenuClick: () => void;
  onLogout: () => void;
  onNavigate?: (route: string) => void;
  title?: string;
  notificationCount?: number;
}

const Navbar: React.FC<NavbarProps> = ({
  user,
  onMenuClick,
  onLogout,
  onNavigate,
  title = 'Dashboard',
  notificationCount = 0
}) => {
  const [profileMenuOpen, setProfileMenuOpen] = useState(false);
  const [profileModalOpen, setProfileModalOpen] = useState(false);
  const [settingsModalOpen, setSettingsModalOpen] = useState(false);
  const [logoutConfirmOpen, setLogoutConfirmOpen] = useState(false);
  const [userProfilePicture, setUserProfilePicture] = useState<string | null>(null);
  const profileButtonRef = useRef<HTMLDivElement>(null);

  const handleProfileMenuOpen = () => {
    setProfileMenuOpen(true);
  };

  const handleProfileMenuClose = () => {
    setProfileMenuOpen(false);
  };

  const handleProfileClick = () => {
    handleProfileMenuClose();
    setProfileModalOpen(true);
    console.log('Opening profile modal');
  };

  const handleSettingsClick = () => {
    handleProfileMenuClose();
    setSettingsModalOpen(true);
    console.log('Opening settings modal');
  };

  const handleLogoutClick = () => {
    handleProfileMenuClose();
    setLogoutConfirmOpen(true);
  };

  const confirmLogout = () => {
    setLogoutConfirmOpen(false);
    onLogout();
  };

  const handleProfilePictureUpdate = (imageUrl: string) => {
    setUserProfilePicture(imageUrl || null);
    console.log('Profile picture updated:', imageUrl);
  };

  return (
    <AppBar
      position="sticky"
      elevation={1}
      sx={{
        bgcolor: 'background.paper',
        color: 'text.primary',
        borderBottom: '1px solid #e9ecef',
        boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
      }}
    >
      <Toolbar>
        <IconButton
          edge="start"
          color="inherit"
          aria-label="menu"
          onClick={onMenuClick}
          sx={{ mr: 2 }}
        >
          <MenuIcon />
        </IconButton>

        <Typography
          variant="h6"
          component="div"
          sx={{
            flexGrow: 1,
            fontWeight: '600',
            fontSize: '20px',
            color: '#1a1a1a',
            fontFamily: '"Inter", "Roboto", sans-serif'
          }}
        >
          {title}
        </Typography>

        <Stack direction="row" spacing={2} alignItems="center">
          {/* Notifications */}
          <IconButton
            color="inherit"
            onClick={() => {
              console.log('Opening notifications panel');
              // TODO: Implement notifications panel
              alert(`You have ${notificationCount} new notifications`);
            }}
            sx={{
              '&:hover': {
                backgroundColor: 'rgba(0, 0, 0, 0.04)'
              }
            }}
          >
            <Badge
              badgeContent={notificationCount}
              color="error"
              sx={{
                '& .MuiBadge-badge': {
                  fontSize: '0.75rem',
                  minWidth: '18px',
                  height: '18px'
                }
              }}
            >
              <NotificationsIcon />
            </Badge>
          </IconButton>

          {/* User Profile Section */}
          <Box
            ref={profileButtonRef}
            onClick={handleProfileMenuOpen}
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 2,
              cursor: 'pointer',
              padding: '8px 12px',
              borderRadius: '8px',
              transition: 'all 0.2s ease',
              '&:hover': {
                backgroundColor: 'rgba(0, 0, 0, 0.04)',
                transform: 'translateY(-1px)'
              }
            }}
          >
            <Box sx={{ textAlign: 'right', display: { xs: 'none', sm: 'block' } }}>
              <Typography
                variant="body2"
                fontWeight="600"
                sx={{
                  fontSize: '14px',
                  color: '#1a1a1a',
                  fontFamily: '"Inter", "Roboto", sans-serif'
                }}
              >
                {user.name}
              </Typography>
              <Typography
                variant="caption"
                sx={{
                  fontSize: '12px',
                  color: '#666666',
                  fontFamily: '"Inter", "Roboto", sans-serif',
                  textTransform: 'capitalize'
                }}
              >
                {user.role}
              </Typography>
            </Box>

            <Stack direction="row" alignItems="center" spacing={0.5}>
              <Avatar
                src={userProfilePicture || undefined}
                sx={{
                  width: 36,
                  height: 36,
                  bgcolor: userProfilePicture ? 'transparent' : 'primary.main',
                  fontSize: '14px',
                  fontWeight: '600'
                }}
              >
                {!userProfilePicture && user.name.charAt(0).toUpperCase()}
              </Avatar>
              <KeyboardArrowDownIcon
                sx={{
                  fontSize: '18px',
                  color: '#666666',
                  transition: 'transform 0.2s ease',
                  transform: profileMenuOpen ? 'rotate(180deg)' : 'rotate(0deg)'
                }}
              />
            </Stack>
          </Box>
        </Stack>
      </Toolbar>

      {/* Profile Dropdown Menu */}
      <Menu
        anchorEl={profileButtonRef.current}
        open={profileMenuOpen}
        onClose={handleProfileMenuClose}
        onClick={handleProfileMenuClose}
        PaperProps={{
          elevation: 8,
          sx: {
            overflow: 'visible',
            filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.12))',
            mt: 1.5,
            minWidth: 200,
            borderRadius: '8px',
            border: '1px solid #e9ecef',
            '&:before': {
              content: '""',
              display: 'block',
              position: 'absolute',
              top: 0,
              right: 20,
              width: 10,
              height: 10,
              bgcolor: 'background.paper',
              transform: 'translateY(-50%) rotate(45deg)',
              zIndex: 0,
              border: '1px solid #e9ecef',
              borderBottom: 'none',
              borderRight: 'none',
            },
          },
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        {/* User Info Header */}
        <Box sx={{ px: 2, py: 1.5, borderBottom: '1px solid #f0f0f0' }}>
          <Typography
            variant="body2"
            fontWeight="600"
            sx={{
              fontSize: '14px',
              color: '#1a1a1a',
              fontFamily: '"Inter", "Roboto", sans-serif'
            }}
          >
            {user.name}
          </Typography>
          <Typography
            variant="caption"
            sx={{
              fontSize: '12px',
              color: '#666666',
              fontFamily: '"Inter", "Roboto", sans-serif'
            }}
          >
            {user.email}
          </Typography>
        </Box>

        {/* Menu Items */}
        <MenuItem
          onClick={handleProfileClick}
          sx={{
            py: 1.5,
            '&:hover': { backgroundColor: '#f8f9fa' }
          }}
        >
          <ListItemIcon sx={{ minWidth: '36px' }}>
            <PersonIcon sx={{ fontSize: '20px', color: '#666666' }} />
          </ListItemIcon>
          <ListItemText
            primary="Profile"
            primaryTypographyProps={{
              fontSize: '14px',
              fontFamily: '"Inter", "Roboto", sans-serif'
            }}
          />
        </MenuItem>

        <MenuItem
          onClick={handleSettingsClick}
          sx={{
            py: 1.5,
            '&:hover': { backgroundColor: '#f8f9fa' }
          }}
        >
          <ListItemIcon sx={{ minWidth: '36px' }}>
            <SettingsIcon sx={{ fontSize: '20px', color: '#666666' }} />
          </ListItemIcon>
          <ListItemText
            primary="Settings"
            primaryTypographyProps={{
              fontSize: '14px',
              fontFamily: '"Inter", "Roboto", sans-serif'
            }}
          />
        </MenuItem>

        <Divider sx={{ my: 0.5 }} />

        <MenuItem
          onClick={handleLogoutClick}
          sx={{
            py: 1.5,
            '&:hover': { backgroundColor: '#fff5f5', color: '#d32f2f' }
          }}
        >
          <ListItemIcon sx={{ minWidth: '36px' }}>
            <LogoutIcon sx={{ fontSize: '20px', color: '#d32f2f' }} />
          </ListItemIcon>
          <ListItemText
            primary="Logout"
            primaryTypographyProps={{
              fontSize: '14px',
              fontFamily: '"Inter", "Roboto", sans-serif',
              color: '#d32f2f'
            }}
          />
        </MenuItem>
      </Menu>

      {/* Logout Confirmation Dialog */}
      <Dialog
        open={logoutConfirmOpen}
        onClose={() => setLogoutConfirmOpen(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: { borderRadius: '12px' }
        }}
      >
        <DialogTitle sx={{ textAlign: 'center' }}>
          <LogoutIcon sx={{ fontSize: 48, color: 'warning.main', mb: 1 }} />
          <Typography variant="h5" fontWeight="600">
            Confirm Logout
          </Typography>
        </DialogTitle>

        <DialogContent sx={{ textAlign: 'center' }}>
          <Typography variant="body1" color="text.secondary">
            Are you sure you want to logout?
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            You will need to login again to access the ERPCA Tax Engine dashboard.
          </Typography>
        </DialogContent>

        <DialogActions sx={{ p: 3, gap: 2, justifyContent: 'center' }}>
          <Button
            onClick={() => setLogoutConfirmOpen(false)}
            variant="outlined"
            sx={{ minWidth: 100 }}
          >
            Cancel
          </Button>
          <Button
            onClick={confirmLogout}
            variant="contained"
            color="error"
            startIcon={<LogoutIcon />}
            sx={{ minWidth: 100 }}
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>

      {/* Profile Modal */}
      <ProfileModal
        user={user}
        open={profileModalOpen}
        onClose={() => setProfileModalOpen(false)}
        onProfilePictureUpdate={handleProfilePictureUpdate}
      />

      {/* Settings Modal */}
      <SettingsModal
        user={user}
        open={settingsModalOpen}
        onClose={() => setSettingsModalOpen(false)}
        onLogout={onLogout}
      />
    </AppBar>
  );
};

export default Navbar;