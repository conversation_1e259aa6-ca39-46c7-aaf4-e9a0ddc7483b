import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  <PERSON>alog<PERSON><PERSON>,
  Button,
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  Chip,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tabs,
  Tab
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import BusinessIcon from '@mui/icons-material/Business';
import PersonIcon from '@mui/icons-material/Person';
import PhoneIcon from '@mui/icons-material/Phone';
import EmailIcon from '@mui/icons-material/Email';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import SyncIcon from '@mui/icons-material/Sync';
import DescriptionIcon from '@mui/icons-material/Description';
import CloudDownloadIcon from '@mui/icons-material/CloudDownload';
import { Client, Notice } from '../../types/schema';
import { formatDate, formatGSTIN } from '../../utils/formatters';
import StatusPill from '../common/StatusPill';
import ClientNoticeDownload from './ClientNoticeDownload';

interface ClientDetailModalProps {
  client: Client | null;
  open: boolean;
  onClose: () => void;
  notices: Notice[];
  onSyncNotices?: (clientId: string) => Promise<void>;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`client-tabpanel-${index}`}
      aria-labelledby={`client-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
}

const ClientDetailModal: React.FC<ClientDetailModalProps> = ({
  client,
  open,
  onClose,
  notices,
  onSyncNotices
}) => {
  const [tabValue, setTabValue] = useState(0);
  const [syncing, setSyncing] = useState(false);

  if (!client) return null;

  const clientNotices = notices.filter(notice => notice.gstin === client.gstin);
  const pendingNotices = clientNotices.filter(notice => notice.status === 'new' || notice.status === 'pending');
  const repliedNotices = clientNotices.filter(notice => notice.status === 'replied');

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleSyncNotices = async () => {
    if (onSyncNotices) {
      setSyncing(true);
      try {
        await onSyncNotices(client.id);
      } finally {
        setSyncing(false);
      }
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle>
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Stack direction="row" alignItems="center" spacing={2}>
            <BusinessIcon color="primary" />
            <Box>
              <Typography variant="h6">{client.clientName}</Typography>
              <Typography variant="body2" color="text.secondary">
                GSTIN: {formatGSTIN(client.gstin)}
              </Typography>
            </Box>
          </Stack>
          <IconButton onClick={onClose}>
            <CloseIcon />
          </IconButton>
        </Stack>
      </DialogTitle>

      <DialogContent>
        <Tabs value={tabValue} onChange={handleTabChange} sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tab label="Client Details" />
          <Tab label={`Notices (${clientNotices.length})`} />
          <Tab label="Notice Download" icon={<CloudDownloadIcon />} iconPosition="start" />
          <Tab label="Sync Status" />
        </Tabs>

        <TabPanel value={tabValue} index={0}>
          {/* Client Information */}
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Basic Information
                  </Typography>
                  <Stack spacing={2}>
                    <Stack direction="row" alignItems="center" spacing={1}>
                      <BusinessIcon fontSize="small" color="action" />
                      <Typography variant="body2">
                        <strong>Company:</strong> {client.clientName}
                      </Typography>
                    </Stack>
                    <Stack direction="row" alignItems="center" spacing={1}>
                      <PersonIcon fontSize="small" color="action" />
                      <Typography variant="body2">
                        <strong>Contact Person:</strong> {client.contactPerson}
                      </Typography>
                    </Stack>
                    <Stack direction="row" alignItems="center" spacing={1}>
                      <PhoneIcon fontSize="small" color="action" />
                      <Typography variant="body2">
                        <strong>Phone:</strong> {client.phone}
                      </Typography>
                    </Stack>
                    <Stack direction="row" alignItems="center" spacing={1}>
                      <EmailIcon fontSize="small" color="action" />
                      <Typography variant="body2">
                        <strong>Email:</strong> {client.email}
                      </Typography>
                    </Stack>
                    <Stack direction="row" alignItems="center" spacing={1}>
                      <LocationOnIcon fontSize="small" color="action" />
                      <Typography variant="body2">
                        <strong>Address:</strong> {client.address}
                      </Typography>
                    </Stack>
                  </Stack>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Tax Portal Details
                  </Typography>
                  <Stack spacing={2}>
                    <Typography variant="body2">
                      <strong>GSTIN:</strong> {formatGSTIN(client.gstin)}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Login ID:</strong> {client.loginId}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Status:</strong> 
                      <Chip 
                        label={client.status} 
                        color={client.status === 'active' ? 'success' : 'default'}
                        size="small"
                        sx={{ ml: 1 }}
                      />
                    </Typography>
                    <Typography variant="body2">
                      <strong>Added Date:</strong> {formatDate(new Date(client.addedDate))}
                    </Typography>
                    <Typography variant="body2">
                      <strong>Auto Sync:</strong> 
                      <Chip 
                        label={client.autoSyncEnabled ? 'Enabled' : 'Disabled'} 
                        color={client.autoSyncEnabled ? 'success' : 'default'}
                        size="small"
                        sx={{ ml: 1 }}
                      />
                    </Typography>
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          {/* Notices Summary */}
          <Grid container spacing={3} sx={{ mb: 3 }}>
            <Grid item xs={12} sm={4}>
              <Card>
                <CardContent>
                  <Typography color="text.secondary" gutterBottom>
                    Total Notices
                  </Typography>
                  <Typography variant="h4" component="div">
                    {clientNotices.length}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Card>
                <CardContent>
                  <Typography color="text.secondary" gutterBottom>
                    Pending Replies
                  </Typography>
                  <Typography variant="h4" component="div" color="warning.main">
                    {pendingNotices.length}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={4}>
              <Card>
                <CardContent>
                  <Typography color="text.secondary" gutterBottom>
                    Replied
                  </Typography>
                  <Typography variant="h4" component="div" color="success.main">
                    {repliedNotices.length}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Notices Table */}
          <TableContainer component={Paper} variant="outlined">
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Notice ID</TableCell>
                  <TableCell>Subject</TableCell>
                  <TableCell>Type</TableCell>
                  <TableCell>Received Date</TableCell>
                  <TableCell>Due Date</TableCell>
                  <TableCell>Status</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {clientNotices.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} align="center">
                      <Typography color="text.secondary">
                        No notices found for this client
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  clientNotices.map((notice) => (
                    <TableRow key={notice.id}>
                      <TableCell>
                        <Typography variant="body2" fontFamily="monospace">
                          {notice.id}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {notice.subject}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {notice.noticeType}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {formatDate(new Date(notice.receivedDate))}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {formatDate(new Date(notice.dueDate))}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <StatusPill status={notice.status} />
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          {/* Notice Download */}
          <ClientNoticeDownload client={client} />
        </TabPanel>

        <TabPanel value={tabValue} index={3}>
          {/* Sync Status */}
          <Stack spacing={3}>
            <Card variant="outlined">
              <CardContent>
                <Stack direction="row" justifyContent="space-between" alignItems="center">
                  <Box>
                    <Typography variant="h6" gutterBottom>
                      Sync Status
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Last sync: {client.lastNoticeSync ? formatDate(new Date(client.lastNoticeSync)) : 'Never'}
                    </Typography>
                  </Box>
                  <Button
                    variant="contained"
                    startIcon={<SyncIcon />}
                    onClick={handleSyncNotices}
                    disabled={syncing}
                  >
                    {syncing ? 'Syncing...' : 'Sync Now'}
                  </Button>
                </Stack>
              </CardContent>
            </Card>

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Card>
                  <CardContent>
                    <Stack direction="row" alignItems="center" spacing={1}>
                      <DescriptionIcon color="primary" />
                      <Box>
                        <Typography variant="h6">
                          {client.totalNotices || 0}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Total Notices
                        </Typography>
                      </Box>
                    </Stack>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6}>
                <Card>
                  <CardContent>
                    <Stack direction="row" alignItems="center" spacing={1}>
                      <EmailIcon color="warning" />
                      <Box>
                        <Typography variant="h6">
                          {client.pendingReplies || 0}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Pending Replies
                        </Typography>
                      </Box>
                    </Stack>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Stack>
        </TabPanel>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>Close</Button>
      </DialogActions>
    </Dialog>
  );
};

export default ClientDetailModal;
