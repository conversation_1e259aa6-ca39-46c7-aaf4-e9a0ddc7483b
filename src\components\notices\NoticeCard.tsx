import React from 'react';
import {
  <PERSON>,
  CardContent,
  Typo<PERSON>,
  Button,
  Stack,
  Box,
  Chip
} from '@mui/material';
import DescriptionIcon from '@mui/icons-material/Description';
import BusinessIcon from '@mui/icons-material/Business';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import ScheduleIcon from '@mui/icons-material/Schedule';
import VisibilityIcon from '@mui/icons-material/Visibility';
import StatusPill from '../common/StatusPill';
import { Notice } from '../../types/schema';
import { formatDate, formatGSTIN } from '../../utils/formatters';

interface NoticeCardProps {
  notice: Notice;
  onView: () => void;
}

const NoticeCard: React.FC<NoticeCardProps> = ({ notice, onView }) => {
  const isOverdue = new Date(notice.dueDate) < new Date();
  const daysUntilDue = Math.ceil((new Date(notice.dueDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));

  return (
    <Card sx={{ boxShadow: 2, '&:hover': { boxShadow: 4 } }}>
      <CardContent>
        <Stack spacing={2}>
          <Stack direction="row" justifyContent="space-between" alignItems="flex-start">
            <Stack spacing={1}>
              <Stack direction="row" alignItems="center" spacing={1}>
                <DescriptionIcon color="primary" />
                <Typography variant="h6" fontWeight="bold">
                  {notice.id}
                </Typography>
                <StatusPill status={notice.status} />
                {isOverdue && (
                  <Chip label="Overdue" color="error" size="small" />
                )}
              </Stack>
              <Typography variant="body1" color="text.secondary">
                {notice.noticeType}
              </Typography>
            </Stack>
            <Button
              variant="outlined"
              startIcon={<VisibilityIcon />}
              onClick={onView}
            >
              View Details
            </Button>
          </Stack>

          <Typography variant="h6" sx={{ fontWeight: 500 }}>
            {notice.subject}
          </Typography>

          <Stack direction={{ xs: 'column', md: 'row' }} spacing={3}>
            <Stack spacing={1} flex={1}>
              <Stack direction="row" alignItems="center" spacing={1}>
                <BusinessIcon fontSize="small" color="action" />
                <Typography variant="body2">
                  <strong>Client:</strong> {notice.clientName}
                </Typography>
              </Stack>
              <Typography variant="body2" color="primary" fontWeight={500}>
                GSTIN: {formatGSTIN(notice.gstin)}
              </Typography>
            </Stack>

            <Stack spacing={1} flex={1}>
              <Stack direction="row" alignItems="center" spacing={1}>
                <CalendarTodayIcon fontSize="small" color="action" />
                <Typography variant="body2">
                  <strong>Received:</strong> {formatDate(new Date(notice.receivedDate))}
                </Typography>
              </Stack>
              <Stack direction="row" alignItems="center" spacing={1}>
                <ScheduleIcon fontSize="small" color={isOverdue ? 'error' : 'action'} />
                <Typography variant="body2" color={isOverdue ? 'error.main' : 'text.primary'}>
                  <strong>Due:</strong> {formatDate(new Date(notice.dueDate))}
                  {!isOverdue && daysUntilDue >= 0 && (
                    <span style={{ marginLeft: 8 }}>
                      ({daysUntilDue} days left)
                    </span>
                  )}
                </Typography>
              </Stack>
            </Stack>
          </Stack>

          <Typography variant="body2" color="text.secondary">
            {notice.description}
          </Typography>
        </Stack>
      </CardContent>
    </Card>
  );
};

export default NoticeCard;