import React, { useState, useRef } from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  <PERSON>alogContent,
  <PERSON>alogA<PERSON>,
  Button,
  Stack,
  Typography,
  IconButton,
  Box,
  Paper,
  Alert
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import DescriptionIcon from '@mui/icons-material/Description';

interface BulkUploadModalProps {
  open: boolean;
  onClose: () => void;
  onUpload: (file: File) => void;
}

const BulkUploadModal: React.FC<BulkUploadModalProps> = ({ open, onClose, onUpload }) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (file: File) => {
    const allowedTypes = [
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/csv'
    ];

    if (allowedTypes.includes(file.type)) {
      setSelectedFile(file);
    } else {
      alert('Please select a valid Excel (.xlsx, .xls) or CSV file');
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    const file = e.dataTransfer.files[0];
    if (file) handleFileSelect(file);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) handleFileSelect(file);
  };

  const handleUpload = () => {
    if (selectedFile) {
      onUpload(selectedFile);
      handleClose();
    }
  };

  const handleClose = () => {
    setSelectedFile(null);
    setDragOver(false);
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Typography variant="h6">Bulk Upload Clients</Typography>
          <IconButton onClick={handleClose}>
            <CloseIcon />
          </IconButton>
        </Stack>
      </DialogTitle>

      <DialogContent>
        <Stack spacing={3}>
          <Alert severity="info">
            Upload an Excel or CSV file with client data. Make sure your file includes columns: 
            GSTIN, Client Name, Contact Person, Phone, Email, Address.
          </Alert>

          <Paper
            sx={{
              p: 4,
              border: '2px dashed',
              borderColor: dragOver ? 'primary.main' : 'grey.300',
              bgcolor: dragOver ? 'primary.50' : 'grey.50',
              textAlign: 'center',
              cursor: 'pointer',
              transition: 'all 0.2s ease-in-out'
            }}
            onDrop={handleDrop}
            onDragOver={(e) => {
              e.preventDefault();
              setDragOver(true);
            }}
            onDragLeave={() => setDragOver(false)}
            onClick={() => fileInputRef.current?.click()}
          >
            <input
              ref={fileInputRef}
              type="file"
              accept=".xlsx,.xls,.csv"
              onChange={handleFileInputChange}
              style={{ display: 'none' }}
            />

            <Stack spacing={2} alignItems="center">
              <CloudUploadIcon sx={{ fontSize: 48, color: 'primary.main' }} />
              <Typography variant="h6">
                {selectedFile ? 'File Selected' : 'Drop your file here or click to browse'}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Supports Excel (.xlsx, .xls) and CSV files
              </Typography>
            </Stack>
          </Paper>

          {selectedFile && (
            <Paper sx={{ p: 2, bgcolor: 'success.50' }}>
              <Stack direction="row" alignItems="center" spacing={2}>
                <DescriptionIcon color="success" />
                <Box>
                  <Typography variant="body1" fontWeight={500}>
                    {selectedFile.name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {(selectedFile.size / 1024).toFixed(1)} KB
                  </Typography>
                </Box>
              </Stack>
            </Paper>
          )}

          <Alert severity="warning">
            <Typography variant="body2">
              <strong>Note:</strong> Existing clients with the same GSTIN will be skipped. 
              Make sure your data is accurate before uploading.
            </Typography>
          </Alert>
        </Stack>
      </DialogContent>

      <DialogActions sx={{ p: 3 }}>
        <Button onClick={handleClose} variant="outlined">
          Cancel
        </Button>
        <Button 
          onClick={handleUpload} 
          variant="contained" 
          disabled={!selectedFile}
        >
          Upload Clients
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default BulkUploadModal;