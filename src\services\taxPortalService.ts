import { Client, Notice, TaxPortalSession, NoticeSyncLog } from '../types/schema';
import { NoticeStatus } from '../types/enums';

export class TaxPortalService {
  private static instance: TaxPortalService;
  private activeSessions: Map<string, TaxPortalSession> = new Map();

  static getInstance(): TaxPortalService {
    if (!TaxPortalService.instance) {
      TaxPortalService.instance = new TaxPortalService();
    }
    return TaxPortalService.instance;
  }

  /**
   * Authenticate with tax portal for a client
   */
  async authenticateClient(client: Client): Promise<TaxPortalSession> {
    try {
      // Simulate tax portal login
      console.log(`Authenticating client ${client.clientName} with GSTIN: ${client.gstin}`);
      
      // In real implementation, this would make API calls to tax portal
      await this.simulateDelay(2000);
      
      const session: TaxPortalSession = {
        id: `session_${Date.now()}`,
        clientId: client.id,
        gstin: client.gstin,
        sessionToken: `token_${Math.random().toString(36).substr(2, 9)}`,
        loginTime: new Date().toISOString(),
        expiryTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours
        isActive: true
      };

      this.activeSessions.set(client.id, session);
      return session;
    } catch (error) {
      throw new Error(`Failed to authenticate client ${client.clientName}: ${error}`);
    }
  }

  /**
   * Download all notices for a client (used when client is first added)
   */
  async downloadAllNotices(client: Client): Promise<{ notices: Notice[], syncLog: NoticeSyncLog }> {
    try {
      const session = await this.authenticateClient(client);
      
      // Simulate fetching notices from tax portal
      console.log(`Downloading all notices for ${client.clientName}`);
      await this.simulateDelay(3000);

      // Mock notices data - in real implementation, this would come from tax portal API
      const mockNotices: Notice[] = [
        {
          id: `NOT_${client.id}_${Date.now()}_1`,
          gstin: client.gstin,
          clientName: client.clientName,
          noticeType: 'GST Notice',
          subject: 'GSTR-1 Return Discrepancy',
          receivedDate: new Date('2024-12-15').toISOString(),
          dueDate: new Date('2024-12-25').toISOString(),
          status: NoticeStatus.NEW,
          description: 'Discrepancy found in sales data reported in GSTR-1 return',
          attachmentUrl: `/documents/notice_${client.id}_1.pdf`
        },
        {
          id: `NOT_${client.id}_${Date.now()}_2`,
          gstin: client.gstin,
          clientName: client.clientName,
          noticeType: 'Show Cause Notice',
          subject: 'Input Tax Credit Verification',
          receivedDate: new Date('2024-12-18').toISOString(),
          dueDate: new Date('2024-12-28').toISOString(),
          status: NoticeStatus.NEW,
          description: 'Verification required for ITC claimed in previous quarter',
          attachmentUrl: `/documents/notice_${client.id}_2.pdf`
        }
      ];

      const syncLog: NoticeSyncLog = {
        id: `sync_${Date.now()}`,
        clientId: client.id,
        gstin: client.gstin,
        syncDate: new Date().toISOString(),
        noticesFound: mockNotices.length,
        noticesDownloaded: mockNotices.length,
        status: 'success'
      };

      return { notices: mockNotices, syncLog };
    } catch (error) {
      const syncLog: NoticeSyncLog = {
        id: `sync_${Date.now()}`,
        clientId: client.id,
        gstin: client.gstin,
        syncDate: new Date().toISOString(),
        noticesFound: 0,
        noticesDownloaded: 0,
        status: 'failed',
        errorMessage: error instanceof Error ? error.message : 'Unknown error'
      };

      return { notices: [], syncLog };
    }
  }

  /**
   * Check for new notices (used by daily sync bot)
   */
  async checkForNewNotices(client: Client, lastSyncDate?: string): Promise<{ notices: Notice[], syncLog: NoticeSyncLog }> {
    try {
      const session = this.activeSessions.get(client.id) || await this.authenticateClient(client);
      
      console.log(`Checking for new notices for ${client.clientName} since ${lastSyncDate || 'beginning'}`);
      await this.simulateDelay(1500);

      // Simulate finding new notices
      const hasNewNotices = Math.random() > 0.7; // 30% chance of new notices
      const mockNotices: Notice[] = hasNewNotices ? [
        {
          id: `NOT_${client.id}_${Date.now()}_new`,
          gstin: client.gstin,
          clientName: client.clientName,
          noticeType: 'GST Notice',
          subject: 'New Notice - GSTR-3B Filing Reminder',
          receivedDate: new Date().toISOString(),
          dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
          status: NoticeStatus.NEW,
          description: 'Reminder for GSTR-3B filing for current month',
          attachmentUrl: `/documents/notice_${client.id}_new.pdf`
        }
      ] : [];

      const syncLog: NoticeSyncLog = {
        id: `sync_${Date.now()}`,
        clientId: client.id,
        gstin: client.gstin,
        syncDate: new Date().toISOString(),
        noticesFound: mockNotices.length,
        noticesDownloaded: mockNotices.length,
        status: 'success'
      };

      return { notices: mockNotices, syncLog };
    } catch (error) {
      const syncLog: NoticeSyncLog = {
        id: `sync_${Date.now()}`,
        clientId: client.id,
        gstin: client.gstin,
        syncDate: new Date().toISOString(),
        noticesFound: 0,
        noticesDownloaded: 0,
        status: 'failed',
        errorMessage: error instanceof Error ? error.message : 'Unknown error'
      };

      return { notices: [], syncLog };
    }
  }

  /**
   * Run daily sync for all active clients
   */
  async runDailySync(clients: Client[]): Promise<NoticeSyncLog[]> {
    const syncLogs: NoticeSyncLog[] = [];
    
    for (const client of clients.filter(c => c.status === 'active')) {
      try {
        const { syncLog } = await this.checkForNewNotices(client);
        syncLogs.push(syncLog);
      } catch (error) {
        console.error(`Failed to sync notices for client ${client.clientName}:`, error);
      }
    }

    return syncLogs;
  }

  /**
   * Get active session for a client
   */
  getActiveSession(clientId: string): TaxPortalSession | null {
    return this.activeSessions.get(clientId) || null;
  }

  /**
   * Simulate network delay
   */
  private async simulateDelay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

export const taxPortalService = TaxPortalService.getInstance();
