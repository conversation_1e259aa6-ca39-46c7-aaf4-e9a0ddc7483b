import { NoticeStatus, EmailStatus, InvoiceType, UserRole, AuthProvider } from './enums';

// Props types (data passed to components)
export interface LoginProps {
  onGoogleLogin: () => void;
  onMobileLogin: (phone: string) => void;
  loading: boolean;
}

export interface OTPProps {
  phoneNumber: string;
  onVerify: (otp: string) => void;
  onResend: () => void;
  loading: boolean;
}

export interface ClientListProps {
  clients: Client[];
  onAddClient: () => void;
  onViewClient: (clientId: string) => void;
  searchTerm: string;
  onSearch: (term: string) => void;
}

export interface NoticeListProps {
  notices: Notice[];
  onViewNotice: (noticeId: string) => void;
  filters: NoticeFilters;
  onFilterChange: (filters: NoticeFilters) => void;
}

export interface DashboardProps {
  stats: DashboardStats;
  recentNotices: Notice[];
  chartData: ChartData[];
}

// Store types (global state data)
export interface UserState {
  user: User | null;
  isAuthenticated: boolean;
  loading: boolean;
}

export interface ClientState {
  clients: Client[];
  selectedClient: Client | null;
  loading: boolean;
}

export interface NoticeState {
  notices: Notice[];
  selectedNotice: Notice | null;
  filters: NoticeFilters;
  loading: boolean;
}

// Query types (API response data)
export interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  role: UserRole;
  authProvider: AuthProvider;
  isAuthenticated: boolean;
}

export interface TaxPortalCredentials {
  username: string;
  password: string;
}

export interface Client {
  id: string;
  gstin: string;
  clientName: string;
  loginId: string;
  contactPerson: string;
  phone: string;
  email: string;
  address: string;
  addedDate: string;
  status: 'active' | 'inactive';
  primaryTaxPortal?: TaxPortalCredentials;
  secondaryTaxPortal?: TaxPortalCredentials;
  // Tax portal integration fields
  lastNoticeSync?: string;
  totalNotices?: number;
  pendingReplies?: number;
  autoSyncEnabled?: boolean;
}

export interface Notice {
  id: string;
  gstin: string;
  clientName: string;
  noticeType: string;
  subject: string;
  receivedDate: string;
  dueDate: string;
  status: NoticeStatus;
  description: string;
  attachmentUrl: string;
}

export interface Invoice {
  id: string;
  gstin: string;
  clientName: string;
  invoiceNumber: string;
  invoiceDate: string;
  invoiceType: InvoiceType;
  totalAmount: number;
  taxAmount: number;
  downloadUrl: string;
  status: 'downloaded' | 'pending' | 'failed';
}

export interface EmailHistory {
  id: string;
  noticeId: string;
  subject: string;
  sentDate: string;
  status: EmailStatus;
  recipientEmail: string;
  attachments: string[];
}

export interface DashboardStats {
  totalClients: number;
  noticesToday: number;
  repliesSent: number;
  invoicesDownloaded: number;
}

export interface NoticeFilters {
  dateRange?: { start: Date; end: Date };
  status?: NoticeStatus;
  gstin?: string;
}

export interface ChartData {
  status: string;
  count: number;
}

// Tax Portal Integration Types
export interface TaxPortalSession {
  id: string;
  clientId: string;
  gstin: string;
  sessionToken: string;
  loginTime: string;
  expiryTime: string;
  isActive: boolean;
}

export interface NoticeSyncLog {
  id: string;
  clientId: string;
  gstin: string;
  syncDate: string;
  noticesFound: number;
  noticesDownloaded: number;
  status: 'success' | 'failed' | 'partial';
  errorMessage?: string;
}

export interface DailyNoticeSync {
  date: string;
  totalClientsChecked: number;
  totalNoticesFound: number;
  successfulSyncs: number;
  failedSyncs: number;
  logs: NoticeSyncLog[];
}

// Enhanced Notice with sync information
export interface NoticeWithSync extends Notice {
  syncLogId?: string;
  autoDownloaded: boolean;
  replyStatus: 'not_sent' | 'sent' | 'pending' | 'failed';
  replyDate?: string;
}