"""
Notice Download Scheduler
Handles automated daily/periodic notice downloads using APScheduler
"""
import logging
from datetime import datetime, timedelta
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
from typing import List, Dict
import sqlite3
import json

from .notice_downloader import NoticeDownloader

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class NoticeScheduler:
    """Automated notice download scheduler"""
    
    def __init__(self, db_path: str = "notices.db"):
        self.db_path = db_path
        self.downloader = NoticeDownloader(db_path)
        self.scheduler = BackgroundScheduler()
        self.scheduler.start()
        logger.info("Notice scheduler initialized")
    
    def schedule_daily_auto_fetch(self, client_gstin: str, hour: int = 9, minute: int = 0):
        """Schedule daily auto-fetch for a client"""
        try:
            job_id = f"auto_fetch_{client_gstin}"
            
            # Remove existing job if any
            if self.scheduler.get_job(job_id):
                self.scheduler.remove_job(job_id)
            
            # Schedule new job
            self.scheduler.add_job(
                func=self._run_auto_fetch,
                trigger=CronTrigger(hour=hour, minute=minute),
                args=[client_gstin],
                id=job_id,
                name=f"Auto-fetch notices for {client_gstin}",
                replace_existing=True
            )
            
            logger.info(f"Scheduled daily auto-fetch for {client_gstin} at {hour:02d}:{minute:02d}")
            return True
            
        except Exception as e:
            logger.error(f"Error scheduling auto-fetch: {str(e)}")
            return False
    
    def schedule_interval_auto_fetch(self, client_gstin: str, hours: int = 12):
        """Schedule interval-based auto-fetch for a client"""
        try:
            job_id = f"interval_fetch_{client_gstin}"
            
            # Remove existing job if any
            if self.scheduler.get_job(job_id):
                self.scheduler.remove_job(job_id)
            
            # Schedule new job
            self.scheduler.add_job(
                func=self._run_auto_fetch,
                trigger=IntervalTrigger(hours=hours),
                args=[client_gstin],
                id=job_id,
                name=f"Interval auto-fetch notices for {client_gstin} every {hours}h",
                replace_existing=True
            )
            
            logger.info(f"Scheduled interval auto-fetch for {client_gstin} every {hours} hours")
            return True
            
        except Exception as e:
            logger.error(f"Error scheduling interval auto-fetch: {str(e)}")
            return False
    
    def _run_auto_fetch(self, client_gstin: str):
        """Run auto-fetch for a client"""
        try:
            logger.info(f"Running scheduled auto-fetch for {client_gstin}")
            result = self.downloader.auto_fetch_daily(client_gstin)
            
            if result['success']:
                logger.info(f"Auto-fetch completed successfully: {result}")
            else:
                logger.error(f"Auto-fetch failed: {result}")
            
            # Store result in scheduler logs
            self._log_scheduler_run(client_gstin, result)
            
        except Exception as e:
            logger.error(f"Error in scheduled auto-fetch: {str(e)}")
            self._log_scheduler_run(client_gstin, {"success": False, "error": str(e)})
    
    def schedule_all_active_clients(self, hour: int = 9, minute: int = 0):
        """Schedule auto-fetch for all active clients"""
        try:
            active_clients = self._get_active_clients()
            scheduled_count = 0
            
            for client in active_clients:
                if self.schedule_daily_auto_fetch(client['client_gstin'], hour, minute):
                    scheduled_count += 1
            
            logger.info(f"Scheduled auto-fetch for {scheduled_count} active clients")
            return scheduled_count
            
        except Exception as e:
            logger.error(f"Error scheduling all clients: {str(e)}")
            return 0
    
    def unschedule_client(self, client_gstin: str):
        """Remove scheduled jobs for a client"""
        try:
            job_ids = [f"auto_fetch_{client_gstin}", f"interval_fetch_{client_gstin}"]
            removed_count = 0
            
            for job_id in job_ids:
                if self.scheduler.get_job(job_id):
                    self.scheduler.remove_job(job_id)
                    removed_count += 1
            
            logger.info(f"Removed {removed_count} scheduled jobs for {client_gstin}")
            return True
            
        except Exception as e:
            logger.error(f"Error unscheduling client: {str(e)}")
            return False
    
    def get_scheduled_jobs(self) -> List[Dict]:
        """Get list of all scheduled jobs"""
        try:
            jobs = []
            for job in self.scheduler.get_jobs():
                jobs.append({
                    'id': job.id,
                    'name': job.name,
                    'next_run': job.next_run_time.isoformat() if job.next_run_time else None,
                    'trigger': str(job.trigger)
                })
            return jobs
            
        except Exception as e:
            logger.error(f"Error getting scheduled jobs: {str(e)}")
            return []
    
    def _get_active_clients(self) -> List[Dict]:
        """Get list of active clients with stored credentials"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT client_gstin, client_name, username, last_used
                FROM gst_credentials 
                WHERE is_active = 1
            ''')
            
            columns = [desc[0] for desc in cursor.description]
            results = [dict(zip(columns, row)) for row in cursor.fetchall()]
            
            conn.close()
            return results
            
        except Exception as e:
            logger.error(f"Error getting active clients: {str(e)}")
            return []
    
    def _log_scheduler_run(self, client_gstin: str, result: Dict):
        """Log scheduler run results"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Create scheduler_logs table if not exists
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS scheduler_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    client_gstin TEXT NOT NULL,
                    run_type TEXT DEFAULT 'auto_fetch',
                    result_json TEXT,
                    success BOOLEAN,
                    error_message TEXT,
                    run_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            cursor.execute('''
                INSERT INTO scheduler_logs 
                (client_gstin, result_json, success, error_message)
                VALUES (?, ?, ?, ?)
            ''', (
                client_gstin,
                json.dumps(result),
                result.get('success', False),
                result.get('error', None)
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Error logging scheduler run: {str(e)}")
    
    def get_scheduler_logs(self, client_gstin: str = None, limit: int = 50) -> List[Dict]:
        """Get scheduler run logs"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            if client_gstin:
                cursor.execute('''
                    SELECT * FROM scheduler_logs 
                    WHERE client_gstin = ? 
                    ORDER BY run_at DESC 
                    LIMIT ?
                ''', (client_gstin, limit))
            else:
                cursor.execute('''
                    SELECT * FROM scheduler_logs 
                    ORDER BY run_at DESC 
                    LIMIT ?
                ''', (limit,))
            
            columns = [desc[0] for desc in cursor.description]
            results = [dict(zip(columns, row)) for row in cursor.fetchall()]
            
            # Parse JSON results
            for result in results:
                if result['result_json']:
                    try:
                        result['result'] = json.loads(result['result_json'])
                    except:
                        result['result'] = {}
            
            conn.close()
            return results
            
        except Exception as e:
            logger.error(f"Error getting scheduler logs: {str(e)}")
            return []
    
    def run_manual_fetch_all(self):
        """Manually trigger auto-fetch for all active clients"""
        try:
            active_clients = self._get_active_clients()
            results = []
            
            for client in active_clients:
                client_gstin = client['client_gstin']
                logger.info(f"Running manual fetch for {client_gstin}")
                
                result = self.downloader.auto_fetch_daily(client_gstin)
                result['client_gstin'] = client_gstin
                result['client_name'] = client['client_name']
                results.append(result)
                
                # Log the result
                self._log_scheduler_run(client_gstin, result)
            
            return results
            
        except Exception as e:
            logger.error(f"Error in manual fetch all: {str(e)}")
            return []
    
    def shutdown(self):
        """Shutdown the scheduler"""
        try:
            self.scheduler.shutdown()
            logger.info("Notice scheduler shutdown")
        except Exception as e:
            logger.error(f"Error shutting down scheduler: {str(e)}")

# Global scheduler instance
notice_scheduler = NoticeScheduler()
