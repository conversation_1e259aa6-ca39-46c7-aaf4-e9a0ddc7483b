import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  TextField,
  Grid,
  <PERSON>ert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Stack,
  Divider
} from '@mui/material';
import {
  Download as DownloadIcon,
  Schedule as ScheduleIcon,
  History as HistoryIcon,
  Settings as SettingsIcon,
  Refresh as RefreshIcon,
  CloudDownload as CloudDownloadIcon
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { Client } from '../../types/schema';

interface ClientNoticeDownloadProps {
  client: Client;
}

interface DownloadHistory {
  id: number;
  download_type: string;
  start_date: string;
  end_date: string;
  notices_found: number;
  notices_downloaded: number;
  status: string;
  started_at: string;
  completed_at?: string;
}

const ClientNoticeDownload: React.FC<ClientNoticeDownloadProps> = ({ client }) => {
  const [startDate, setStartDate] = useState<Date | null>(new Date(new Date().setFullYear(new Date().getFullYear() - 1)));
  const [endDate, setEndDate] = useState<Date | null>(new Date());
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'info'; text: string } | null>(null);
  
  // Credentials dialog
  const [credentialsDialog, setCredentialsDialog] = useState(false);
  const [credentials, setCredentials] = useState({
    username: '',
    password: ''
  });
  const [hasCredentials, setHasCredentials] = useState(false);
  
  // Download history
  const [downloadHistory, setDownloadHistory] = useState<DownloadHistory[]>([]);
  
  // Schedule settings
  const [scheduleDialog, setScheduleDialog] = useState(false);
  const [scheduleSettings, setScheduleSettings] = useState({
    scheduleType: 'daily',
    hour: 9,
    minute: 0,
    intervalHours: 12
  });

  useEffect(() => {
    checkCredentials();
    loadDownloadHistory();
  }, [client]);

  const checkCredentials = async () => {
    try {
      const response = await fetch(`/api/v1/notices/credentials/${client.gstin}`);
      const result = await response.json();
      setHasCredentials(result.success && result.data?.has_credentials);
    } catch (error) {
      console.error('Error checking credentials:', error);
    }
  };

  const loadDownloadHistory = async () => {
    try {
      const response = await fetch(`/api/v1/notices/download/history?client_gstin=${client.gstin}`);
      const result = await response.json();
      if (result.success) {
        setDownloadHistory(result.data.history || []);
      }
    } catch (error) {
      console.error('Error loading download history:', error);
    }
  };

  const handleStoreCredentials = async () => {
    if (!credentials.username || !credentials.password) {
      setMessage({ type: 'error', text: 'Please fill all credential fields' });
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/v1/notices/credentials', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          client_gstin: client.gstin,
          client_name: client.clientName,
          username: credentials.username,
          password: credentials.password
        })
      });

      const result = await response.json();
      
      if (result.success) {
        setMessage({ type: 'success', text: 'Credentials stored successfully' });
        setCredentialsDialog(false);
        setCredentials({ username: '', password: '' });
        setHasCredentials(true);
      } else {
        setMessage({ type: 'error', text: result.message || 'Failed to store credentials' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Error storing credentials' });
    } finally {
      setLoading(false);
    }
  };

  const handleFirstTimeDownload = async () => {
    if (!startDate || !endDate) {
      setMessage({ type: 'error', text: 'Please select date range' });
      return;
    }

    if (!hasCredentials) {
      setMessage({ type: 'error', text: 'Please store GST credentials first' });
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/v1/notices/download/first-time', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          client_gstin: client.gstin,
          start_date: startDate.toISOString().split('T')[0],
          end_date: endDate.toISOString().split('T')[0]
        })
      });

      const result = await response.json();
      
      if (result.success) {
        setMessage({ type: 'success', text: 'First-time download started! Check history for progress.' });
        setTimeout(() => loadDownloadHistory(), 2000);
      } else {
        setMessage({ type: 'error', text: result.message || 'Failed to start download' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Error starting download' });
    } finally {
      setLoading(false);
    }
  };

  const handleManualAutoFetch = async () => {
    if (!hasCredentials) {
      setMessage({ type: 'error', text: 'Please store GST credentials first' });
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`/api/v1/notices/download/auto-fetch/${client.gstin}`, {
        method: 'POST'
      });

      const result = await response.json();
      
      if (result.success) {
        setMessage({ type: 'success', text: 'Auto-fetch started! Check history for progress.' });
        setTimeout(() => loadDownloadHistory(), 2000);
      } else {
        setMessage({ type: 'error', text: result.message || 'Failed to start auto-fetch' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Error starting auto-fetch' });
    } finally {
      setLoading(false);
    }
  };

  const handleScheduleAutoFetch = async () => {
    if (!hasCredentials) {
      setMessage({ type: 'error', text: 'Please store GST credentials first' });
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/v1/notices/schedule', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          client_gstin: client.gstin,
          schedule_type: scheduleSettings.scheduleType,
          hour: scheduleSettings.hour,
          minute: scheduleSettings.minute,
          interval_hours: scheduleSettings.intervalHours
        })
      });

      const result = await response.json();
      
      if (result.success) {
        setMessage({ type: 'success', text: 'Auto-fetch scheduled successfully!' });
        setScheduleDialog(false);
      } else {
        setMessage({ type: 'error', text: result.message || 'Failed to schedule auto-fetch' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Error scheduling auto-fetch' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        Notice Download for {client.clientName}
      </Typography>
      <Typography variant="body2" color="text.secondary" paragraph>
        Download GST notices for this client with bulk downloads and automated fetching.
      </Typography>

      {message && (
        <Alert 
          severity={message.type} 
          onClose={() => setMessage(null)}
          sx={{ mb: 3 }}
        >
          {message.text}
        </Alert>
      )}

      {/* Credentials Status */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Stack direction="row" justifyContent="space-between" alignItems="center">
            <Box>
              <Typography variant="subtitle1" gutterBottom>
                GST Portal Credentials
              </Typography>
              <Stack direction="row" spacing={1} alignItems="center">
                <Chip 
                  label={hasCredentials ? "Credentials Stored" : "No Credentials"} 
                  color={hasCredentials ? "success" : "warning"}
                  size="small"
                />
                <Typography variant="body2" color="text.secondary">
                  GSTIN: {client.gstin}
                </Typography>
              </Stack>
            </Box>
            <Button
              variant="outlined"
              startIcon={<SettingsIcon />}
              onClick={() => setCredentialsDialog(true)}
            >
              {hasCredentials ? 'Update' : 'Setup'} Credentials
            </Button>
          </Stack>
        </CardContent>
      </Card>

      {/* Download Actions */}
      <Grid container spacing={3}>
        {/* First-Time Download */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                First-Time Download
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Download historical notices for a specific date range.
              </Typography>

              <Stack spacing={2}>
                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DatePicker
                    label="Start Date"
                    value={startDate}
                    onChange={setStartDate}
                    renderInput={(params) => <TextField {...params} size="small" />}
                  />
                </LocalizationProvider>

                <LocalizationProvider dateAdapter={AdapterDateFns}>
                  <DatePicker
                    label="End Date"
                    value={endDate}
                    onChange={setEndDate}
                    renderInput={(params) => <TextField {...params} size="small" />}
                  />
                </LocalizationProvider>

                <Button
                  variant="contained"
                  startIcon={loading ? <CircularProgress size={20} /> : <DownloadIcon />}
                  onClick={handleFirstTimeDownload}
                  disabled={loading || !hasCredentials}
                  fullWidth
                >
                  {loading ? 'Starting...' : 'Start Bulk Download'}
                </Button>
              </Stack>
            </CardContent>
          </Card>
        </Grid>

        {/* Auto-Fetch */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Auto-Fetch
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Fetch new notices from the last 7 days.
              </Typography>

              <Stack spacing={2}>
                <Button
                  variant="contained"
                  startIcon={loading ? <CircularProgress size={20} /> : <RefreshIcon />}
                  onClick={handleManualAutoFetch}
                  disabled={loading || !hasCredentials}
                  fullWidth
                >
                  {loading ? 'Fetching...' : 'Run Auto-Fetch Now'}
                </Button>

                <Button
                  variant="outlined"
                  startIcon={<ScheduleIcon />}
                  onClick={() => setScheduleDialog(true)}
                  disabled={!hasCredentials}
                  fullWidth
                >
                  Schedule Auto-Fetch
                </Button>
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Download History */}
      {downloadHistory.length > 0 && (
        <Card sx={{ mt: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Download History
            </Typography>
            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Type</TableCell>
                    <TableCell>Date Range</TableCell>
                    <TableCell>Found</TableCell>
                    <TableCell>Downloaded</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Started</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {downloadHistory.slice(0, 5).map((record) => (
                    <TableRow key={record.id}>
                      <TableCell>
                        <Chip 
                          label={record.download_type.replace('_', ' ')} 
                          size="small"
                          color={record.download_type === 'first_time' ? 'primary' : 'secondary'}
                        />
                      </TableCell>
                      <TableCell>
                        {record.start_date} to {record.end_date}
                      </TableCell>
                      <TableCell>{record.notices_found}</TableCell>
                      <TableCell>{record.notices_downloaded}</TableCell>
                      <TableCell>
                        <Chip 
                          label={record.status} 
                          size="small"
                          color={record.status === 'completed' ? 'success' : record.status === 'failed' ? 'error' : 'warning'}
                        />
                      </TableCell>
                      <TableCell>{new Date(record.started_at).toLocaleString()}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      )}

      {/* Credentials Dialog */}
      <Dialog open={credentialsDialog} onClose={() => setCredentialsDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>GST Portal Credentials</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="GST Portal Username"
                  value={credentials.username}
                  onChange={(e) => setCredentials(prev => ({ ...prev, username: e.target.value }))}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="GST Portal Password"
                  type="password"
                  value={credentials.password}
                  onChange={(e) => setCredentials(prev => ({ ...prev, password: e.target.value }))}
                />
              </Grid>
              <Grid item xs={12}>
                <Alert severity="info">
                  Credentials are encrypted and stored securely for {client.clientName} ({client.gstin}).
                </Alert>
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCredentialsDialog(false)}>Cancel</Button>
          <Button 
            variant="contained" 
            onClick={handleStoreCredentials}
            disabled={loading}
          >
            {loading ? 'Storing...' : 'Store Credentials'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Schedule Dialog */}
      <Dialog open={scheduleDialog} onClose={() => setScheduleDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Schedule Auto-Fetch</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  select
                  label="Schedule Type"
                  value={scheduleSettings.scheduleType}
                  onChange={(e) => setScheduleSettings(prev => ({ ...prev, scheduleType: e.target.value }))}
                  SelectProps={{ native: true }}
                >
                  <option value="daily">Daily</option>
                  <option value="interval">Interval</option>
                </TextField>
              </Grid>
              {scheduleSettings.scheduleType === 'daily' && (
                <>
                  <Grid item xs={6}>
                    <TextField
                      fullWidth
                      label="Hour (24h)"
                      type="number"
                      value={scheduleSettings.hour}
                      onChange={(e) => setScheduleSettings(prev => ({ ...prev, hour: parseInt(e.target.value) }))}
                      inputProps={{ min: 0, max: 23 }}
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <TextField
                      fullWidth
                      label="Minute"
                      type="number"
                      value={scheduleSettings.minute}
                      onChange={(e) => setScheduleSettings(prev => ({ ...prev, minute: parseInt(e.target.value) }))}
                      inputProps={{ min: 0, max: 59 }}
                    />
                  </Grid>
                </>
              )}
              {scheduleSettings.scheduleType === 'interval' && (
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Interval (Hours)"
                    type="number"
                    value={scheduleSettings.intervalHours}
                    onChange={(e) => setScheduleSettings(prev => ({ ...prev, intervalHours: parseInt(e.target.value) }))}
                    inputProps={{ min: 1, max: 168 }}
                  />
                </Grid>
              )}
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setScheduleDialog(false)}>Cancel</Button>
          <Button 
            variant="contained" 
            onClick={handleScheduleAutoFetch}
            disabled={loading}
          >
            {loading ? 'Scheduling...' : 'Schedule Auto-Fetch'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ClientNoticeDownload;
