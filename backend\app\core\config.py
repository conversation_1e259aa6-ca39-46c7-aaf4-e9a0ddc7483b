"""
Application configuration settings
"""
from typing import List, Optional
from pydantic import BaseSettings, validator
from decouple import config


class Settings(BaseSettings):
    # Application
    APP_NAME: str = config("APP_NAME", default="GST Notice Management System")
    APP_VERSION: str = config("APP_VERSION", default="1.0.0")
    DEBUG: bool = config("DEBUG", default=False, cast=bool)
    ENVIRONMENT: str = config("ENVIRONMENT", default="development")
    
    # Database
    DATABASE_URL: str = config("DATABASE_URL")
    DATABASE_URL_ASYNC: str = config("DATABASE_URL_ASYNC")
    
    # Redis
    REDIS_URL: str = config("REDIS_URL", default="redis://localhost:6379/0")
    
    # JWT
    SECRET_KEY: str = config("SECRET_KEY")
    ALGORITHM: str = config("ALGORITHM", default="HS256")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = config("ACCESS_TOKEN_EXPIRE_MINUTES", default=30, cast=int)
    
    # GST Portal
    GST_PORTAL_BASE_URL: str = config("GST_PORTAL_BASE_URL", default="https://services.gst.gov.in")
    GST_API_KEY: str = config("GST_API_KEY", default="")
    GST_CLIENT_ID: str = config("GST_CLIENT_ID", default="")
    GST_CLIENT_SECRET: str = config("GST_CLIENT_SECRET", default="")
    
    # Email
    SMTP_HOST: str = config("SMTP_HOST", default="smtp.gmail.com")
    SMTP_PORT: int = config("SMTP_PORT", default=587, cast=int)
    SMTP_USERNAME: str = config("SMTP_USERNAME", default="")
    SMTP_PASSWORD: str = config("SMTP_PASSWORD", default="")
    SMTP_USE_TLS: bool = config("SMTP_USE_TLS", default=True, cast=bool)
    
    # File Storage
    UPLOAD_DIR: str = config("UPLOAD_DIR", default="./uploads")
    NOTICE_STORAGE_DIR: str = config("NOTICE_STORAGE_DIR", default="./notices")
    MAX_FILE_SIZE: int = config("MAX_FILE_SIZE", default=10485760, cast=int)  # 10MB
    
    # Celery
    CELERY_BROKER_URL: str = config("CELERY_BROKER_URL", default="redis://localhost:6379/0")
    CELERY_RESULT_BACKEND: str = config("CELERY_RESULT_BACKEND", default="redis://localhost:6379/0")
    
    # Logging
    LOG_LEVEL: str = config("LOG_LEVEL", default="INFO")
    LOG_FILE: str = config("LOG_FILE", default="./logs/app.log")
    
    # Security
    CORS_ORIGINS: List[str] = config(
        "CORS_ORIGINS", 
        default="http://localhost:3000,http://localhost:5173",
        cast=lambda v: [i.strip() for i in v.split(',')]
    )
    ALLOWED_HOSTS: List[str] = config(
        "ALLOWED_HOSTS",
        default="localhost,127.0.0.1",
        cast=lambda v: [i.strip() for i in v.split(',')]
    )
    
    # Notice Configuration
    DEFAULT_FETCH_INTERVAL_HOURS: int = config("DEFAULT_FETCH_INTERVAL_HOURS", default=24, cast=int)
    MAX_NOTICE_FETCH_YEARS: int = config("MAX_NOTICE_FETCH_YEARS", default=2, cast=int)
    BATCH_SIZE: int = config("BATCH_SIZE", default=100, cast=int)
    
    @validator("CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v):
        if isinstance(v, str):
            return [i.strip() for i in v.split(",")]
        return v
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Global settings instance
settings = Settings()
