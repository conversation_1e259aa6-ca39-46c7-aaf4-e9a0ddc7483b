import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON>ield,
  Stack,
  Ty<PERSON>graphy,
  IconButton,
  InputAdornment,
  Alert,
  Divider
} from '@mui/material';
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import SecurityIcon from '@mui/icons-material/Security';
import { TaxPortalCredentials as ITaxPortalCredentials } from '../../types/schema';

interface TaxPortalCredentialsProps {
  title: string;
  credentials: ITaxPortalCredentials;
  onCredentialsChange: (credentials: ITaxPortalCredentials) => void;
  errors?: {
    username?: string;
    password?: string;
  };
  required?: boolean;
}

const TaxPortalCredentials: React.FC<TaxPortalCredentialsProps> = ({
  title,
  credentials,
  onCredentialsChange,
  errors = {},
  required = false
}) => {
  const [showPassword, setShowPassword] = useState(false);

  const handleUsernameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onCredentialsChange({
      ...credentials,
      username: e.target.value
    });
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onCredentialsChange({
      ...credentials,
      password: e.target.value
    });
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <Card sx={{ bgcolor: 'grey.50', border: '1px solid', borderColor: 'grey.200' }}>
      <CardContent>
        <Stack spacing={2}>
          <Stack direction="row" alignItems="center" spacing={1}>
            <SecurityIcon color="primary" fontSize="small" />
            <Typography variant="h6" color="primary">
              {title}
            </Typography>
            {required && (
              <Typography variant="caption" color="error">
                *
              </Typography>
            )}
          </Stack>

          <Typography variant="body2" color="text.secondary">
            Enter the login credentials for accessing the tax portal
          </Typography>

          <Divider />

          <TextField
            label={`Username ${required ? '*' : ''}`}
            value={credentials.username}
            onChange={handleUsernameChange}
            error={!!errors.username}
            helperText={errors.username || 'Enter tax portal username/email'}
            fullWidth
            size="small"
          />

          <TextField
            label={`Password ${required ? '*' : ''}`}
            type={showPassword ? 'text' : 'password'}
            value={credentials.password}
            onChange={handlePasswordChange}
            error={!!errors.password}
            helperText={errors.password || 'Enter tax portal password'}
            fullWidth
            size="small"
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    aria-label="toggle password visibility"
                    onClick={togglePasswordVisibility}
                    edge="end"
                    size="small"
                  >
                    {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />

          <Alert severity="info" sx={{ mt: 1 }}>
            <Typography variant="caption">
              These credentials are encrypted and stored securely. They will be used for automated tax portal operations.
            </Typography>
          </Alert>
        </Stack>
      </CardContent>
    </Card>
  );
};

export default TaxPortalCredentials;