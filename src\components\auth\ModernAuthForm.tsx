import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>,
  TextField,
  Button,
  Stack,
  Divider,
  InputAdornment,
  IconButton,
  FormControlLabel,
  Checkbox,
  Link,
  Tabs,
  Tab,
  Alert,
  CircularProgress,
  useTheme,
  alpha
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Email,
  Phone,
  Lock,
  Google,
  Person,
  Business
} from '@mui/icons-material';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => (
  <div role="tabpanel" hidden={value !== index}>
    {value === index && <Box sx={{ pt: 3 }}>{children}</Box>}
  </div>
);

interface AuthCredentials {
  email?: string;
  phone?: string;
  password: string;
  confirmPassword?: string;
  fullName?: string;
  companyName?: string;
  rememberMe?: boolean;
}

interface ModernAuthFormProps {
  onLogin: (credentials: AuthCredentials) => void;
  onRegister: (credentials: AuthCredentials) => void;
  onGoogleLogin: () => void;
  onForgotPassword: (email: string) => void;
  loading?: boolean;
}

const ModernAuthForm: React.FC<ModernAuthFormProps> = ({
  onLogin,
  onRegister,
  onGoogleLogin,
  onForgotPassword,
  loading = false
}) => {
  const theme = useTheme();
  const [activeTab, setActiveTab] = useState(0);
  const [loginMethod, setLoginMethod] = useState<'email' | 'phone'>('email');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [formData, setFormData] = useState<AuthCredentials>({
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    fullName: '',
    companyName: '',
    rememberMe: false
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
    setErrors({});
    setFormData({
      email: '',
      phone: '',
      password: '',
      confirmPassword: '',
      fullName: '',
      companyName: '',
      rememberMe: false
    });
  };

  const handleInputChange = (field: keyof AuthCredentials) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = (isLogin: boolean = true): boolean => {
    const newErrors: Record<string, string> = {};

    // Email/Phone validation
    if (loginMethod === 'email') {
      if (!formData.email) {
        newErrors.email = 'Email is required';
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
        newErrors.email = 'Please enter a valid email address';
      }
    } else {
      if (!formData.phone) {
        newErrors.phone = 'Phone number is required';
      } else if (!/^[6-9]\d{9}$/.test(formData.phone)) {
        newErrors.phone = 'Please enter a valid 10-digit mobile number';
      }
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 8) {
      newErrors.password = 'Password must be at least 8 characters long';
    }

    // Registration-specific validations
    if (!isLogin) {
      if (!formData.fullName) {
        newErrors.fullName = 'Full name is required';
      }

      if (!formData.confirmPassword) {
        newErrors.confirmPassword = 'Please confirm your password';
      } else if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    const isLogin = activeTab === 0;

    if (!validateForm(isLogin)) {
      return;
    }

    if (isLogin) {
      onLogin(formData);
    } else {
      onRegister(formData);
    }
  };

  const handleForgotPassword = () => {
    if (!formData.email) {
      setErrors({ email: 'Please enter your email address' });
      return;
    }
    onForgotPassword(formData.email);
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
        p: 2
      }}
    >
      <Card
        sx={{
          maxWidth: 480,
          width: '100%',
          boxShadow: theme.shadows[20],
          borderRadius: 3,
          overflow: 'hidden',
          backdropFilter: 'blur(10px)',
          border: `1px solid ${alpha(theme.palette.common.white, 0.2)}`
        }}
      >
        <Box
          sx={{
            background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
            color: 'white',
            p: 3,
            textAlign: 'center'
          }}
        >
          <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
            GST Tax Portal
          </Typography>
          <Typography variant="body2" sx={{ opacity: 0.9 }}>
            Streamline your GST compliance management
          </Typography>
        </Box>

        <CardContent sx={{ p: 0 }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            variant="fullWidth"
            sx={{
              borderBottom: 1,
              borderColor: 'divider',
              '& .MuiTab-root': {
                py: 2,
                fontSize: '1rem',
                fontWeight: 600
              }
            }}
          >
            <Tab label="Sign In" />
            <Tab label="Sign Up" />
          </Tabs>

          <Box sx={{ p: 4 }}>
            {/* Google Login Button */}
            <Button
              variant="outlined"
              fullWidth
              size="large"
              startIcon={loading ? <CircularProgress size={20} /> : <Google />}
              onClick={onGoogleLogin}
              disabled={loading}
              sx={{
                py: 1.5,
                mb: 3,
                borderColor: alpha(theme.palette.grey[400], 0.5),
                '&:hover': {
                  borderColor: theme.palette.primary.main,
                  backgroundColor: alpha(theme.palette.primary.main, 0.04)
                }
              }}
            >
              Continue with Google
            </Button>

            <Divider sx={{ my: 3 }}>
              <Typography variant="body2" color="text.secondary">
                or continue with {loginMethod}
              </Typography>
            </Divider>

            <form onSubmit={handleSubmit}>
              <TabPanel value={activeTab} index={0}>
                {/* Login Form */}
                <Stack spacing={3}>
                  {/* Login Method Toggle */}
                  <Stack direction="row" spacing={1}>
                    <Button
                      variant={loginMethod === 'email' ? 'contained' : 'outlined'}
                      size="small"
                      startIcon={<Email />}
                      onClick={() => setLoginMethod('email')}
                      sx={{ flex: 1 }}
                    >
                      Email
                    </Button>
                    <Button
                      variant={loginMethod === 'phone' ? 'contained' : 'outlined'}
                      size="small"
                      startIcon={<Phone />}
                      onClick={() => setLoginMethod('phone')}
                      sx={{ flex: 1 }}
                    >
                      Mobile
                    </Button>
                  </Stack>

                  {/* Email/Phone Input */}
                  {loginMethod === 'email' ? (
                    <TextField
                      label="Email Address"
                      type="email"
                      value={formData.email}
                      onChange={handleInputChange('email')}
                      error={!!errors.email}
                      helperText={errors.email}
                      fullWidth
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Email color="action" />
                          </InputAdornment>
                        )
                      }}
                    />
                  ) : (
                    <TextField
                      label="Mobile Number"
                      value={formData.phone}
                      onChange={handleInputChange('phone')}
                      error={!!errors.phone}
                      helperText={errors.phone || 'Enter 10-digit mobile number'}
                      fullWidth
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Phone color="action" />
                            <Typography variant="body2" color="text.secondary" sx={{ ml: 1, mr: 1 }}>
                              +91
                            </Typography>
                          </InputAdornment>
                        )
                      }}
                    />
                  )}

                  {/* Password Input */}
                  <TextField
                    label="Password"
                    type={showPassword ? 'text' : 'password'}
                    value={formData.password}
                    onChange={handleInputChange('password')}
                    error={!!errors.password}
                    helperText={errors.password}
                    fullWidth
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Lock color="action" />
                        </InputAdornment>
                      ),
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={() => setShowPassword(!showPassword)}
                            edge="end"
                          >
                            {showPassword ? <VisibilityOff /> : <Visibility />}
                          </IconButton>
                        </InputAdornment>
                      )
                    }}
                  />

                  {/* Remember Me & Forgot Password */}
                  <Stack direction="row" justifyContent="space-between" alignItems="center">
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={formData.rememberMe}
                          onChange={handleInputChange('rememberMe')}
                          size="small"
                        />
                      }
                      label="Remember me"
                    />
                    <Link
                      component="button"
                      type="button"
                      variant="body2"
                      onClick={handleForgotPassword}
                      sx={{ textDecoration: 'none' }}
                    >
                      Forgot password?
                    </Link>
                  </Stack>

                  {/* Login Button */}
                  <Button
                    type="submit"
                    variant="contained"
                    fullWidth
                    size="large"
                    disabled={loading}
                    sx={{
                      py: 1.5,
                      background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
                      boxShadow: theme.shadows[8]
                    }}
                  >
                    {loading ? <CircularProgress size={24} color="inherit" /> : 'Sign In'}
                  </Button>
                </Stack>
              </TabPanel>

              <TabPanel value={activeTab} index={1}>
                {/* Registration Form */}
                <Stack spacing={3}>
                  {/* Login Method Toggle */}
                  <Stack direction="row" spacing={1}>
                    <Button
                      variant={loginMethod === 'email' ? 'contained' : 'outlined'}
                      size="small"
                      startIcon={<Email />}
                      onClick={() => setLoginMethod('email')}
                      sx={{ flex: 1 }}
                    >
                      Email
                    </Button>
                    <Button
                      variant={loginMethod === 'phone' ? 'contained' : 'outlined'}
                      size="small"
                      startIcon={<Phone />}
                      onClick={() => setLoginMethod('phone')}
                      sx={{ flex: 1 }}
                    >
                      Mobile
                    </Button>
                  </Stack>

                  {/* Full Name */}
                  <TextField
                    label="Full Name"
                    value={formData.fullName}
                    onChange={handleInputChange('fullName')}
                    error={!!errors.fullName}
                    helperText={errors.fullName}
                    fullWidth
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Person color="action" />
                        </InputAdornment>
                      )
                    }}
                  />

                  {/* Company Name (Optional) */}
                  <TextField
                    label="Company Name (Optional)"
                    value={formData.companyName}
                    onChange={handleInputChange('companyName')}
                    fullWidth
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Business color="action" />
                        </InputAdornment>
                      )
                    }}
                  />

                  {/* Email/Phone Input */}
                  {loginMethod === 'email' ? (
                    <TextField
                      label="Email Address"
                      type="email"
                      value={formData.email}
                      onChange={handleInputChange('email')}
                      error={!!errors.email}
                      helperText={errors.email}
                      fullWidth
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Email color="action" />
                          </InputAdornment>
                        )
                      }}
                    />
                  ) : (
                    <TextField
                      label="Mobile Number"
                      value={formData.phone}
                      onChange={handleInputChange('phone')}
                      error={!!errors.phone}
                      helperText={errors.phone || 'Enter 10-digit mobile number'}
                      fullWidth
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Phone color="action" />
                            <Typography variant="body2" color="text.secondary" sx={{ ml: 1, mr: 1 }}>
                              +91
                            </Typography>
                          </InputAdornment>
                        )
                      }}
                    />
                  )}

                  {/* Password Input */}
                  <TextField
                    label="Password"
                    type={showPassword ? 'text' : 'password'}
                    value={formData.password}
                    onChange={handleInputChange('password')}
                    error={!!errors.password}
                    helperText={errors.password || 'Minimum 8 characters'}
                    fullWidth
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Lock color="action" />
                        </InputAdornment>
                      ),
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={() => setShowPassword(!showPassword)}
                            edge="end"
                          >
                            {showPassword ? <VisibilityOff /> : <Visibility />}
                          </IconButton>
                        </InputAdornment>
                      )
                    }}
                  />

                  {/* Confirm Password Input */}
                  <TextField
                    label="Confirm Password"
                    type={showConfirmPassword ? 'text' : 'password'}
                    value={formData.confirmPassword}
                    onChange={handleInputChange('confirmPassword')}
                    error={!!errors.confirmPassword}
                    helperText={errors.confirmPassword}
                    fullWidth
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Lock color="action" />
                        </InputAdornment>
                      ),
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                            edge="end"
                          >
                            {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                          </IconButton>
                        </InputAdornment>
                      )
                    }}
                  />

                  {/* Terms Agreement */}
                  <FormControlLabel
                    control={<Checkbox size="small" required />}
                    label={
                      <Typography variant="body2">
                        I agree to the{' '}
                        <Link href="#" sx={{ textDecoration: 'none' }}>
                          Terms of Service
                        </Link>{' '}
                        and{' '}
                        <Link href="#" sx={{ textDecoration: 'none' }}>
                          Privacy Policy
                        </Link>
                      </Typography>
                    }
                  />

                  {/* Register Button */}
                  <Button
                    type="submit"
                    variant="contained"
                    fullWidth
                    size="large"
                    disabled={loading}
                    sx={{
                      py: 1.5,
                      background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
                      boxShadow: theme.shadows[8]
                    }}
                  >
                    {loading ? <CircularProgress size={24} color="inherit" /> : 'Create Account'}
                  </Button>
                </Stack>
              </TabPanel>
            </form>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default ModernAuthForm;
