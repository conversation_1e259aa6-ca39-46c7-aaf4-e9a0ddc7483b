"""
Client model for managing GST clients
"""
from sqlalchemy import Colum<PERSON>, <PERSON>, Inte<PERSON>, Foreign<PERSON>ey, DateTime, Boolean, Text, JSON
from sqlalchemy.orm import relationship
from .base import BaseModel


class Client(BaseModel):
    __tablename__ = "clients"
    
    # Basic Information
    gstin = Column(String(15), unique=True, index=True, nullable=False)
    client_name = Column(String(255), nullable=False)
    contact_person = Column(String(255), nullable=False)
    email = Column(String(255), nullable=False)
    phone = Column(String(20), nullable=False)
    address = Column(Text, nullable=True)
    
    # GST Portal Credentials (encrypted)
    gst_username = Column(String(255), nullable=False)
    gst_password_encrypted = Column(Text, nullable=False)  # Encrypted password
    
    # Backup credentials (optional)
    gst_username_backup = Column(String(255), nullable=True)
    gst_password_backup_encrypted = Column(Text, nullable=True)
    
    # Sync Configuration
    auto_sync_enabled = Column(Boolean, default=True, nullable=False)
    sync_interval_hours = Column(Integer, default=24, nullable=False)
    last_sync_at = Column(DateTime(timezone=True), nullable=True)
    last_successful_sync_at = Column(DateTime(timezone=True), nullable=True)
    
    # Statistics
    total_notices = Column(Integer, default=0, nullable=False)
    pending_replies = Column(Integer, default=0, nullable=False)
    
    # Additional metadata
    metadata = Column(JSON, nullable=True)  # For storing additional client-specific data
    
    # Foreign Keys
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Relationships
    user = relationship("User", back_populates="clients")
    notices = relationship("Notice", back_populates="client", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Client(gstin='{self.gstin}', name='{self.client_name}')>"
