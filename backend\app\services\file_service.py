"""
File service for handling document storage and management
"""
import os
import aiofiles
import hashlib
from datetime import datetime
from typing import Op<PERSON>, Tuple
from pathlib import Path
from ..core.config import settings
import logging

logger = logging.getLogger(__name__)


class FileService:
    """Service for managing file operations"""
    
    def __init__(self):
        self.notice_storage_dir = Path(settings.NOTICE_STORAGE_DIR)
        self.upload_dir = Path(settings.UPLOAD_DIR)
        self._ensure_directories()
    
    def _ensure_directories(self):
        """Ensure storage directories exist"""
        self.notice_storage_dir.mkdir(parents=True, exist_ok=True)
        self.upload_dir.mkdir(parents=True, exist_ok=True)
    
    async def save_notice_file(
        self, 
        gstin: str, 
        notice_id: str, 
        file_content: bytes, 
        filename: str
    ) -> str:
        """
        Save notice file to storage
        Returns the file path
        """
        try:
            # Create client-specific directory
            client_dir = self.notice_storage_dir / gstin
            client_dir.mkdir(exist_ok=True)
            
            # Generate safe filename
            safe_filename = self._sanitize_filename(filename)
            if not safe_filename.endswith('.pdf'):
                safe_filename += '.pdf'
            
            # Create unique filename with notice ID
            final_filename = f"{notice_id}_{safe_filename}"
            file_path = client_dir / final_filename
            
            # Save file
            async with aiofiles.open(file_path, 'wb') as f:
                await f.write(file_content)
            
            logger.info(f"Saved notice file: {file_path}")
            return str(file_path)
            
        except Exception as e:
            logger.error(f"Failed to save notice file for {gstin}/{notice_id}: {str(e)}")
            raise e
    
    async def save_upload_file(
        self, 
        file_content: bytes, 
        filename: str,
        user_id: int
    ) -> str:
        """
        Save uploaded file
        Returns the file path
        """
        try:
            # Create user-specific directory
            user_dir = self.upload_dir / str(user_id)
            user_dir.mkdir(exist_ok=True)
            
            # Generate unique filename with timestamp
            timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
            safe_filename = self._sanitize_filename(filename)
            final_filename = f"{timestamp}_{safe_filename}"
            file_path = user_dir / final_filename
            
            # Save file
            async with aiofiles.open(file_path, 'wb') as f:
                await f.write(file_content)
            
            logger.info(f"Saved upload file: {file_path}")
            return str(file_path)
            
        except Exception as e:
            logger.error(f"Failed to save upload file {filename}: {str(e)}")
            raise e
    
    async def read_file(self, file_path: str) -> bytes:
        """Read file content"""
        try:
            async with aiofiles.open(file_path, 'rb') as f:
                return await f.read()
        except Exception as e:
            logger.error(f"Failed to read file {file_path}: {str(e)}")
            raise e
    
    def delete_file(self, file_path: str) -> bool:
        """Delete file"""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"Deleted file: {file_path}")
                return True
            return False
        except Exception as e:
            logger.error(f"Failed to delete file {file_path}: {str(e)}")
            return False
    
    def get_file_info(self, file_path: str) -> Optional[dict]:
        """Get file information"""
        try:
            if not os.path.exists(file_path):
                return None
            
            stat = os.stat(file_path)
            return {
                "size": stat.st_size,
                "created_at": datetime.fromtimestamp(stat.st_ctime),
                "modified_at": datetime.fromtimestamp(stat.st_mtime),
                "exists": True
            }
        except Exception as e:
            logger.error(f"Failed to get file info for {file_path}: {str(e)}")
            return None
    
    def calculate_file_hash(self, file_path: str) -> Optional[str]:
        """Calculate SHA256 hash of file"""
        try:
            hash_sha256 = hashlib.sha256()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_sha256.update(chunk)
            return hash_sha256.hexdigest()
        except Exception as e:
            logger.error(f"Failed to calculate hash for {file_path}: {str(e)}")
            return None
    
    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for safe storage"""
        # Remove or replace unsafe characters
        unsafe_chars = '<>:"/\\|?*'
        for char in unsafe_chars:
            filename = filename.replace(char, '_')
        
        # Limit length
        if len(filename) > 100:
            name, ext = os.path.splitext(filename)
            filename = name[:95] + ext
        
        return filename
    
    def get_client_notice_files(self, gstin: str) -> list:
        """Get list of notice files for a client"""
        try:
            client_dir = self.notice_storage_dir / gstin
            if not client_dir.exists():
                return []
            
            files = []
            for file_path in client_dir.iterdir():
                if file_path.is_file():
                    file_info = self.get_file_info(str(file_path))
                    if file_info:
                        files.append({
                            "filename": file_path.name,
                            "path": str(file_path),
                            **file_info
                        })
            
            return sorted(files, key=lambda x: x["modified_at"], reverse=True)
            
        except Exception as e:
            logger.error(f"Failed to get client files for {gstin}: {str(e)}")
            return []
    
    def cleanup_old_files(self, days_old: int = 365) -> int:
        """
        Clean up files older than specified days
        Returns number of files deleted
        """
        deleted_count = 0
        cutoff_date = datetime.utcnow().timestamp() - (days_old * 24 * 60 * 60)
        
        try:
            # Clean upload directory
            for root, dirs, files in os.walk(self.upload_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    if os.path.getmtime(file_path) < cutoff_date:
                        if self.delete_file(file_path):
                            deleted_count += 1
            
            logger.info(f"Cleaned up {deleted_count} old files")
            return deleted_count
            
        except Exception as e:
            logger.error(f"Failed to cleanup old files: {str(e)}")
            return deleted_count
    
    def get_storage_stats(self) -> dict:
        """Get storage statistics"""
        try:
            stats = {
                "notice_files": 0,
                "notice_size_mb": 0,
                "upload_files": 0,
                "upload_size_mb": 0,
                "total_size_mb": 0
            }
            
            # Count notice files
            if self.notice_storage_dir.exists():
                for root, dirs, files in os.walk(self.notice_storage_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        size = os.path.getsize(file_path)
                        stats["notice_files"] += 1
                        stats["notice_size_mb"] += size / (1024 * 1024)
            
            # Count upload files
            if self.upload_dir.exists():
                for root, dirs, files in os.walk(self.upload_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        size = os.path.getsize(file_path)
                        stats["upload_files"] += 1
                        stats["upload_size_mb"] += size / (1024 * 1024)
            
            stats["total_size_mb"] = stats["notice_size_mb"] + stats["upload_size_mb"]
            
            # Round to 2 decimal places
            for key in ["notice_size_mb", "upload_size_mb", "total_size_mb"]:
                stats[key] = round(stats[key], 2)
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get storage stats: {str(e)}")
            return {}


# Global instance
file_service = FileService()
