# ERPCA API Integration Setup Guide

## 🚀 Quick Setup Instructions

### Step 1: Get Your API Base URL
From your Postman collection, you need to replace `{{apiBaseUrl}}` with your actual API domain.

**Example:**
- If your API is hosted at: `https://api.yourcompany.com`
- Then your full endpoint would be: `https://api.yourcompany.com/scripts/crm_api_get_account_subscription_details.php`

### Step 2: Configure the API in the Application

#### Option A: Using the Settings UI (Recommended)
1. **Open the application** at `http://localhost:5180`
2. **Click the Settings icon** (gear icon) in the top navigation
3. **Go to "API Config" tab**
4. **Enter your details:**
   - API Base URL: `https://your-actual-api-domain.com`
   - API ID: `erpca`
   - API Pass: `e736258681ac6d7126d298cc93a732db1dad2996`
   - Test Email: `<EMAIL>`
   - Test Phone: `**********`
5. **Click "Test API Connection"**
6. **If successful, copy the environment configuration**

#### Option B: Manual Environment File Setup
1. **Edit the `.env` file** in your project root
2. **Update these values:**
```bash
# Replace with your actual API domain
REACT_APP_ERPCA_API_URL=https://your-actual-api-domain.com
REACT_APP_ERPCA_API_ID=erpca
REACT_APP_ERPCA_API_PASS=e736258681ac6d7126d298cc93a732db1dad2996
REACT_APP_DEFAULT_EMAIL=<EMAIL>
REACT_APP_DEFAULT_PHONE=**********
REACT_APP_USE_MOCK_DATA=false
```
3. **Restart the development server:** `npm run dev`

### Step 3: Test the Integration

#### Test GST/ERPCA Fetching:
1. **Go to Clients** → Click "Add Client"
2. **Click "Fetch from ERPCA"** button
3. **Should fetch real data** from your API
4. **Check browser console** for API call logs

#### Expected API Calls:
The system will make these exact calls from your Postman collection:

**Subscription Details:**
```
GET https://your-api-domain.com/scripts/crm_api_get_account_subscription_details.php?email=<EMAIL>&phone=**********

Headers:
Id: erpca
Pass: e736258681ac6d7126d298cc93a732db1dad2996
Content-Type: application/json
```

**Active Users:**
```
GET https://your-api-domain.com/scripts/get_total_active_user_of_account.php?email=<EMAIL>&phone=**********

Headers:
Id: erpca
Pass: e736258681ac6d7126d298cc93a732db1dad2996
Content-Type: application/json
```

### Step 4: Troubleshooting

#### Common Issues:

**1. CORS Errors:**
- Your API server needs to allow CORS requests from `http://localhost:5180`
- Add CORS headers to your API responses

**2. 404 Not Found:**
- Verify your API base URL is correct
- Check that the PHP scripts exist at the specified paths

**3. Authentication Errors:**
- Verify the API ID and Pass are correct
- Check that your API server accepts these headers

**4. Network Errors:**
- Ensure your API server is running and accessible
- Test the API directly in Postman first

#### Debug Steps:
1. **Open browser developer tools** (F12)
2. **Go to Network tab**
3. **Try the API fetch**
4. **Check the network requests** for errors
5. **Look at console logs** for detailed error messages

### Step 5: Production Deployment

When deploying to production:

1. **Update environment variables** on your hosting platform
2. **Set production API URL**
3. **Ensure CORS is configured** for your production domain
4. **Test all API endpoints** in production environment

## 🎯 Current Status

- ✅ **Mock Data**: Working perfectly for development/demo
- ✅ **API Structure**: Ready for your real API
- ✅ **Error Handling**: Graceful fallback to mock data
- ✅ **UI Integration**: Complete client fetching interface
- ⏳ **Real API**: Waiting for your API base URL

## 📞 Need Help?

If you encounter issues:
1. Check the browser console for error messages
2. Verify your API is accessible via Postman
3. Ensure CORS is properly configured
4. Test with the API configuration tool in Settings

The system is fully ready for your real API integration! 🚀
