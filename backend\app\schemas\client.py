"""
Client schemas for API requests and responses
"""
from typing import Op<PERSON>, Dict, Any
from pydantic import BaseModel, EmailStr, validator
from datetime import datetime


class ClientBase(BaseModel):
    gstin: str
    client_name: str
    contact_person: str
    email: EmailStr
    phone: str
    address: Optional[str] = None
    auto_sync_enabled: bool = True
    sync_interval_hours: int = 24
    
    @validator('gstin')
    def validate_gstin(cls, v):
        from ..core.security import validate_gstin
        if not validate_gstin(v):
            raise ValueError('Invalid GSTIN format')
        return v.upper()
    
    @validator('phone')
    def validate_phone(cls, v):
        from ..core.security import validate_phone
        if not validate_phone(v):
            raise ValueError('Invalid phone number format')
        return v


class ClientCreate(ClientBase):
    gst_username: str
    gst_password: str
    gst_username_backup: Optional[str] = None
    gst_password_backup: Optional[str] = None
    
    @validator('gst_username')
    def validate_gst_username(cls, v):
        if len(v) < 3:
            raise ValueError('GST username must be at least 3 characters long')
        return v
    
    @validator('gst_password')
    def validate_gst_password(cls, v):
        if len(v) < 6:
            raise ValueError('GST password must be at least 6 characters long')
        return v


class ClientUpdate(BaseModel):
    client_name: Optional[str] = None
    contact_person: Optional[str] = None
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    address: Optional[str] = None
    auto_sync_enabled: Optional[bool] = None
    sync_interval_hours: Optional[int] = None
    gst_username: Optional[str] = None
    gst_password: Optional[str] = None
    gst_username_backup: Optional[str] = None
    gst_password_backup: Optional[str] = None
    is_active: Optional[bool] = None


class ClientInDB(ClientBase):
    id: int
    user_id: int
    total_notices: int
    pending_replies: int
    last_sync_at: Optional[datetime]
    last_successful_sync_at: Optional[datetime]
    metadata: Optional[Dict[str, Any]]
    is_active: bool
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class Client(ClientInDB):
    # Exclude sensitive fields from API response
    pass


class ClientWithCredentials(ClientInDB):
    """Client schema that includes masked credentials for admin view"""
    gst_username_masked: Optional[str] = None
    gst_username_backup_masked: Optional[str] = None


class ClientSyncStatus(BaseModel):
    client_id: int
    gstin: str
    client_name: str
    last_sync_at: Optional[datetime]
    last_successful_sync_at: Optional[datetime]
    sync_status: str  # 'never', 'success', 'failed', 'in_progress'
    next_sync_due: Optional[datetime]
    auto_sync_enabled: bool
    total_notices: int
    pending_replies: int


class ClientStats(BaseModel):
    total_clients: int
    active_clients: int
    clients_with_pending_replies: int
    clients_synced_today: int
    clients_never_synced: int
    clients_with_sync_errors: int


class BulkClientUpload(BaseModel):
    """Schema for bulk client upload"""
    clients: list[ClientCreate]
    
    @validator('clients')
    def validate_clients_list(cls, v):
        if len(v) == 0:
            raise ValueError('At least one client must be provided')
        if len(v) > 100:
            raise ValueError('Maximum 100 clients can be uploaded at once')
        return v


class ClientSyncRequest(BaseModel):
    """Request to sync specific clients"""
    client_ids: list[int]
    force_sync: bool = False  # Force sync even if recently synced
    
    @validator('client_ids')
    def validate_client_ids(cls, v):
        if len(v) == 0:
            raise ValueError('At least one client ID must be provided')
        if len(v) > 50:
            raise ValueError('Maximum 50 clients can be synced at once')
        return v
