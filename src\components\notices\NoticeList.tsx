import React, { useState } from 'react';
import {
  Box,
  Typography,
  Stack,
  Card,
  CardContent
} from '@mui/material';
import NoticeFilters from './NoticeFilters';
import NoticeCard from './NoticeCard';
import NoticeDetailModal from './NoticeDetailModal';
import { Notice, NoticeFilters as INoticeFilters } from '../../types/schema';

interface NoticeListProps {
  notices: Notice[];
  onSendReply: (noticeId: string, reply: string) => void;
}

const NoticeList: React.FC<NoticeListProps> = ({ notices, onSendReply }) => {
  const [filters, setFilters] = useState<INoticeFilters>({});
  const [selectedNotice, setSelectedNotice] = useState<Notice | null>(null);

  const filteredNotices = notices.filter(notice => {
    if (filters.status && notice.status !== filters.status) return false;
    if (filters.gstin && !notice.gstin.toLowerCase().includes(filters.gstin.toLowerCase())) return false;
    if (filters.dateRange) {
      const noticeDate = new Date(notice.receivedDate);
      if (noticeDate < filters.dateRange.start || noticeDate > filters.dateRange.end) return false;
    }
    return true;
  });

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom fontWeight="bold">
        Notice Management
      </Typography>

      <NoticeFilters filters={filters} onFilterChange={setFilters} />

      <Stack spacing={2} sx={{ mt: 3 }}>
        {filteredNotices.map((notice) => (
          <NoticeCard
            key={notice.id}
            notice={notice}
            onView={() => setSelectedNotice(notice)}
          />
        ))}
      </Stack>

      {filteredNotices.length === 0 && (
        <Card sx={{ mt: 3 }}>
          <CardContent sx={{ textAlign: 'center', py: 6 }}>
            <Typography variant="h6" color="text.secondary" gutterBottom>
              No notices found
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Try adjusting your filter criteria
            </Typography>
          </CardContent>
        </Card>
      )}

      <NoticeDetailModal
        notice={selectedNotice}
        open={!!selectedNotice}
        onClose={() => setSelectedNotice(null)}
        onSendReply={onSendReply}
      />
    </Box>
  );
};

export default NoticeList;