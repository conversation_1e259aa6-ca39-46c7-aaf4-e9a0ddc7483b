import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Stepper,
  Step,
  StepLabel,
  CircularProgress,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Download as DownloadIcon,
  Schedule as ScheduleIcon,
  History as HistoryIcon,
  Settings as SettingsIcon,
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  Refresh as RefreshIcon,
  CloudDownload as CloudDownloadIcon
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';

interface Client {
  id: string;
  gstin: string;
  clientName: string;
  hasCredentials?: boolean;
}

interface DownloadHistory {
  id: number;
  client_gstin: string;
  download_type: string;
  start_date: string;
  end_date: string;
  notices_found: number;
  notices_downloaded: number;
  status: string;
  started_at: string;
  completed_at?: string;
}

interface ScheduledJob {
  id: string;
  name: string;
  next_run: string;
  trigger: string;
}

const NoticeDownloadManager: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'first-time' | 'auto-fetch' | 'schedule' | 'history'>('first-time');
  const [clients, setClients] = useState<Client[]>([]);
  const [selectedClient, setSelectedClient] = useState<string>('');
  const [startDate, setStartDate] = useState<Date | null>(new Date(new Date().setFullYear(new Date().getFullYear() - 1)));
  const [endDate, setEndDate] = useState<Date | null>(new Date());
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'info'; text: string } | null>(null);
  
  // Credentials dialog
  const [credentialsDialog, setCredentialsDialog] = useState(false);
  const [credentials, setCredentials] = useState({
    username: '',
    password: '',
    clientName: ''
  });
  
  // Download history
  const [downloadHistory, setDownloadHistory] = useState<DownloadHistory[]>([]);
  const [scheduledJobs, setScheduledJobs] = useState<ScheduledJob[]>([]);
  
  // Schedule settings
  const [scheduleSettings, setScheduleSettings] = useState({
    scheduleType: 'daily',
    hour: 9,
    minute: 0,
    intervalHours: 12
  });

  useEffect(() => {
    loadClients();
    loadDownloadHistory();
    loadScheduledJobs();
  }, []);

  const loadClients = async () => {
    try {
      // Mock clients data - replace with actual API call
      const mockClients: Client[] = [
        { id: '1', gstin: '27AAPFU0939F1ZV', clientName: 'ABC Enterprises Pvt Ltd', hasCredentials: true },
        { id: '2', gstin: '29AABCU9603R1ZX', clientName: 'XYZ Industries Ltd', hasCredentials: false },
        { id: '3', gstin: '24AAGCC7409Q1ZZ', clientName: 'PQR Trading Co', hasCredentials: true }
      ];
      setClients(mockClients);
    } catch (error) {
      console.error('Error loading clients:', error);
    }
  };

  const loadDownloadHistory = async () => {
    try {
      // Mock download history - replace with actual API call
      const mockHistory: DownloadHistory[] = [
        {
          id: 1,
          client_gstin: '27AAPFU0939F1ZV',
          download_type: 'first_time',
          start_date: '2023-01-01',
          end_date: '2024-01-01',
          notices_found: 45,
          notices_downloaded: 45,
          status: 'completed',
          started_at: '2024-01-15T09:00:00Z',
          completed_at: '2024-01-15T09:15:00Z'
        },
        {
          id: 2,
          client_gstin: '27AAPFU0939F1ZV',
          download_type: 'auto_fetch',
          start_date: '2024-01-10',
          end_date: '2024-01-17',
          notices_found: 3,
          notices_downloaded: 2,
          status: 'completed',
          started_at: '2024-01-17T09:00:00Z',
          completed_at: '2024-01-17T09:05:00Z'
        }
      ];
      setDownloadHistory(mockHistory);
    } catch (error) {
      console.error('Error loading download history:', error);
    }
  };

  const loadScheduledJobs = async () => {
    try {
      // Mock scheduled jobs - replace with actual API call
      const mockJobs: ScheduledJob[] = [
        {
          id: 'auto_fetch_27AAPFU0939F1ZV',
          name: 'Auto-fetch notices for 27AAPFU0939F1ZV',
          next_run: '2024-01-18T09:00:00Z',
          trigger: 'cron[hour=9, minute=0]'
        }
      ];
      setScheduledJobs(mockJobs);
    } catch (error) {
      console.error('Error loading scheduled jobs:', error);
    }
  };

  const handleStoreCredentials = async () => {
    if (!selectedClient || !credentials.username || !credentials.password) {
      setMessage({ type: 'error', text: 'Please fill all credential fields' });
      return;
    }

    setLoading(true);
    try {
      const client = clients.find(c => c.id === selectedClient);
      if (!client) return;

      // API call to store credentials
      const response = await fetch('/api/v1/notices/credentials', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          client_gstin: client.gstin,
          client_name: client.clientName,
          username: credentials.username,
          password: credentials.password
        })
      });

      const result = await response.json();
      
      if (result.success) {
        setMessage({ type: 'success', text: 'Credentials stored successfully' });
        setCredentialsDialog(false);
        setCredentials({ username: '', password: '', clientName: '' });
        loadClients(); // Refresh to update hasCredentials status
      } else {
        setMessage({ type: 'error', text: result.message || 'Failed to store credentials' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Error storing credentials' });
    } finally {
      setLoading(false);
    }
  };

  const handleFirstTimeDownload = async () => {
    if (!selectedClient || !startDate || !endDate) {
      setMessage({ type: 'error', text: 'Please select client and date range' });
      return;
    }

    const client = clients.find(c => c.id === selectedClient);
    if (!client?.hasCredentials) {
      setMessage({ type: 'error', text: 'Please store GST credentials for this client first' });
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/v1/notices/download/first-time', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          client_gstin: client.gstin,
          start_date: startDate.toISOString().split('T')[0],
          end_date: endDate.toISOString().split('T')[0]
        })
      });

      const result = await response.json();
      
      if (result.success) {
        setMessage({ type: 'success', text: 'First-time download started! Check history for progress.' });
        setTimeout(() => loadDownloadHistory(), 2000); // Refresh history after delay
      } else {
        setMessage({ type: 'error', text: result.message || 'Failed to start download' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Error starting download' });
    } finally {
      setLoading(false);
    }
  };

  const handleManualAutoFetch = async () => {
    if (!selectedClient) {
      setMessage({ type: 'error', text: 'Please select a client' });
      return;
    }

    const client = clients.find(c => c.id === selectedClient);
    if (!client?.hasCredentials) {
      setMessage({ type: 'error', text: 'Please store GST credentials for this client first' });
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`/api/v1/notices/download/auto-fetch/${client.gstin}`, {
        method: 'POST'
      });

      const result = await response.json();
      
      if (result.success) {
        setMessage({ type: 'success', text: 'Auto-fetch started! Check history for progress.' });
        setTimeout(() => loadDownloadHistory(), 2000);
      } else {
        setMessage({ type: 'error', text: result.message || 'Failed to start auto-fetch' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Error starting auto-fetch' });
    } finally {
      setLoading(false);
    }
  };

  const handleScheduleAutoFetch = async () => {
    if (!selectedClient) {
      setMessage({ type: 'error', text: 'Please select a client' });
      return;
    }

    const client = clients.find(c => c.id === selectedClient);
    if (!client?.hasCredentials) {
      setMessage({ type: 'error', text: 'Please store GST credentials for this client first' });
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/v1/notices/schedule', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          client_gstin: client.gstin,
          schedule_type: scheduleSettings.scheduleType,
          hour: scheduleSettings.hour,
          minute: scheduleSettings.minute,
          interval_hours: scheduleSettings.intervalHours
        })
      });

      const result = await response.json();
      
      if (result.success) {
        setMessage({ type: 'success', text: 'Auto-fetch scheduled successfully!' });
        loadScheduledJobs();
      } else {
        setMessage({ type: 'error', text: result.message || 'Failed to schedule auto-fetch' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Error scheduling auto-fetch' });
    } finally {
      setLoading(false);
    }
  };

  const handleUnschedule = async (clientGstin: string) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/v1/notices/schedule/${clientGstin}`, {
        method: 'DELETE'
      });

      const result = await response.json();
      
      if (result.success) {
        setMessage({ type: 'success', text: 'Auto-fetch unscheduled successfully!' });
        loadScheduledJobs();
      } else {
        setMessage({ type: 'error', text: result.message || 'Failed to unschedule' });
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Error unscheduling auto-fetch' });
    } finally {
      setLoading(false);
    }
  };

  const renderFirstTimeDownload = () => (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          First-Time Bulk Download
        </Typography>
        <Typography variant="body2" color="text.secondary" paragraph>
          Download all notices for a client within a specific date range. This is typically done once when setting up a new client.
        </Typography>

        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>Select Client</InputLabel>
              <Select
                value={selectedClient}
                onChange={(e) => setSelectedClient(e.target.value)}
                label="Select Client"
              >
                {clients.map((client) => (
                  <MenuItem key={client.id} value={client.id}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {client.clientName} ({client.gstin})
                      {!client.hasCredentials && (
                        <Chip label="No Credentials" color="warning" size="small" />
                      )}
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={3}>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label="Start Date"
                value={startDate}
                onChange={setStartDate}
                renderInput={(params) => <TextField {...params} fullWidth />}
              />
            </LocalizationProvider>
          </Grid>

          <Grid item xs={12} md={3}>
            <LocalizationProvider dateAdapter={AdapterDateFns}>
              <DatePicker
                label="End Date"
                value={endDate}
                onChange={setEndDate}
                renderInput={(params) => <TextField {...params} fullWidth />}
              />
            </LocalizationProvider>
          </Grid>

          <Grid item xs={12}>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                variant="contained"
                startIcon={loading ? <CircularProgress size={20} /> : <DownloadIcon />}
                onClick={handleFirstTimeDownload}
                disabled={loading}
              >
                {loading ? 'Starting Download...' : 'Start Bulk Download'}
              </Button>

              <Button
                variant="outlined"
                startIcon={<SettingsIcon />}
                onClick={() => setCredentialsDialog(true)}
                disabled={!selectedClient}
              >
                Manage Credentials
              </Button>
            </Box>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );

  const renderAutoFetch = () => (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Manual Auto-Fetch
        </Typography>
        <Typography variant="body2" color="text.secondary" paragraph>
          Manually trigger auto-fetch to download new notices from the last 7 days. This fetches only new notices that haven't been downloaded yet.
        </Typography>

        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>Select Client</InputLabel>
              <Select
                value={selectedClient}
                onChange={(e) => setSelectedClient(e.target.value)}
                label="Select Client"
              >
                {clients.map((client) => (
                  <MenuItem key={client.id} value={client.id}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {client.clientName} ({client.gstin})
                      {!client.hasCredentials && (
                        <Chip label="No Credentials" color="warning" size="small" />
                      )}
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                variant="contained"
                startIcon={loading ? <CircularProgress size={20} /> : <RefreshIcon />}
                onClick={handleManualAutoFetch}
                disabled={loading}
              >
                {loading ? 'Fetching...' : 'Run Auto-Fetch Now'}
              </Button>

              <Button
                variant="outlined"
                startIcon={<CloudDownloadIcon />}
                onClick={() => {/* Handle fetch all clients */}}
                disabled={loading}
              >
                Fetch All Clients
              </Button>
            </Box>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );

  const renderSchedule = () => (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Schedule Automated Downloads
        </Typography>
        <Typography variant="body2" color="text.secondary" paragraph>
          Set up automated daily or interval-based notice downloads for clients.
        </Typography>

        <Grid container spacing={3}>
          <Grid item xs={12} md={4}>
            <FormControl fullWidth>
              <InputLabel>Select Client</InputLabel>
              <Select
                value={selectedClient}
                onChange={(e) => setSelectedClient(e.target.value)}
                label="Select Client"
              >
                {clients.map((client) => (
                  <MenuItem key={client.id} value={client.id}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {client.clientName} ({client.gstin})
                      {!client.hasCredentials && (
                        <Chip label="No Credentials" color="warning" size="small" />
                      )}
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>

          <Grid item xs={12} md={4}>
            <FormControl fullWidth>
              <InputLabel>Schedule Type</InputLabel>
              <Select
                value={scheduleSettings.scheduleType}
                onChange={(e) => setScheduleSettings(prev => ({ ...prev, scheduleType: e.target.value }))}
                label="Schedule Type"
              >
                <MenuItem value="daily">Daily</MenuItem>
                <MenuItem value="interval">Interval</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          {scheduleSettings.scheduleType === 'daily' && (
            <>
              <Grid item xs={6} md={2}>
                <TextField
                  fullWidth
                  label="Hour (24h)"
                  type="number"
                  value={scheduleSettings.hour}
                  onChange={(e) => setScheduleSettings(prev => ({ ...prev, hour: parseInt(e.target.value) }))}
                  inputProps={{ min: 0, max: 23 }}
                />
              </Grid>
              <Grid item xs={6} md={2}>
                <TextField
                  fullWidth
                  label="Minute"
                  type="number"
                  value={scheduleSettings.minute}
                  onChange={(e) => setScheduleSettings(prev => ({ ...prev, minute: parseInt(e.target.value) }))}
                  inputProps={{ min: 0, max: 59 }}
                />
              </Grid>
            </>
          )}

          {scheduleSettings.scheduleType === 'interval' && (
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Interval (Hours)"
                type="number"
                value={scheduleSettings.intervalHours}
                onChange={(e) => setScheduleSettings(prev => ({ ...prev, intervalHours: parseInt(e.target.value) }))}
                inputProps={{ min: 1, max: 168 }}
              />
            </Grid>
          )}

          <Grid item xs={12}>
            <Button
              variant="contained"
              startIcon={loading ? <CircularProgress size={20} /> : <ScheduleIcon />}
              onClick={handleScheduleAutoFetch}
              disabled={loading}
            >
              {loading ? 'Scheduling...' : 'Schedule Auto-Fetch'}
            </Button>
          </Grid>
        </Grid>

        {/* Scheduled Jobs Table */}
        <Box sx={{ mt: 4 }}>
          <Typography variant="h6" gutterBottom>
            Currently Scheduled Jobs
          </Typography>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Client</TableCell>
                  <TableCell>Schedule</TableCell>
                  <TableCell>Next Run</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {scheduledJobs.map((job) => (
                  <TableRow key={job.id}>
                    <TableCell>{job.name}</TableCell>
                    <TableCell>{job.trigger}</TableCell>
                    <TableCell>{new Date(job.next_run).toLocaleString()}</TableCell>
                    <TableCell>
                      <Tooltip title="Remove Schedule">
                        <IconButton
                          color="error"
                          onClick={() => {
                            const gstin = job.id.replace('auto_fetch_', '').replace('interval_fetch_', '');
                            handleUnschedule(gstin);
                          }}
                        >
                          <StopIcon />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
                {scheduledJobs.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={4} align="center">
                      No scheduled jobs found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </Box>
      </CardContent>
    </Card>
  );

  const renderHistory = () => (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Download History
        </Typography>
        <Typography variant="body2" color="text.secondary" paragraph>
          View the history of all notice download operations.
        </Typography>

        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Client GSTIN</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Date Range</TableCell>
                <TableCell>Found</TableCell>
                <TableCell>Downloaded</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Started</TableCell>
                <TableCell>Completed</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {downloadHistory.map((record) => (
                <TableRow key={record.id}>
                  <TableCell>{record.client_gstin}</TableCell>
                  <TableCell>
                    <Chip
                      label={record.download_type.replace('_', ' ')}
                      size="small"
                      color={record.download_type === 'first_time' ? 'primary' : 'secondary'}
                    />
                  </TableCell>
                  <TableCell>
                    {record.start_date} to {record.end_date}
                  </TableCell>
                  <TableCell>{record.notices_found}</TableCell>
                  <TableCell>{record.notices_downloaded}</TableCell>
                  <TableCell>
                    <Chip
                      label={record.status}
                      size="small"
                      color={record.status === 'completed' ? 'success' : record.status === 'failed' ? 'error' : 'warning'}
                    />
                  </TableCell>
                  <TableCell>{new Date(record.started_at).toLocaleString()}</TableCell>
                  <TableCell>
                    {record.completed_at ? new Date(record.completed_at).toLocaleString() : '-'}
                  </TableCell>
                </TableRow>
              ))}
              {downloadHistory.length === 0 && (
                <TableRow>
                  <TableCell colSpan={8} align="center">
                    No download history found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </CardContent>
    </Card>
  );

  const renderCredentialsDialog = () => (
    <Dialog open={credentialsDialog} onClose={() => setCredentialsDialog(false)} maxWidth="sm" fullWidth>
      <DialogTitle>Manage GST Portal Credentials</DialogTitle>
      <DialogContent>
        <Box sx={{ pt: 2 }}>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="GST Portal Username"
                value={credentials.username}
                onChange={(e) => setCredentials(prev => ({ ...prev, username: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="GST Portal Password"
                type="password"
                value={credentials.password}
                onChange={(e) => setCredentials(prev => ({ ...prev, password: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12}>
              <Alert severity="info">
                Credentials are encrypted and stored securely. They are only used for automated notice downloads.
              </Alert>
            </Grid>
          </Grid>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={() => setCredentialsDialog(false)}>Cancel</Button>
        <Button
          variant="contained"
          onClick={handleStoreCredentials}
          disabled={loading}
        >
          {loading ? 'Storing...' : 'Store Credentials'}
        </Button>
      </DialogActions>
    </Dialog>
  );

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom fontWeight="bold">
        Notice Download Manager
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        Manage GST notice downloads with first-time bulk downloads and automated daily fetching.
      </Typography>

      {message && (
        <Alert
          severity={message.type}
          onClose={() => setMessage(null)}
          sx={{ mb: 3 }}
        >
          {message.text}
        </Alert>
      )}

      {/* Tab Navigation */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Box sx={{ display: 'flex', gap: 2 }}>
          {[
            { key: 'first-time', label: 'First-Time Download', icon: <DownloadIcon /> },
            { key: 'auto-fetch', label: 'Manual Auto-Fetch', icon: <RefreshIcon /> },
            { key: 'schedule', label: 'Schedule', icon: <ScheduleIcon /> },
            { key: 'history', label: 'History', icon: <HistoryIcon /> }
          ].map((tab) => (
            <Button
              key={tab.key}
              variant={activeTab === tab.key ? 'contained' : 'outlined'}
              startIcon={tab.icon}
              onClick={() => setActiveTab(tab.key as any)}
            >
              {tab.label}
            </Button>
          ))}
        </Box>
      </Box>

      {/* Tab Content */}
      {activeTab === 'first-time' && renderFirstTimeDownload()}
      {activeTab === 'auto-fetch' && renderAutoFetch()}
      {activeTab === 'schedule' && renderSchedule()}
      {activeTab === 'history' && renderHistory()}

      {/* Dialogs */}
      {renderCredentialsDialog()}
    </Box>
  );
};

export default NoticeDownloadManager;
