/**
 * API service for backend communication
 */

const API_BASE_URL = 'http://localhost:8000';

export interface BackendDashboardStats {
  total_clients: number;
  total_notices: number;
  new_notices: number;
  pending_replies: number;
  notices_today: number;
  overdue_notices: number;
}

export interface FrontendDashboardStats {
  totalClients: number;
  noticesToday: number;
  repliesSent: number;
  invoicesDownloaded: number;
}

export interface ApiClient {
  id: number;
  gstin: string;
  client_name: string;
  contact_person: string;
  email: string;
  phone: string;
  total_notices: number;
  pending_replies: number;
  last_sync_at: string;
  auto_sync_enabled: boolean;
  is_active: boolean;
}

export interface ApiNotice {
  id: number;
  notice_id: string;
  subject: string;
  notice_type: string;
  status: string;
  notice_date: string;
  due_date: string;
  received_date: string;
  client_id: number;
  description: string;
}

export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
  total?: number;
  limit?: number;
  offset?: number;
  has_more?: boolean;
}

class ApiService {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const defaultOptions: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    };

    const response = await fetch(url, { ...defaultOptions, ...options });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  // Dashboard APIs
  async getDashboardStats(): Promise<BackendDashboardStats> {
    return this.request<BackendDashboardStats>('/api/v1/dashboard/stats');
  }

  // Client APIs
  async getClients(): Promise<ApiClient[]> {
    return this.request<ApiClient[]>('/api/v1/clients/');
  }

  async getClient(id: number): Promise<ApiClient> {
    return this.request<ApiClient>(`/api/v1/clients/${id}`);
  }

  // Notice APIs
  async getNotices(): Promise<ApiResponse<ApiNotice[]>> {
    return this.request<ApiResponse<ApiNotice[]>>('/api/v1/notices/');
  }

  async getNotice(id: number): Promise<ApiNotice> {
    return this.request<ApiNotice>(`/api/v1/notices/${id}`);
  }

  // Sync APIs
  async getSyncStatus(): Promise<any[]> {
    return this.request<any[]>('/api/v1/sync/status');
  }

  async triggerManualSync(clientIds: number[]): Promise<any> {
    return this.request('/api/v1/sync/manual', {
      method: 'POST',
      body: JSON.stringify({ client_ids: clientIds, force_sync: false }),
    });
  }

  async syncAllClients(): Promise<any> {
    return this.request('/api/v1/sync/all', {
      method: 'POST',
      body: JSON.stringify({ force_sync: false }),
    });
  }

  // Health check
  async healthCheck(): Promise<{ status: string; app_name: string; version: string }> {
    return this.request('/health');
  }

  // Test connection
  async testConnection(): Promise<boolean> {
    try {
      await this.healthCheck();
      return true;
    } catch (error) {
      console.error('Backend connection failed:', error);
      return false;
    }
  }
}

// Create and export a singleton instance
export const apiService = new ApiService();

// Helper function to transform backend data to frontend format
export const transformDashboardStats = (backendStats: BackendDashboardStats): FrontendDashboardStats => ({
  totalClients: backendStats.total_clients,
  noticesToday: backendStats.notices_today,
  repliesSent: backendStats.total_notices - backendStats.pending_replies, // Calculate replies sent
  invoicesDownloaded: backendStats.total_notices, // Use total notices as proxy for now
});

export const transformClient = (backendClient: ApiClient) => ({
  id: backendClient.id,
  gstin: backendClient.gstin,
  clientName: backendClient.client_name,
  contactPerson: backendClient.contact_person,
  email: backendClient.email,
  phone: backendClient.phone,
  totalNotices: backendClient.total_notices,
  pendingReplies: backendClient.pending_replies,
  lastSyncAt: backendClient.last_sync_at,
  autoSyncEnabled: backendClient.auto_sync_enabled,
  isActive: backendClient.is_active,
  addedDate: new Date().toISOString(), // Default for now
  status: backendClient.is_active ? 'active' : 'inactive',
  address: '', // Default for now
});

export const transformNotice = (backendNotice: ApiNotice) => ({
  id: backendNotice.id,
  noticeId: backendNotice.notice_id,
  gstin: '', // Will be populated from client data
  clientName: '', // Will be populated from client data
  noticeType: backendNotice.notice_type,
  subject: backendNotice.subject,
  receivedDate: backendNotice.received_date,
  dueDate: backendNotice.due_date,
  status: backendNotice.status,
  description: backendNotice.description,
  attachmentUrl: '', // Default for now
});

export default apiService;
