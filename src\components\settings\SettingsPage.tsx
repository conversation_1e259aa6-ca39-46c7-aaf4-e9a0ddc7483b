import React, { useState } from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Tabs,
  Tab,
  TextField,
  Button,
  Stack,
  Switch,
  FormControlLabel,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Alert,
  Divider,
  Avatar,
  IconButton,
  InputAdornment,
  Grid,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import {
  Person as PersonIcon,
  Security as SecurityIcon,
  Palette as PaletteIcon,
  Schedule as ScheduleIcon,
  Visibility,
  VisibilityOff,
  PhotoCamera as PhotoCameraIcon,
  Save as SaveIcon,
  Brightness4 as DarkModeIcon,
  Brightness7 as LightModeIcon,
  Logout as LogoutIcon,
  ExitToApp as ExitToAppIcon
} from '@mui/icons-material';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => {
  return (
    <div hidden={value !== index}>
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
};

interface SettingsPageProps {
  onLogout?: () => void;
}

const SettingsPage: React.FC<SettingsPageProps> = ({ onLogout }) => {
  const { isDarkMode, toggleTheme } = useTheme();
  const [tabValue, setTabValue] = useState(0);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [saveMessage, setSaveMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [logoutConfirmOpen, setLogoutConfirmOpen] = useState(false);

  // User Settings State
  const [userSettings, setUserSettings] = useState({
    name: 'Rajesh Kumar',
    username: 'rajeshkumar123',
    email: '<EMAIL>',
    firmName: 'Kumar & Associates',
    mobile: '+919876543210',
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  // Scheduler Settings State
  const [schedulerSettings, setSchedulerSettings] = useState({
    frequency: 'once', // 'once' or 'twice'
    timeOnce: '09:00',
    timeFirst: '09:00',
    timeSecond: '18:00',
    enabled: true
  });

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleUserSettingsChange = (field: string) => (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setUserSettings(prev => ({
      ...prev,
      [field]: e.target.value
    }));
  };

  const handleSchedulerChange = (field: string) => (
    e: React.ChangeEvent<HTMLInputElement> | any
  ) => {
    const value = e.target.type === 'checkbox' ? e.target.checked : e.target.value;
    setSchedulerSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSaveUserSettings = () => {
    // Validate passwords if changing
    if (userSettings.newPassword) {
      if (userSettings.newPassword !== userSettings.confirmPassword) {
        setSaveMessage({ type: 'error', text: 'New passwords do not match' });
        return;
      }
      if (userSettings.newPassword.length < 6) {
        setSaveMessage({ type: 'error', text: 'Password must be at least 6 characters' });
        return;
      }
    }

    // TODO: Implement actual save logic
    console.log('Saving user settings:', userSettings);
    setSaveMessage({ type: 'success', text: 'Settings saved successfully!' });
    
    // Clear password fields
    setUserSettings(prev => ({
      ...prev,
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    }));

    setTimeout(() => setSaveMessage(null), 3000);
  };

  const handleSaveSchedulerSettings = () => {
    // TODO: Implement actual save logic
    console.log('Saving scheduler settings:', schedulerSettings);
    setSaveMessage({ type: 'success', text: 'Scheduler settings saved successfully!' });
    setTimeout(() => setSaveMessage(null), 3000);
  };

  const handleToggleDarkMode = () => {
    toggleTheme();
    console.log('Theme toggled to:', !isDarkMode ? 'dark' : 'light');
  };

  const handleLogoutClick = () => {
    setLogoutConfirmOpen(true);
  };

  const confirmLogout = () => {
    setLogoutConfirmOpen(false);
    if (onLogout) {
      onLogout();
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" fontWeight="bold" gutterBottom>
        Settings
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        Manage your account settings and preferences
      </Typography>

      {saveMessage && (
        <Alert 
          severity={saveMessage.type} 
          onClose={() => setSaveMessage(null)}
          sx={{ mb: 3 }}
        >
          {saveMessage.text}
        </Alert>
      )}

      <Card>
        <CardContent sx={{ p: 0 }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            sx={{ borderBottom: 1, borderColor: 'divider', px: 3 }}
          >
            <Tab
              label="User Settings"
              icon={<PersonIcon />}
              iconPosition="start"
            />
            <Tab
              label="Security"
              icon={<SecurityIcon />}
              iconPosition="start"
            />
            <Tab
              label="Appearance"
              icon={<PaletteIcon />}
              iconPosition="start"
            />
            <Tab
              label="Scheduler"
              icon={<ScheduleIcon />}
              iconPosition="start"
            />
            <Tab
              label="Account"
              icon={<ExitToAppIcon />}
              iconPosition="start"
            />
          </Tabs>

          {/* User Settings Tab */}
          <TabPanel value={tabValue} index={0}>
            <Box sx={{ px: 3 }}>
              <Stack spacing={3}>
                {/* Profile Picture */}
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>
                  <Avatar
                    sx={{ width: 80, height: 80, fontSize: '2rem' }}
                  >
                    {userSettings.name.charAt(0)}
                  </Avatar>
                  <Box>
                    <Typography variant="h6">{userSettings.name}</Typography>
                    <Typography variant="body2" color="text.secondary">
                      {userSettings.firmName}
                    </Typography>
                    <Button
                      startIcon={<PhotoCameraIcon />}
                      size="small"
                      sx={{ mt: 1 }}
                    >
                      Change Photo
                    </Button>
                  </Box>
                </Box>

                <Divider />

                {/* User Information */}
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Full Name"
                      value={userSettings.name}
                      onChange={handleUserSettingsChange('name')}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Username"
                      value={userSettings.username}
                      onChange={handleUserSettingsChange('username')}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Email Address"
                      type="email"
                      value={userSettings.email}
                      onChange={handleUserSettingsChange('email')}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Mobile Number"
                      value={userSettings.mobile}
                      onChange={handleUserSettingsChange('mobile')}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Firm Name"
                      value={userSettings.firmName}
                      onChange={handleUserSettingsChange('firmName')}
                    />
                  </Grid>
                </Grid>

                <Button
                  variant="contained"
                  startIcon={<SaveIcon />}
                  onClick={handleSaveUserSettings}
                  sx={{ alignSelf: 'flex-start' }}
                >
                  Save Changes
                </Button>
              </Stack>
            </Box>
          </TabPanel>

          {/* Security Tab */}
          <TabPanel value={tabValue} index={1}>
            <Box sx={{ px: 3 }}>
              <Stack spacing={3}>
                <Typography variant="h6">Change Password</Typography>
                
                <TextField
                  fullWidth
                  label="Current Password"
                  type={showCurrentPassword ? 'text' : 'password'}
                  value={userSettings.currentPassword}
                  onChange={handleUserSettingsChange('currentPassword')}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                          edge="end"
                        >
                          {showCurrentPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />

                <TextField
                  fullWidth
                  label="New Password"
                  type={showNewPassword ? 'text' : 'password'}
                  value={userSettings.newPassword}
                  onChange={handleUserSettingsChange('newPassword')}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={() => setShowNewPassword(!showNewPassword)}
                          edge="end"
                        >
                          {showNewPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />

                <TextField
                  fullWidth
                  label="Confirm New Password"
                  type={showConfirmPassword ? 'text' : 'password'}
                  value={userSettings.confirmPassword}
                  onChange={handleUserSettingsChange('confirmPassword')}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                          edge="end"
                        >
                          {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />

                <Button
                  variant="contained"
                  startIcon={<SaveIcon />}
                  onClick={handleSaveUserSettings}
                  sx={{ alignSelf: 'flex-start' }}
                >
                  Update Password
                </Button>
              </Stack>
            </Box>
          </TabPanel>

          {/* Appearance Tab */}
          <TabPanel value={tabValue} index={2}>
            <Box sx={{ px: 3 }}>
              <Stack spacing={3}>
                <Typography variant="h6">Theme Settings</Typography>
                
                <Paper sx={{ p: 3, border: '1px solid', borderColor: 'divider' }}>
                  <Stack direction="row" justifyContent="space-between" alignItems="center">
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      {isDarkMode ? <DarkModeIcon /> : <LightModeIcon />}
                      <Box>
                        <Typography variant="body1" fontWeight="500">
                          Dark Mode
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Switch between light and dark themes
                        </Typography>
                      </Box>
                    </Box>
                    <Switch
                      checked={isDarkMode}
                      onChange={handleToggleDarkMode}
                    />
                  </Stack>
                </Paper>

                <Alert severity="info">
                  Theme changes will be applied immediately across the application.
                </Alert>
              </Stack>
            </Box>
          </TabPanel>

          {/* Scheduler Tab */}
          <TabPanel value={tabValue} index={3}>
            <Box sx={{ px: 3 }}>
              <Stack spacing={3}>
                <Typography variant="h6">Scheduler Configuration</Typography>
                <Typography variant="body2" color="text.secondary">
                  Configure automatic notice downloading schedule
                </Typography>

                <FormControlLabel
                  control={
                    <Switch
                      checked={schedulerSettings.enabled}
                      onChange={handleSchedulerChange('enabled')}
                    />
                  }
                  label="Enable Automatic Scheduling"
                />

                {schedulerSettings.enabled && (
                  <>
                    <FormControl fullWidth>
                      <InputLabel>Frequency</InputLabel>
                      <Select
                        value={schedulerSettings.frequency}
                        label="Frequency"
                        onChange={handleSchedulerChange('frequency')}
                      >
                        <MenuItem value="once">Once a day</MenuItem>
                        <MenuItem value="twice">Twice a day</MenuItem>
                      </Select>
                    </FormControl>

                    {schedulerSettings.frequency === 'once' ? (
                      <TextField
                        fullWidth
                        label="Time"
                        type="time"
                        value={schedulerSettings.timeOnce}
                        onChange={handleSchedulerChange('timeOnce')}
                        InputLabelProps={{ shrink: true }}
                      />
                    ) : (
                      <Grid container spacing={2}>
                        <Grid item xs={6}>
                          <TextField
                            fullWidth
                            label="First Time"
                            type="time"
                            value={schedulerSettings.timeFirst}
                            onChange={handleSchedulerChange('timeFirst')}
                            InputLabelProps={{ shrink: true }}
                          />
                        </Grid>
                        <Grid item xs={6}>
                          <TextField
                            fullWidth
                            label="Second Time"
                            type="time"
                            value={schedulerSettings.timeSecond}
                            onChange={handleSchedulerChange('timeSecond')}
                            InputLabelProps={{ shrink: true }}
                          />
                        </Grid>
                      </Grid>
                    )}

                    <Alert severity="info">
                      The system will automatically download new notices at the scheduled time(s).
                    </Alert>
                  </>
                )}

                <Button
                  variant="contained"
                  startIcon={<SaveIcon />}
                  onClick={handleSaveSchedulerSettings}
                  sx={{ alignSelf: 'flex-start' }}
                >
                  Save Scheduler Settings
                </Button>
              </Stack>
            </Box>
          </TabPanel>

          {/* Account Tab */}
          <TabPanel value={tabValue} index={4}>
            <Box sx={{ px: 3 }}>
              <Stack spacing={3}>
                <Typography variant="h6">Account Management</Typography>
                <Typography variant="body2" color="text.secondary">
                  Manage your account and session settings
                </Typography>

                {/* Account Info */}
                <Paper sx={{ p: 3, border: '1px solid', borderColor: 'divider' }}>
                  <Stack spacing={2}>
                    <Typography variant="subtitle1" fontWeight="600">
                      Account Information
                    </Typography>
                    <Box>
                      <Typography variant="body2">
                        <strong>Name:</strong> {userSettings.name}
                      </Typography>
                      <Typography variant="body2">
                        <strong>Firm:</strong> {userSettings.firmName}
                      </Typography>
                      <Typography variant="body2">
                        <strong>Email:</strong> {userSettings.email}
                      </Typography>
                      <Typography variant="body2">
                        <strong>Username:</strong> {userSettings.username}
                      </Typography>
                    </Box>
                  </Stack>
                </Paper>

                {/* Logout Section */}
                <Paper sx={{
                  p: 3,
                  border: '1px solid',
                  borderColor: 'error.light',
                  backgroundColor: '#fff5f5'
                }}>
                  <Stack spacing={2}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <LogoutIcon sx={{ color: 'error.main' }} />
                      <Box>
                        <Typography variant="subtitle1" fontWeight="600" color="error.main">
                          Sign Out
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          End your current session and return to login
                        </Typography>
                      </Box>
                    </Box>

                    <Button
                      variant="outlined"
                      color="error"
                      startIcon={<LogoutIcon />}
                      onClick={handleLogoutClick}
                      sx={{
                        alignSelf: 'flex-start',
                        '&:hover': {
                          backgroundColor: 'error.main',
                          color: 'white'
                        }
                      }}
                    >
                      Logout
                    </Button>
                  </Stack>
                </Paper>

                <Alert severity="info">
                  You can also logout from the profile dropdown in the top navigation bar.
                </Alert>
              </Stack>
            </Box>
          </TabPanel>
        </CardContent>
      </Card>

      {/* Logout Confirmation Dialog */}
      <Dialog
        open={logoutConfirmOpen}
        onClose={() => setLogoutConfirmOpen(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: { borderRadius: '12px' }
        }}
      >
        <DialogTitle sx={{ textAlign: 'center' }}>
          <LogoutIcon sx={{ fontSize: 48, color: 'warning.main', mb: 1 }} />
          <Typography variant="h5" fontWeight="600">
            Confirm Logout
          </Typography>
        </DialogTitle>

        <DialogContent sx={{ textAlign: 'center' }}>
          <Typography variant="body1" color="text.secondary">
            Are you sure you want to logout?
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            You will need to login again to access the ERPCA Tax Engine dashboard.
          </Typography>
        </DialogContent>

        <DialogActions sx={{ p: 3, gap: 2, justifyContent: 'center' }}>
          <Button
            onClick={() => setLogoutConfirmOpen(false)}
            variant="outlined"
            sx={{ minWidth: 100 }}
          >
            Cancel
          </Button>
          <Button
            onClick={confirmLogout}
            variant="contained"
            color="error"
            startIcon={<LogoutIcon />}
            sx={{ minWidth: 100 }}
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SettingsPage;
