// GST API Service for integrating with official GST portal APIs

// Types for GST API responses
export interface GSTClientDetails {
  gstin: string;
  legalName: string;
  tradeName: string;
  registrationDate: string;
  constitutionOfBusiness: string;
  taxpayerType: string;
  gstinStatus: string;
  lastUpdatedDate: string;
  registrationAddress: {
    buildingName: string;
    streetName: string;
    location: string;
    pincode: string;
    district: string;
    state: string;
  };
  principalPlaceOfBusiness: {
    buildingName: string;
    streetName: string;
    location: string;
    pincode: string;
    district: string;
    state: string;
  };
  filingStatus: {
    gstr1: string;
    gstr3b: string;
    lastFilingDate: string;
  };
  businessActivities: string[];
  authorizedSignatories: Array<{
    name: string;
    designation: string;
  }>;
}

export interface GSTReturn {
  returnType: 'GSTR1' | 'GSTR3B' | 'GSTR9';
  period: string;
  filingDate: string;
  status: 'Filed' | 'Not Filed' | 'Late Filed';
  dueDate: string;
  arn: string;
}

export interface GSTNotice {
  noticeNumber: string;
  noticeType: string;
  issueDate: string;
  dueDate: string;
  status: 'Open' | 'Closed' | 'Under Review';
  description: string;
  amount?: number;
  section: string;
}

// API Configuration
const GST_API_BASE_URL = process.env.REACT_APP_GST_API_URL || 'https://api.gst.gov.in';
const API_KEY = process.env.REACT_APP_GST_API_KEY || 'your-api-key';
const API_TIMEOUT = parseInt(process.env.REACT_APP_API_TIMEOUT || '30000');

// Helper function to make API calls with fetch
const makeApiCall = async (endpoint: string, options: RequestInit = {}): Promise<any> => {
  const token = localStorage.getItem('gst-api-token');

  const defaultHeaders: HeadersInit = {
    'Content-Type': 'application/json',
    'X-API-Key': API_KEY,
  };

  if (token) {
    defaultHeaders.Authorization = `Bearer ${token}`;
  }

  const config: RequestInit = {
    ...options,
    headers: {
      ...defaultHeaders,
      ...options.headers,
    },
  };

  // Create AbortController for timeout
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), API_TIMEOUT);

  try {
    const response = await fetch(`${GST_API_BASE_URL}${endpoint}`, {
      ...config,
      signal: controller.signal,
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error: any) {
    clearTimeout(timeoutId);

    if (error.name === 'AbortError') {
      throw new Error('Request timeout');
    }

    console.error('GST API Error:', error);
    throw error;
  }
};

class GSTApiService {
  /**
   * Fetch client details from GST portal using GSTIN
   */
  async fetchClientDetails(gstin: string): Promise<GSTClientDetails> {
    try {
      // Validate GSTIN format
      if (!this.validateGSTIN(gstin)) {
        throw new Error('Invalid GSTIN format');
      }

      const data = await makeApiCall(`/taxpayer/search?gstin=${gstin}`);
      return this.transformClientDetails(data);
    } catch (error: any) {
      console.error('Error fetching client details:', error);

      // Return mock data for development/demo purposes
      if (process.env.NODE_ENV === 'development' || process.env.REACT_APP_USE_MOCK_DATA === 'true') {
        return this.getMockClientDetails(gstin);
      }

      throw new Error(`Failed to fetch client details: ${error.message}`);
    }
  }

  /**
   * Fetch GST returns for a client
   */
  async fetchGSTReturns(gstin: string, fromDate?: string, toDate?: string): Promise<GSTReturn[]> {
    try {
      let endpoint = `/returns/${gstin}`;
      const params = new URLSearchParams();

      if (fromDate) params.append('from_date', fromDate);
      if (toDate) params.append('to_date', toDate);

      if (params.toString()) {
        endpoint += `?${params.toString()}`;
      }

      const data = await makeApiCall(endpoint);
      return data.returns || [];
    } catch (error: any) {
      console.error('Error fetching GST returns:', error);

      // Return mock data for development
      if (process.env.NODE_ENV === 'development' || process.env.REACT_APP_USE_MOCK_DATA === 'true') {
        return this.getMockGSTReturns(gstin);
      }

      throw new Error(`Failed to fetch GST returns: ${error.message}`);
    }
  }

  /**
   * Fetch GST notices for a client
   */
  async fetchGSTNotices(gstin: string): Promise<GSTNotice[]> {
    try {
      const data = await makeApiCall(`/notices/${gstin}`);
      return data.notices || [];
    } catch (error: any) {
      console.error('Error fetching GST notices:', error);

      // Return mock data for development
      if (process.env.NODE_ENV === 'development' || process.env.REACT_APP_USE_MOCK_DATA === 'true') {
        return this.getMockGSTNotices(gstin);
      }

      throw new Error(`Failed to fetch GST notices: ${error.message}`);
    }
  }

  /**
   * Validate GSTIN format
   */
  validateGSTIN(gstin: string): boolean {
    const gstinRegex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;
    return gstinRegex.test(gstin);
  }

  /**
   * Transform API response to our client details format
   */
  private transformClientDetails(apiData: any): GSTClientDetails {
    return {
      gstin: apiData.gstin,
      legalName: apiData.legalName || apiData.legal_name,
      tradeName: apiData.tradeName || apiData.trade_name,
      registrationDate: apiData.registrationDate || apiData.registration_date,
      constitutionOfBusiness: apiData.constitutionOfBusiness || apiData.constitution_of_business,
      taxpayerType: apiData.taxpayerType || apiData.taxpayer_type,
      gstinStatus: apiData.gstinStatus || apiData.gstin_status,
      lastUpdatedDate: apiData.lastUpdatedDate || apiData.last_updated_date,
      registrationAddress: {
        buildingName: apiData.registrationAddress?.buildingName || '',
        streetName: apiData.registrationAddress?.streetName || '',
        location: apiData.registrationAddress?.location || '',
        pincode: apiData.registrationAddress?.pincode || '',
        district: apiData.registrationAddress?.district || '',
        state: apiData.registrationAddress?.state || '',
      },
      principalPlaceOfBusiness: {
        buildingName: apiData.principalPlaceOfBusiness?.buildingName || '',
        streetName: apiData.principalPlaceOfBusiness?.streetName || '',
        location: apiData.principalPlaceOfBusiness?.location || '',
        pincode: apiData.principalPlaceOfBusiness?.pincode || '',
        district: apiData.principalPlaceOfBusiness?.district || '',
        state: apiData.principalPlaceOfBusiness?.state || '',
      },
      filingStatus: {
        gstr1: apiData.filingStatus?.gstr1 || 'Not Available',
        gstr3b: apiData.filingStatus?.gstr3b || 'Not Available',
        lastFilingDate: apiData.filingStatus?.lastFilingDate || '',
      },
      businessActivities: apiData.businessActivities || [],
      authorizedSignatories: apiData.authorizedSignatories || [],
    };
  }

  /**
   * Mock client details for development/demo
   */
  private getMockClientDetails(gstin: string): GSTClientDetails {
    return {
      gstin: gstin,
      legalName: 'ABC ENTERPRISES PRIVATE LIMITED',
      tradeName: 'ABC Enterprises',
      registrationDate: '2018-07-01',
      constitutionOfBusiness: 'Private Limited Company',
      taxpayerType: 'Regular',
      gstinStatus: 'Active',
      lastUpdatedDate: '2024-01-15',
      registrationAddress: {
        buildingName: 'Tech Park Building',
        streetName: 'MG Road',
        location: 'Bangalore',
        pincode: '560001',
        district: 'Bangalore Urban',
        state: 'Karnataka',
      },
      principalPlaceOfBusiness: {
        buildingName: 'Tech Park Building',
        streetName: 'MG Road',
        location: 'Bangalore',
        pincode: '560001',
        district: 'Bangalore Urban',
        state: 'Karnataka',
      },
      filingStatus: {
        gstr1: 'Filed',
        gstr3b: 'Filed',
        lastFilingDate: '2024-01-20',
      },
      businessActivities: [
        'Software Development',
        'IT Consulting Services'
      ],
      authorizedSignatories: [
        {
          name: 'Rajesh Kumar',
          designation: 'Director'
        }
      ],
    };
  }

  /**
   * Mock GST returns for development
   */
  private getMockGSTReturns(gstin: string): GSTReturn[] {
    return [
      {
        returnType: 'GSTR3B',
        period: '122023',
        filingDate: '2024-01-20',
        status: 'Filed',
        dueDate: '2024-01-20',
        arn: 'AA240120123456789'
      },
      {
        returnType: 'GSTR1',
        period: '122023',
        filingDate: '2024-01-11',
        status: 'Filed',
        dueDate: '2024-01-11',
        arn: 'AA240111123456789'
      }
    ];
  }

  /**
   * Mock GST notices for development
   */
  private getMockGSTNotices(gstin: string): GSTNotice[] {
    return [
      {
        noticeNumber: 'GST-001-2024',
        noticeType: 'GSTR-3B Mismatch',
        issueDate: '2024-01-15',
        dueDate: '2024-02-15',
        status: 'Open',
        description: 'Discrepancy found in GSTR-3B filing for December 2023',
        amount: 25000,
        section: 'Section 61'
      },
      {
        noticeNumber: 'GST-002-2024',
        noticeType: 'Input Credit Mismatch',
        issueDate: '2024-02-01',
        dueDate: '2024-03-01',
        status: 'Open',
        description: 'ITC claimed more than eligible amount',
        amount: 15000,
        section: 'Section 16'
      }
    ];
  }
}

export const gstApiService = new GSTApiService();
export default gstApiService;
