// Whitebooks API Service for ERPCA Tax Engine
// Real API integration with https://api.whitebooks.in

// Types for Whitebooks API responses
export interface WhitebooksClientDetails {
  gstin: string;
  legalName: string;
  tradeName: string;
  businessName: string;
  email: string;
  registrationDate: string;
  gstinStatus: string;
  address: {
    buildingName?: string;
    streetName?: string;
    location?: string;
    pincode?: string;
    district?: string;
    state?: string;
  };
  authorizedSignatories?: Array<{
    name: string;
    designation: string;
  }>;
  filingStatus?: {
    gstr1?: string;
    gstr3b?: string;
    lastFilingDate?: string;
  };
  businessActivities?: string[];
}

// API Configuration
const WHITEBOOKS_API_BASE_URL = process.env.REACT_APP_ERPCA_API_URL || 'https://api.whitebooks.in';
const CLIENT_ID = process.env.REACT_APP_ERPCA_CLIENT_ID || 'GSTP761cfe60-b2b9-4590-8992-25537b2f71dc';
const CLIENT_SECRET = process.env.REACT_APP_ERPCA_CLIENT_SECRET || 'GSTP49dec62c-6415-47c1-9746-112764f40ab2';
const DEFAULT_EMAIL = process.env.REACT_APP_DEFAULT_EMAIL || '<EMAIL>';
const DEFAULT_GSTIN = process.env.REACT_APP_DEFAULT_GSTIN || '27AADCG2914R1Z7';
const API_TIMEOUT = 30000;
const USE_MOCK_DATA = process.env.REACT_APP_USE_MOCK_DATA === 'true';

class WhitebooksApiService {
  /**
   * Make API call to Whitebooks backend
   */
  private async makeWhitebooksApiCall(endpoint: string, params: Record<string, string> = {}): Promise<any> {
    // Headers as specified in your API
    const headers: HeadersInit = {
      'client_id': CLIENT_ID,
      'client_secret': CLIENT_SECRET,
      'Content-Type': 'application/json',
    };

    // Build URL with query parameters
    const url = new URL(`${WHITEBOOKS_API_BASE_URL}${endpoint}`);
    Object.entries(params).forEach(([key, value]) => {
      url.searchParams.append(key, value);
    });

    // Create AbortController for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), API_TIMEOUT);

    try {
      console.log('Making Whitebooks API call to:', url.toString());
      console.log('Headers:', headers);

      const response = await fetch(url.toString(), {
        method: 'GET',
        headers,
        mode: 'cors',
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      console.log('Whitebooks API Response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Whitebooks API Error Response:', errorText);
        throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      console.log('Whitebooks API Response data:', data);
      return data;
    } catch (error: any) {
      clearTimeout(timeoutId);
      
      if (error.name === 'AbortError') {
        throw new Error('Request timeout');
      }
      
      console.error('Whitebooks API Error:', error);
      throw error;
    }
  }

  /**
   * Search client details using email and GSTIN
   */
  async searchClientDetails(email: string = DEFAULT_EMAIL, gstin: string = DEFAULT_GSTIN): Promise<WhitebooksClientDetails> {
    console.log(`Searching Whitebooks API for email: ${email}, GSTIN: ${gstin}`);
    
    // If using mock data
    if (USE_MOCK_DATA) {
      console.log('Using mock data for Whitebooks API');
      return this.getMockClientDetails(email, gstin);
    }

    try {
      // Call the exact endpoint you provided
      const data = await this.makeWhitebooksApiCall('/public/search', {
        email: email,
        gstin: gstin
      });
      
      // Transform the API response to our format
      const clientDetails: WhitebooksClientDetails = this.transformApiResponse(data, email, gstin);
      
      console.log('Whitebooks client details fetched successfully:', clientDetails);
      return clientDetails;
    } catch (error: any) {
      console.error('Error fetching Whitebooks client details:', error);
      
      // Fallback to mock data
      console.log('Falling back to mock data due to API error');
      return this.getMockClientDetails(email, gstin);
    }
  }

  /**
   * Transform API response to our client details format
   */
  private transformApiResponse(data: any, email: string, gstin: string): WhitebooksClientDetails {
    // This will depend on the actual structure of your API response
    // For now, I'll create a flexible transformation
    return {
      gstin: data.gstin || gstin,
      legalName: data.legalName || data.legal_name || data.businessName || 'Unknown Business',
      tradeName: data.tradeName || data.trade_name || data.businessName || 'Unknown Business',
      businessName: data.businessName || data.business_name || data.legalName || 'Unknown Business',
      email: data.email || email,
      registrationDate: data.registrationDate || data.registration_date || new Date().toISOString(),
      gstinStatus: data.gstinStatus || data.gstin_status || data.status || 'Active',
      address: {
        buildingName: data.address?.buildingName || data.building_name || '',
        streetName: data.address?.streetName || data.street_name || '',
        location: data.address?.location || data.city || '',
        pincode: data.address?.pincode || data.pin_code || '',
        district: data.address?.district || '',
        state: data.address?.state || ''
      },
      authorizedSignatories: data.authorizedSignatories || data.authorized_signatories || [],
      filingStatus: data.filingStatus || data.filing_status || {},
      businessActivities: data.businessActivities || data.business_activities || []
    };
  }

  /**
   * Validate GSTIN format
   */
  validateGSTIN(gstin: string): boolean {
    const gstinRegex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;
    return gstinRegex.test(gstin);
  }

  /**
   * Mock client details for development/demo
   */
  private getMockClientDetails(email: string, gstin: string): Promise<WhitebooksClientDetails> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          gstin: gstin,
          legalName: 'ABC ENTERPRISES PRIVATE LIMITED',
          tradeName: 'ABC Enterprises',
          businessName: 'ABC Enterprises',
          email: email,
          registrationDate: '2018-07-01T00:00:00.000Z',
          gstinStatus: 'Active',
          address: {
            buildingName: 'Tech Park Building',
            streetName: 'MG Road',
            location: 'Bangalore',
            pincode: '560001',
            district: 'Bangalore Urban',
            state: 'Karnataka',
          },
          authorizedSignatories: [
            {
              name: 'Rajesh Kumar',
              designation: 'Director'
            }
          ],
          filingStatus: {
            gstr1: 'Filed',
            gstr3b: 'Filed',
            lastFilingDate: '2024-01-20',
          },
          businessActivities: [
            'Software Development',
            'IT Consulting Services'
          ]
        });
      }, 2000); // 2 second delay to simulate API call
    });
  }

  /**
   * Test API connection
   */
  async testConnection(email: string = DEFAULT_EMAIL, gstin: string = DEFAULT_GSTIN): Promise<{ success: boolean; message: string; data?: any }> {
    try {
      console.log('Testing Whitebooks API connection...');
      const data = await this.makeWhitebooksApiCall('/public/search', {
        email: email,
        gstin: gstin
      });
      
      return {
        success: true,
        message: 'API connection successful!',
        data: data
      };
    } catch (error: any) {
      return {
        success: false,
        message: `API connection failed: ${error.message}`
      };
    }
  }
}

export const whitebooksApiService = new WhitebooksApiService();
export default whitebooksApiService;
