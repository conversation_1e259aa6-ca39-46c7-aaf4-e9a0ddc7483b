import React, { useState } from 'react';
import { Box, Button, Typography, Paper, Stack, Alert } from '@mui/material';
import { apiService, transformDashboardStats } from '../../services/api';

const ApiTest: React.FC = () => {
  const [backendData, setBackendData] = useState<any>(null);
  const [transformedData, setTransformedData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testApi = async () => {
    try {
      setLoading(true);
      setError(null);
      
      console.log('🧪 Testing API transformation...');
      
      // Fetch raw backend data
      const rawData = await apiService.getDashboardStats();
      console.log('📥 Raw backend data:', rawData);
      setBackendData(rawData);
      
      // Transform the data
      const transformed = transformDashboardStats(rawData);
      console.log('🔄 Transformed data:', transformed);
      setTransformedData(transformed);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'API test failed');
      console.error('❌ API test error:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={{ p: 3, maxWidth: 800 }}>
      <Typography variant="h5" gutterBottom>
        API Integration Test
      </Typography>
      
      <Button 
        variant="contained" 
        onClick={testApi} 
        disabled={loading}
        sx={{ mb: 3 }}
      >
        {loading ? 'Testing...' : 'Test API Transformation'}
      </Button>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Stack spacing={3}>
        {backendData && (
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Raw Backend Data (snake_case):
            </Typography>
            <pre style={{ fontSize: '12px', overflow: 'auto' }}>
              {JSON.stringify(backendData, null, 2)}
            </pre>
          </Paper>
        )}

        {transformedData && (
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Transformed Frontend Data (camelCase):
            </Typography>
            <pre style={{ fontSize: '12px', overflow: 'auto' }}>
              {JSON.stringify(transformedData, null, 2)}
            </pre>
          </Paper>
        )}

        {transformedData && (
          <Paper sx={{ p: 2, bgcolor: 'success.light' }}>
            <Typography variant="h6" gutterBottom>
              Dashboard Display Values:
            </Typography>
            <Stack spacing={1}>
              <Typography>Total Clients: {transformedData.totalClients}</Typography>
              <Typography>Notices Today: {transformedData.noticesToday}</Typography>
              <Typography>Replies Sent: {transformedData.repliesSent}</Typography>
              <Typography>Invoices Downloaded: {transformedData.invoicesDownloaded}</Typography>
            </Stack>
          </Paper>
        )}
      </Stack>
    </Box>
  );
};

export default ApiTest;
