"""
Notice management endpoints
"""
from typing import List, Optional
from fastapi import API<PERSON><PERSON>er, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc
from ....core.database import get_db
from ....models import Notice, Client, User, NoticeStatus
from ....schemas.notice import (
    Notice as NoticeSchema,
    NoticeUpdate,
    NoticeFilter,
    NoticeStats,
    NoticeListResponse,
    NoticeBulkUpdate
)
from ..endpoints.auth import get_current_active_user
import logging

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/", response_model=NoticeListResponse)
async def get_notices(
    client_id: Optional[int] = None,
    status: Optional[NoticeStatus] = None,
    search: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get notices with filtering"""
    # Base query - only notices for user's clients
    query = db.query(Notice).join(Client).filter(Client.user_id == current_user.id)
    
    # Apply filters
    if client_id:
        query = query.filter(Notice.client_id == client_id)
    
    if status:
        query = query.filter(Notice.status == status)
    
    if search:
        search_filter = or_(
            Notice.subject.ilike(f"%{search}%"),
            Notice.notice_id.ilike(f"%{search}%"),
            Notice.description.ilike(f"%{search}%")
        )
        query = query.filter(search_filter)
    
    # Get total count
    total = query.count()
    
    # Get paginated results
    notices = query.order_by(desc(Notice.notice_date)).offset(skip).limit(limit).all()
    
    return NoticeListResponse(
        success=True,
        message="Notices retrieved successfully",
        data=notices,
        total=total,
        limit=limit,
        offset=skip,
        has_more=skip + limit < total
    )


@router.get("/stats", response_model=NoticeStats)
async def get_notice_stats(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get notice statistics"""
    from datetime import datetime, timedelta
    
    # Base query for user's notices
    base_query = db.query(Notice).join(Client).filter(Client.user_id == current_user.id)
    
    total_notices = base_query.count()
    new_notices = base_query.filter(Notice.status == NoticeStatus.NEW).count()
    reviewed_notices = base_query.filter(Notice.status == NoticeStatus.REVIEWED).count()
    replied_notices = base_query.filter(Notice.status == NoticeStatus.REPLIED).count()
    pending_notices = base_query.filter(Notice.status == NoticeStatus.PENDING).count()
    closed_notices = base_query.filter(Notice.status == NoticeStatus.CLOSED).count()
    
    # Overdue notices
    today = datetime.utcnow()
    overdue_notices = base_query.filter(
        and_(Notice.due_date < today, Notice.status != NoticeStatus.CLOSED)
    ).count()
    
    # Notices received today
    today_start = today.replace(hour=0, minute=0, second=0, microsecond=0)
    notices_today = base_query.filter(Notice.received_date >= today_start).count()
    
    return NoticeStats(
        total_notices=total_notices,
        new_notices=new_notices,
        reviewed_notices=reviewed_notices,
        replied_notices=replied_notices,
        pending_notices=pending_notices,
        closed_notices=closed_notices,
        overdue_notices=overdue_notices,
        notices_today=notices_today
    )


@router.get("/{notice_id}", response_model=NoticeSchema)
async def get_notice(
    notice_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get specific notice"""
    notice = db.query(Notice).join(Client).filter(
        and_(Notice.id == notice_id, Client.user_id == current_user.id)
    ).first()
    
    if not notice:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Notice not found"
        )
    
    return notice


@router.put("/{notice_id}", response_model=NoticeSchema)
async def update_notice(
    notice_id: int,
    notice_data: NoticeUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update notice"""
    notice = db.query(Notice).join(Client).filter(
        and_(Notice.id == notice_id, Client.user_id == current_user.id)
    ).first()
    
    if not notice:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Notice not found"
        )
    
    # Update fields
    update_data = notice_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(notice, field, value)
    
    db.commit()
    db.refresh(notice)
    
    logger.info(f"Notice updated: {notice.notice_id} by user {current_user.email}")
    return notice


@router.put("/bulk-update")
async def bulk_update_notices(
    update_data: NoticeBulkUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Bulk update notices"""
    # Verify all notices belong to user
    notices = db.query(Notice).join(Client).filter(
        and_(Notice.id.in_(update_data.notice_ids), Client.user_id == current_user.id)
    ).all()
    
    if len(notices) != len(update_data.notice_ids):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Some notices not found"
        )
    
    # Update notices
    updated_count = 0
    for notice in notices:
        if update_data.status:
            notice.status = update_data.status
        if update_data.priority:
            notice.priority = update_data.priority
        updated_count += 1
    
    db.commit()
    
    logger.info(f"Bulk updated {updated_count} notices by user {current_user.email}")
    return {"message": f"Successfully updated {updated_count} notices"}


@router.delete("/{notice_id}")
async def delete_notice(
    notice_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete notice"""
    notice = db.query(Notice).join(Client).filter(
        and_(Notice.id == notice_id, Client.user_id == current_user.id)
    ).first()
    
    if not notice:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Notice not found"
        )
    
    # Soft delete
    notice.is_active = False
    db.commit()
    
    logger.info(f"Notice deleted: {notice.notice_id} by user {current_user.email}")
    return {"message": "Notice deleted successfully"}
