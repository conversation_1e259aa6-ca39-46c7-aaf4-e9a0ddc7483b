import React from 'react';
import {
  Card,
  CardContent,
  Stack,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Button
} from '@mui/material';
import ClearIcon from '@mui/icons-material/Clear';
import { NoticeFilters as INoticeFilters } from '../../types/schema';
import { NoticeStatus } from '../../types/enums';

interface NoticeFiltersProps {
  filters: INoticeFilters;
  onFilterChange: (filters: INoticeFilters) => void;
}

const NoticeFilters: React.FC<NoticeFiltersProps> = ({ filters, onFilterChange }) => {
  const handleStatusChange = (status: string) => {
    onFilterChange({
      ...filters,
      status: status ? (status as NoticeStatus) : undefined
    });
  };

  const handleGstinChange = (gstin: string) => {
    onFilterChange({
      ...filters,
      gstin: gstin || undefined
    });
  };

  const handleClearFilters = () => {
    onFilterChange({});
  };

  return (
    <Card>
      <CardContent>
        <Stack direction={{ xs: 'column', md: 'row' }} spacing={2} alignItems="center">
          <TextField
            label="Search by GSTIN"
            value={filters.gstin || ''}
            onChange={(e) => handleGstinChange(e.target.value)}
            placeholder="Enter GSTIN..."
            sx={{ minWidth: 200 }}
          />

          <FormControl sx={{ minWidth: 150 }}>
            <InputLabel>Status</InputLabel>
            <Select
              value={filters.status || ''}
              onChange={(e) => handleStatusChange(e.target.value)}
              label="Status"
            >
              <MenuItem value="">All Status</MenuItem>
              <MenuItem value={NoticeStatus.NEW}>New</MenuItem>
              <MenuItem value={NoticeStatus.PENDING}>Pending</MenuItem>
              <MenuItem value={NoticeStatus.REPLIED}>Replied</MenuItem>
            </Select>
          </FormControl>

          <TextField
            label="From Date"
            type="date"
            value={filters.dateRange?.start ? filters.dateRange.start.toISOString().split('T')[0] : ''}
            onChange={(e) => {
              const date = e.target.value ? new Date(e.target.value) : undefined;
              onFilterChange({
                ...filters,
                dateRange: date ? { ...filters.dateRange, start: date } : undefined
              });
            }}
            InputLabelProps={{ shrink: true }}
            sx={{ minWidth: 150 }}
          />

          <TextField
            label="To Date"
            type="date"
            value={filters.dateRange?.end ? filters.dateRange.end.toISOString().split('T')[0] : ''}
            onChange={(e) => {
              const date = e.target.value ? new Date(e.target.value) : undefined;
              onFilterChange({
                ...filters,
                dateRange: date ? { ...filters.dateRange, end: date } : undefined
              });
            }}
            InputLabelProps={{ shrink: true }}
            sx={{ minWidth: 150 }}
          />

          <Button
            variant="outlined"
            startIcon={<ClearIcon />}
            onClick={handleClearFilters}
          >
            Clear
          </Button>
        </Stack>
      </CardContent>
    </Card>
  );
};

export default NoticeFilters;